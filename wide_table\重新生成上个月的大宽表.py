import importlib
import os
import sys
from datetime import datetime, timedelta

from yyutils import *

scriptpath = "./"
sys.path.append(os.path.abspath(scriptpath))


def from_A_to_B(start, end):
    start_day = datetime.strptime(start, '%Y-%m-%d')
    if not end:
        end = start
    end_day = datetime.strptime(end, '%Y-%m-%d')
    n = 0
    while True:
        one = start_day + timedelta(days=n)
        if one <= end_day:
            n += 1
            yield str(one.strftime('%Y-%m-%d'))
        else:
            break


from WideTableReportGenerator import do_create_recent_report_wide

start, end, days = TimeUtils.getLastMonthRange()
# start = "2023-07-16",
# end = "2023-07-16"
for report_day in from_A_to_B(start, end):
    do_create_recent_report_wide(report_day)
