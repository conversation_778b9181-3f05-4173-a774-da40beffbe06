from yyutils import *
from utils.YYSQLTemplate import YY<PERSON><PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT,orderByCityVT
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    xjg_sql = f'''
                    SELECT 
                        count(distinct vehicle_no,vehicle_color) xjg_count,
                        toUInt8(vehicle_type) vehicle_type,
                        toUInt32(owner_id) owner_id
                    from gdispx_data.vehicle_history_yunzheng vhy 
                    where create_date BETWEEN '{start_day}'  and '{end_day}'
                    group by owner_id,vehicle_type
        '''
    jg_vt_sql1 = f'''
                    select 
                        owner_id ,
                        any(owner_name) owner_name,
                        vehicle_no ,vehicle_color,
                        vehicle_type,
                        any(area_manger_short_name) area_manger_short_name,
                        if(max(exception_type)=2 ,1,0) has_exception
                    from gdispx_data.v_vehicle_wide_day_all vvwda 
                    where inc_day BETWEEN '{start_day}'  and '{end_day}'
                    group by owner_id ,vehicle_no ,vehicle_color ,vehicle_type
                '''
    jg_vt_sql2 = SQLHelper().table(jg_vt_sql1, 'D') \
        .groupBy('owner_id,vehicle_type') \
        .select(
        '''
            owner_id ,
            any(owner_name) owner_name,
            vehicle_type,
            any(area_manger_short_name) area_manger_short_name,
            count(1) jg_count,
            sum(has_exception) has_exception_count
        '''
    )
    final_sql = SQLHelper().table(jg_vt_sql2, 'A') \
        .leftJoin().table(xjg_sql, 'B') \
        .on(' A.owner_id = B.owner_id and A.vehicle_type = B.vehicle_type ') \
        .where('A.has_exception_count>0') \
        .select(
        '''
        A.owner_id owner_id,
        owner_name,
        splitByChar(',',area_manger_short_name)[2] city_name,
        splitByChar(',',area_manger_short_name)[3] area_name,
        vehicle_type,
        dictGetString('gdispx_data.sys_dict_item','label',(toString(A.vehicle_type),'vehicle_type')) vehicle_type_name,
        xjg_count,
        jg_count,
        has_exception_count,
        FLOOR(has_exception_count/jg_count,4) ex_rate
        '''
    )
    return final_sql


def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        ne = OrderedDict(zip(
            ('city_name','area_name',
             'owner_name', 'vehicle_type',
             'xjg_count', 'jg_count',
             'has_exception_count','ex_rate'
             ),
            (e['city_name'], e['area_name'],
             e['owner_name'], e['vehicle_type_name'],
             e['xjg_count'], e['jg_count'],
             e['has_exception_count'],
             FLOORtoPercent(e['ex_rate'])
             )
        ))
        outlist.append(ne)
    return outlist


def getTop10(inlist):
    outlist = []
    n_g = list_2_dictlist(inlist, 'city_name')
    for g in n_g.values():
        vt_sg = list_2_dictlist(g,'vehicle_type')
        for vt,sg in vt_sg.items():
            sg.sort(key=lambda o: - o['ex_rate'])
            if len(sg) > 10:
                tmplist = list(filter(lambda o: o['ex_rate'] >= sg[9]['ex_rate'], sg))
            else:
                tmplist = sg[:10]
            outlist.extend(tmplist)
    return outlist


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = getTop10(datalist)
    outlist = orderByCityVT(outlist)
    outlist = export_outlist_orderDict(outlist)
    h = list(outlist[0].keys())
    otfile = join_path(bpath, 'exception_sheet2.csv')
    write_csv(otfile, h, outlist)
    return outlist

if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
