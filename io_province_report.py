from yyutils import *
import sys, os

'''
出入省报表统计

入参不设置，则取前一天的数据进行统计
入参设置，统计指定日期的数据
不会统计一段时间的情况
'''
bpath = r'/data/report_data/出入省'


def getReportDate():
    if len(sys.argv) == 2:
        day = sys.argv[1]
    else:
        day = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    return day


key_zone = {
    '广西','河南','海南','内蒙古','西藏','福建','浙江'
}


@countTime("开始下载gps记录..")
def download_gps_hour_data_by_day(otpath, report_day):
    sql = f'''
        SELECT
            vehicle_no,
            transform(vehicle_color, ['1','2','93','94'], ['蓝色','黄色','黄绿双拼色','渐变绿色']) vehicle_color,
            toDate(gps_time_hour) report_date,
            gps_time_hour,
            province_name,
            city_name,
            area_name,
            transform(vehicle_type, ['1','3','4'], ['客运车辆','危险货运','重型货车']) vehicle_type,
            vehicle_area_name,
            vehicle_city_name,
            owner_name
        from gdispx_data.statistics_last_gps_hour_all
        where
	        toDate(gps_time_hour) = '{report_day}'
	        and 
	         vehicle_no in (
	            SELECT vehicle_no from
		        (
		            SELECT vehicle_no , COUNT(vehicle_no) as passProvice from
			        (
			            SELECT dict.1 as vehicle_no, dict.2 as vehicle_color, dict.3 as provice from
				        (
				            SELECT DISTINCT (vehicle_no, vehicle_color, province_name) as dict 
				            from gdispx_data.statistics_last_gps_hour_all
				            where
					            toDate(gps_time_hour) = '{report_day}'
                                order by vehicle_no , gps_time_hour 
                        ) 
                    )
		            group by vehicle_no HAVING passProvice>1
		        ) 
		        union All
		        select DISTINCT vehicle_no from gdispx_data.statistics_last_gps_hour_all
		        where province_name in ( { ",".join(["'" + z + "'" for z in key_zone]) } )
		        and toDate(gps_time_hour) = '{report_day}'
		    )
        order by vehicle_no, gps_time_hour
    '''
    for i in range(10):
        try:
            db = DBUtil(DBType.CLICKHOUSE.value, DBInfo.CK_PROD_204.value)
            outlist = db.query_to_dictList(sql)
            db.close()
            h = ['vehicle_no', 'vehicle_color','report_date' ,'gps_time_hour',
                 'province_name','city_name','area_name', 'vehicle_type',
                 'vehicle_area_name','vehicle_city_name','owner_name']
            otfile = join_path(otpath, f'原始轨迹位置数据.csv')
            write_csv(otfile, h, outlist)
            Logger.instance().info(f"数据第{i + 1}次下载成功！")
            break
        except Exception as ex:
            Logger.instance().info(f"数据第{i+1}次下载失败,重试中...")
            continue


col_mapper = {
    'report_date': '时间',
    'province_name': '所在省份',
    'city_name': '所在城市',
    'area_name': '所在区县',
    'vehicle_no': '车牌号',
    'vehicle_color': '车牌颜色',
    'vehicle_type': '经营范围',
    'vehicle_city_name': '车辆所属城市',
    'vehicle_area_name': '车辆所属区县',
    'owner_name': '车辆所属业户',
}


def export_distinct(inlist):
    outlist = []
    for g in read_a_group(inlist, '车牌号'):
        outlist.append(g[0])
    return outlist


def download_gps_hour_data_by_day_for_key_zone(otpath, report_day):
    zone_strs = ','.join([f"'{p}'" for p in key_zone])
    sql = f"""
    SELECT 
        DISTINCT toDate(gps_time_hour) report_date,
        area_name,
        city_name,
        province_name,
        vehicle_no,
        transform(vehicle_color, ['1','2','93','94'], ['蓝色','黄色','黄绿双拼色','渐变绿色']) vehicle_color,
        transform(vehicle_type, ['1','3','4'], ['客运车辆','危险货运','重型货车']) vehicle_type,
        vehicle_area_name,
        vehicle_city_name,
        owner_name
    from gdispx_data.statistics_last_gps_hour_all slgha 
    where province_name in ({zone_strs})
    and toDate(gps_time_hour) = '{report_day}'
    order by vehicle_no, gps_time_hour
    """

    for i in range(10):
        try:
            db = DBUtil(DBType.CLICKHOUSE.value, DBInfo.CK_BETA.value)
            outlist = db.query_to_dictList(sql)
            db.close()
            h = ['时间', '所在省份', '所在城市', '所在区县', '车牌号', '车牌颜色', '经营范围', '车辆所属城市', '车辆所属区县', '车辆所属业户']
            outlist1 = []
            for e in outlist:
                ne = {}
                for k, v in e.items():
                    ne[col_mapper.get(k)] = v
                outlist1.append(ne)
            outlist1 = export_distinct(outlist1)
            otfile = join_path(otpath, f'{report_day}_途径敏感省份的车辆明细.csv')
            write_csv(otfile, h, outlist1)
            break
        except Exception as ex:
            continue


def download_jianguan_data_by_day(otpath):
    sql = f'''
        select vehicle_no,vehicle_color,vehicle_type,manger_short_name
        from gdispx_data.vehicle_related_infos 
    '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    outist = db.query_to_dictList(sql)
    db.close()
    otfile = join_path(otpath, 'vehicles.csv')
    h = ['vehicle_no', 'vehicle_color', 'vehicle_type', 'manger_short_name']
    write_csv(otfile, h, outist)
    Logger.instance().info("监管车辆下载成功!")


def getTop1(sg):
    p_f = {}
    for e in sg:
        if not e['province_name']:
            continue
        p_f.setdefault(e['province_name'],0)
        p_f[e['province_name']] += 1
    if not p_f:
        return None
    p_f_list = list(p_f.items())
    p_f_list.sort(key=lambda o:-o[1])
    p = p_f_list[0][0]
    return list(filter(lambda o:o['province_name'] == p,sg))[0]

def getCompressedG(g):
    outlist = []
    for sg in read_a_group(g, 'gps_time_hour'):
        top1 = getTop1(sg)
        if not top1:
            continue
        outlist.append(top1)
    return outlist


def check_is_io_province(g):
    tlist = list(filter(lambda o: o['province_name'] == '广东', g))
    if not tlist or len(tlist) == len(g):
        return False
    return True


def export_io_info_by_vehicle(g):
    if g[0]['province_name'] == '广东' and g[-1]['province_name'] != '广东':
        return {
            '当日出入省车辆': g[0]['vehicle_no'],
            '出入类型': '出省',
            '当日起始': g[0]['province_name'],
            '当日终点': g[-1]['province_name'],
        }
    if g[0]['province_name'] != '广东' and g[-1]['province_name'] == '广东':
        return {
            '当日出入省车辆': g[0]['vehicle_no'],
            '出入类型': '入省',
            '当日终点': g[-1]['province_name'],
            '当日起始': g[0]['province_name'],
        }
    if g[0]['province_name'] != '广东' and g[-1]['province_name'] != '广东':
        return {
            '当日出入省车辆': g[0]['vehicle_no'],
            '出入类型': '入省又出省',
            '当日起始': g[0]['province_name'],
            '当日终点': g[-1]['province_name'],
            '中间停留': '广东',
        }
    if g[0]['province_name'] == '广东' and g[-1]['province_name'] == '广东':
        tlist = list(filter(lambda o: o['province_name'] != '广东', g))
        if not tlist:
            return {}
        return {
            '当日出入省车辆': g[0]['vehicle_no'],
            '出入类型': '出省又入省',
            '当日终点': g[-1]['province_name'],
            '当日起始': g[0]['province_name'],
            '中间停留': tlist[0]['province_name'],
        }
    return {}

def check_is_key_zone(g):
    tlist = list(filter(lambda o:o['province_name'] in key_zone,g))
    if tlist:
        ne = {}
        ne['时间'] = tlist[0]['report_date']
        ne['所在省份'] = tlist[0]['province_name']
        ne['所在城市'] = tlist[0]['city_name']
        ne['所在区县'] = tlist[0]['area_name']
        ne['车牌号'] = tlist[0]['vehicle_no']
        ne['车牌颜色'] = tlist[0]['vehicle_color']
        ne['经营范围'] = tlist[0]['vehicle_type']
        ne['车辆所属城市'] = tlist[0]['vehicle_city_name']
        ne['车辆所属区县'] = tlist[0]['vehicle_area_name']
        ne['车辆所属业户'] = tlist[0]['owner_name']
        ne['最后停留省份'] = g[-1]['province_name']
        ne['最后停留城市'] = g[-1]['city_name']
        ne['最后停留区县'] = g[-1]['area_name']
        return ne
    return None

@countTime("清洗原始数据")
def export_mid_data(bpath,report_day):
    infile = join_path(bpath, '原始轨迹位置数据.csv')
    otfile2 = join_path(bpath,f'{report_day}_途径敏感省份的车辆明细.csv')
    h2 = ['时间', '所在省份', '所在城市', '所在区县', '车牌号', '车牌颜色',
          '经营范围', '车辆所属城市', '车辆所属区县', '车辆所属业户','最后停留省份','最后停留城市','最后停留区县']
    outlist2 = []
    outlist = []
    for g in read_a_group(readCsv(infile, print_count=100000), 'vehicle_no'):
        g = getCompressedG(g)
        e2 = check_is_key_zone(g)
        if e2:
            outlist2.append(e2)
        is_io = check_is_io_province(g)
        if not is_io:
            continue
        r = export_io_info_by_vehicle(g)
        if not r:
            print('error')
            continue
        outlist.append(r)
    write_csv(otfile2,h2,outlist2)
    h = ['当日出入省车辆', '出入类型', '当日起始', '中间停留', '当日终点']
    otfile = join_path(bpath, 'mid.csv')
    write_csv(otfile, h, outlist)


vt_dict = {
    '1': '客',
    '2': '客',
    '3': '危',
    '4': '重',
}

@countTime("开始导出出入省数据")
def export_io_report(bpath, report_day):
    infile = join_path(bpath, 'mid.csv')
    inlist = read_csv(infile)
    vns = set([e['当日出入省车辆'] for e in inlist])
    infile0 = join_path(bpath, 'vehicles.csv')
    tlist1 = list(filter(lambda o: o['vehicle_no'] in vns, readCsv(infile0, print_count=0)))
    v_e = list_2_dict(tlist1, 'vehicle_no')
    outlist = []
    for e in inlist:
        if not v_e.get(e['当日出入省车辆']):
            print(e['当日出入省车辆'], '未被监管')
            continue
        ne = {}
        ne['车辆'] = e['当日出入省车辆']
        ne['所属地市'] = v_e.get(e['当日出入省车辆'])['manger_short_name'].split(',')[1] if v_e.get(e['当日出入省车辆']) else ''
        ne['所属区县'] = v_e.get(e['当日出入省车辆'])['manger_short_name'].split(',')[-1] if v_e.get(e['当日出入省车辆']) else ''
        ne['经营范围'] = vt_dict.get(v_e.get(e['当日出入省车辆'])['vehicle_type']) if v_e.get(e['当日出入省车辆']) else ''
        ne['日期'] = report_day
        ne['出入类型'] = e['出入类型']
        ne['当日起始'] = e['当日起始']
        ne['中间停留'] = e['中间停留']
        ne['当日终点'] = e['当日终点']
        outlist.append(ne)
    h = ['车辆', '所属地市', '所属区县', '经营范围', '日期', '出入类型', '当日起始', '中间停留', '当日终点']
    otfile = join_path(bpath, f'{report_day}_出入省车辆详情.csv')
    write_csv(otfile, h, outlist)

@countTime("开始统计出入省指标")
def do_stats(bpath, report_day):
    infile = join_path(bpath, f'{report_day}_出入省车辆详情.csv')
    otfile1 = join_path(bpath, f'{report_day}_出入省总量统计.csv')
    otfile2 = join_path(bpath, f'{report_day}_离粤重型货车统计.csv')
    inlist = read_csv(infile)
    n1, n2 = 0, 0
    for e in inlist:
        if e['出入类型'] not in ('入省', '出省'):
            n1 += 1
            n2 += 1
            continue
        if e['出入类型'] == '入省':
            n1 += 1
        else:
            n2 += 1
    se = {
        '日期': '2022-04-25',
        '入省车辆数': n1,
        '出省车辆数': n2
    }
    write_csv(otfile1, ['日期', '入省车辆数', '出省车辆数'], [se])
    print(n1, n2)
    p_freq = {}
    for e in inlist:
        if e['经营范围'] == '重' and e['出入类型'] == '出省':
            p_freq.setdefault(e['当日终点'], 0)
            p_freq[e['当日终点']] += 1
            continue
        if e['经营范围'] == '重' and e['出入类型'] == '入省又出省':
            p_freq.setdefault(e['当日终点'], 0)
            p_freq[e['当日终点']] += 1
            continue
        if e['经营范围'] == '重' and e['出入类型'] == '出省又入省':
            p_freq.setdefault(e['中间停留'], 0)
            p_freq[e['中间停留']] += 1
            continue
    outlist = []
    for p, freq in p_freq.items():
        outlist.append({
            'p': p,
            'freq': freq
        })
    outlist.sort(key=lambda o: -o['freq'])
    write_csv(otfile2, ['p', 'freq'], outlist)

@countTime("导出")
def mvfiles(otpath, report_day):
    cur_path = r'/data/track_report/'
    infile1 = join_path(otpath, f'{report_day}_出入省车辆详情.csv')
    infile2 = join_path(otpath, f'{report_day}_出入省总量统计.csv')
    infile3 = join_path(otpath, f'{report_day}_离粤重型货车统计.csv')
    otfile1 = join_path(cur_path, f'{report_day}_出入省车辆详情.csv')
    otfile2 = join_path(cur_path, f'{report_day}_出入省总量统计.csv')
    otfile3 = join_path(cur_path, f'{report_day}_离粤重型货车统计.csv')
    movefile(infile1, otfile1)
    movefile(infile2, otfile2)
    movefile(infile3, otfile3)
    infile4 = join_path(otpath, f'{report_day}_途径敏感省份的车辆明细.csv')
    otfile4 = join_path(cur_path, f'{report_day}_途径敏感省份的车辆明细.csv')
    movefile(infile4, otfile4)
    # infile5 = join_path(otpath, f'{report_day}_ETC_CKTS_NEW.csv')
    # infile6 = join_path(otpath, f'{report_day}_ETC_MJTX_NEW.csv')
    # infile7 = join_path(otpath, f'{report_day}_ETC_RKTX_NEW.csv')
    # otfile5 = join_path(cur_path, f'{report_day}_ETC_CKTS_NEW.csv')
    # otfile6 = join_path(cur_path, f'{report_day}_ETC_MJTX_NEW.csv')
    # otfile7 = join_path(cur_path, f'{report_day}_ETC_RKTX_NEW.csv')
    # movefile(infile5, otfile5)
    # movefile(infile6, otfile6)
    # movefile(infile7, otfile7)

@countTime("开始下载ETC记录")
def export_ETC_DATA(otpath, report_day):
    db = DBUtil(DBType.CLICKHOUSE.value, DBInfo.CK_PROD_204.value)
    sql1 = f'''
        SELECT * from gdispx_data.ETC_CKTS_NEW where toDate(g6) = '{report_day}';
    '''
    sql2 = f'''
        SELECT * from gdispx_data.ETC_MJTX_NEW where toDate(g8) = '{report_day}';
    '''
    sql3 = f'''
        SELECT * from gdispx_data.ETC_RKTX_NEW where toDate(g5) = '{report_day}';
    '''
    otfile1 = join_path(otpath, f'{report_day}_ETC_CKTS_NEW.csv')
    otfile2 = join_path(otpath, f'{report_day}_ETC_MJTX_NEW.csv')
    otfile3 = join_path(otpath, f'{report_day}_ETC_RKTX_NEW.csv')
    db.query_to_csv(sql1, otfile1)
    db.query_to_csv(sql2, otfile2)
    db.query_to_csv(sql3, otfile3)
    db.close()


def main(report_day=None):
    Logger.instance("出入省统计",join_path(bpath,'log.log'))
    try:
        if not report_day:
            report_day = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
        otpath = createOutpath(bpath, report_day)
        download_gps_hour_data_by_day(otpath, report_day)
        download_jianguan_data_by_day(otpath)
        export_mid_data(otpath,report_day)
        export_io_report(otpath, report_day)
        do_stats(otpath, report_day)
        # export_ETC_DATA(otpath, report_day)
        mvfiles(otpath, report_day)
    except Exception as ex:
        Logger.instance().info(ex)


if __name__ == '__main__':
    report_day = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    main(report_day)
