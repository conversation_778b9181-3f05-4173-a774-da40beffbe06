import streamlit as st
import pandas as pd
import altair as alt

# --- 页面配置和标题 ---
st.set_page_config(
    page_title="交互式图表 | Palmer Penguins",
    page_icon="🐧",
    layout="wide"
)

st.title("🐧 Palmer Penguins 数据集探索")
st.write(
    "这是一个交互式图表示例。你可以使用侧边栏的控件来筛选数据，"
    "图表将会实时更新。"
)

# --- 加载数据 ---
# 使用 Streamlit 的缓存功能来提高性能，避免每次交互都重新加载数据
@st.cache_data
def load_data():
    # 使用在线 URL 加载数据，无需本地文件
    csv_url = "https://raw.githubusercontent.com/allisonhorst/palmerpenguins/main/inst/extdata/penguins.csv"
    df = pd.read_csv(csv_url)
    return df

penguins_df = load_data()

# --- 侧边栏交互控件 ---
with st.sidebar:
    st.header("📊 筛选控件")

    # 1. 物种选择 (多选)
    # 获取所有唯一的物种，并设置为默认全选
    species_list = list(penguins_df["species"].unique())
    selected_species = st.multiselect(
        "选择企鹅种类:",
        options=species_list,
        default=species_list
    )

    # 2. 体重选择 (滑块)
    # 获取体重的最大和最小值
    min_weight = int(penguins_df["body_mass_g"].min())
    max_weight = int(penguins_df["body_mass_g"].max())

    # 创建一个范围滑块
    selected_weight_range = st.slider(
        "选择体重范围 (克):",
        min_value=min_weight,
        max_value=max_weight,
        value=(min_weight, max_weight) # 默认选择整个范围
    )


# --- 数据筛选 ---
# 根据侧边栏的选择来过滤 DataFrame
# 1. 按物种筛选
filtered_df = penguins_df[penguins_df["species"].isin(selected_species)]

# 2. 按体重范围筛选
filtered_df = filtered_df[
    (filtered_df["body_mass_g"] >= selected_weight_range[0]) &
    (filtered_df["body_mass_g"] <= selected_weight_range[1])
    ]


# --- 主页面内容 ---
st.subheader("🐧 筛选后的企鹅数据")

# 显示一个指标，告诉用户还剩多少数据
st.metric(
    label="数据点数量",
    value=filtered_df.shape[0],
    help=f"总共有 {penguins_df.shape[0]} 条数据"
)

# --- 创建图表 ---
# 使用 Altair 创建一个交互式散点图
scatter_plot = alt.Chart(filtered_df).mark_circle(size=80, opacity=0.8).encode(
    # X 轴: 鳍的长度
    x=alt.X('flipper_length_mm:Q',
            title='鳍的长度 (mm)',
            scale=alt.Scale(zero=False)
            ),
    # Y 轴: 喙的长度
    y=alt.Y('bill_length_mm:Q',
            title='喙的长度 (mm)',
            scale=alt.Scale(zero=False)
            ),
    # 颜色: 根据物种区分
    color=alt.Color('species:N', title='种类'),
    # 提示框: 鼠标悬停时显示更多信息
    tooltip=[
        alt.Tooltip('species', title='种类'),
        alt.Tooltip('bill_length_mm', title='喙长 (mm)'),
        alt.Tooltip('flipper_length_mm', title='鳍长 (mm)'),
        alt.Tooltip('body_mass_g', title='体重 (g)')
    ]
).properties(
    title='企鹅的喙长 vs. 鳍长'
).interactive() # 使图表可以缩放和拖动


# --- 显示图表和数据 ---
st.altair_chart(scatter_plot, use_container_width=True)

st.write("---")
st.subheader("详细数据视图")
st.dataframe(filtered_df, use_container_width=True)
