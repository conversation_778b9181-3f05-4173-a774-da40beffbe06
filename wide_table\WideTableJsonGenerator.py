import csv
import gzip
from itertools import islice
import json
from yyutils import *
import subprocess

bpath = r'./'


def write_gzip_csv(infile, headers=[], outlist=[], encode='utf-8', sep=','):
    with open(infile, 'wt', newline='', encoding=encode) as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers, delimiter=sep)
        writer.writeheader()
        writer.writerows(outlist)
        subprocess.run(["zstd", "-fq", "--rm", f"{infile}"])


def write_to_db(report_day):
    subprocess.run(["clickhouse-client", "-h172.16.167.5", f"""--query=alter table gdispx_data.vehicle_wide_day_all_for_report drop partition '{report_day}' """])
    ## 压缩
    subprocess.run(["clickhouse-client", "-h172.16.167.5", f"""--query=INSERT INTO vehicle_wide_day_all_for_report from infile '{report_day}.csv.zst' compression 'zstd' FORMAT CSVWithNames """, "--format_csv_allow_single_quotes=false"])


def write_json(infile, dicts, encode='utf-8'):
    with open(infile, 'wt', newline='', encoding=encode) as dstfile:
        json.dump(dicts, dstfile, ensure_ascii=False)


def write_gzip_tsv(infile, headers=[], outlist=[], encode='utf-8', sep='\t'):
    write_gzip_csv(infile, headers, outlist, encode, sep)


def getFloatDefault(e, key, default_value=None):
    if key not in e:
        e[key] = default_value
        return e[key]
    if not e[key] or float(e[key]) == 0:
        e[key] = default_value
    else:
        e[key] = float(e[key])
    return e[key]


def getIntDefault(e, key, default_value=None):
    if key not in e:
        e[key] = default_value
        return e[key]
    if not e[key] or int(e[key]) == 0:
        e[key] = default_value
    else:
        e[key] = int(e[key])


def check_has_gps_info(e, db, day):
    if not e["master_sim_code"]:
        return False
    sim = e["master_sim_code"]
    sql = f'''
                select  gps_time
                from gdispx_data.business_raw_gps_info_all 
                where toDate(gps_time) = '{day}'
                and sim_code = '{sim}' limit 1
        '''
    rows = db.query_to_dictList(sql)
    return len(rows) > 0


def collect_ever(report_day):
    start_day = TimeUtils.DeltaDay(-180, cur_day=report_day)
    end_day = TimeUtils.DeltaDay(-1, cur_day=report_day)

    ever_gps_sql = f'''
                select dict.1 as vehicle_no,dict.2 as vehicle_color from (
            select distinct (
            vehicle_no,vehicle_color) as dict
            from gdispx_data.vehicle_wide_day_all  
            where never_gps = 0 and inc_day between '{start_day}' and '{end_day}') as ever_gps
        '''

    ever_alarm_sql = f'''
                    select dict.1 as vehicle_no,dict.2 as vehicle_color from (
                select distinct (
                vehicle_no,vehicle_color) as dict
                from gdispx_data.vehicle_wide_day_all  
                where never_alarm = 0 and inc_day between '{start_day}' and '{end_day}') as ever_alarm
            '''

    ever_acc_sql = f'''
                    select dict.1 as vehicle_no,dict.2 as vehicle_color from (
                select distinct (
                vehicle_no,vehicle_color) as dict
                from gdispx_data.vehicle_wide_day_all  
                where never_acc = 0 and inc_day between '{start_day}' and '{end_day}') as ever_acc
            '''

    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist1 = db.query_to_dictList(ever_gps_sql)
    ever_gps = set([(e['vehicle_no'], str(e['vehicle_color'])) for e in datalist1])
    datalist2 = db.query_to_dictList(ever_alarm_sql)
    ever_alarm = set([(e['vehicle_no'], str(e['vehicle_color'])) for e in datalist2])
    datalist3 = db.query_to_dictList(ever_acc_sql)
    ever_acc = set([(e['vehicle_no'], str(e['vehicle_color'])) for e in datalist3])
    db.close()
    return ever_gps, ever_alarm, ever_acc


def adjust_never_online(vehicle_info, ever_gps):
    vn = (vehicle_info['vehicle_no'], str(vehicle_info['vehicle_color']))
    if vehicle_info['online'] == 1:
        vehicle_info['exception_type'] = ''
    else:
        if str(vehicle_info.get('exception_type', '')) != '2':
            vehicle_info['exception_type'] = ''
    if vn in ever_gps:
        vehicle_info['never_gps'] = 0
    elif vehicle_info['online'] == 1:
        vehicle_info['never_gps'] = 0
    else:
        vehicle_info['never_gps'] = 1


def adjust_never_alarm(vehicle_info, ever_alarm):
    vn = (vehicle_info['vehicle_no'], str(vehicle_info['vehicle_color']))
    if vn in ever_alarm:
        vehicle_info['never_alarm'] = 0
    elif vehicle_info.get('alarmNum', 0) > 0:
        vehicle_info['never_alarm'] = 0
    else:
        vehicle_info['never_alarm'] = 1


def adjust_never_accessory(vehicle_info, ever_acc):
    vn = (vehicle_info['vehicle_no'], str(vehicle_info['vehicle_color']))
    if vn in ever_acc:
        vehicle_info['never_acc'] = 0
    elif vehicle_info['has_accessory']:
        vehicle_info['never_acc'] = 0
    else:
        vehicle_info['never_acc'] = 1


def adjust_vehicle_list(vehicle_list, report_day):
    # 修正在线状态
    global ever_gps, ever_alarm, ever_acc
    if ever_gps is None:
        ever_gps, ever_alarm, ever_acc = collect_ever(report_day)

    Logger.instance().info("\t正在修正在线数据...")
    adjust_online(vehicle_list, report_day)

    # 修正三个从未状态
    Logger.instance().info("\t正在修正三个从未数据...")
    for vehicle_info in vehicle_list:
        adjust_never_online(vehicle_info, ever_gps)
        adjust_never_alarm(vehicle_info, ever_alarm)
        adjust_never_accessory(vehicle_info, ever_acc)


def collect_history_day_alarm(day):
    alarm_sql = f'''
                    SELECT
                        vehicle_no,
                        vehicle_color,
                        sum(alarmNum) AS alarmNum,
                        sum(type10405) coverAlarm
                    FROM
                        gdispx_data.statistics_original_alarm_day_all soada
                    where
                        alarm_time = '{day}'
                    group by
                        vehicle_no,
                        vehicle_color
        '''
    alarm_db = DBUtil(DBType.CLICKHOUSE.value, DBInfo.CK_PROD_ALARM.value)
    alarm_list = alarm_db.query_to_dictList(alarm_sql)
    alarm_db.close()
    return alarm_list


def collect_history_day_risk(day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=day)
    risk_sql_new = f'''
        select
            vehicle_no ,
            toUInt8(vehicle_color) vehicle_color,
            risk_oneLevel+risk_twoLevel+risk_threeLevel riskNum,
            sum(is_manual_dispose) risk_disposeNum,
            count(1) - risk_disposeNum auto_riskNum,
            sum(dispose_is_mistake) dispose_mistake_riskNum,
            sum(has_audit) audit_riskNum,
            audit_riskNum - sum(audit_is_mistake) audit_reject_riskNum,
            sum(has_check) check_riskNum,
            sum(check_is_mistake) check_confirm_riskNum,
            check_riskNum - check_confirm_riskNum check_reject_riskNum,
            sum(oneLevel) risk_oneLevel,
            sum(twoLevel) risk_twoLevel,
            sum(threeLevel) risk_threeLevel
        from 
            (SELECT 
                id,
                vehicle_no,
                vehicle_color,
                is_manual_dispose,
                dispose_is_mistake,
                has_audit,
                audit_is_mistake,
                has_check,
                check_is_mistake,
                if(check_is_mistake=1 and  oneLevel =1, 0,oneLevel) oneLevel,
                if(check_is_mistake=1 and  twoLevel =1, 0,twoLevel) twoLevel,
                if(check_is_mistake=1 and  threeLevel =1, 0,threeLevel) threeLevel
             from 
                    (SELECT 
                    distinct
                    vehicle_no ,
                    vehicle_color,
                    id,
                    if(alarm_level=1,1,0) oneLevel,
                    if(alarm_level=2,1,0) twoLevel,
                    if(alarm_level=3,1,0) threeLevel
                    from gdispx_data.business_ai_alarm_info 
                    where toDate(alarm_time) = '{day}' 
                    and alarm_code in (10402,10401,10413,90001,90002,90007,10410,10405,10412)
                    and id != '') A
                left JOIN 
                    (SELECT 
                    alarm_id,
                    max(if(dispose_type=0 and process_unit !='监管系统', 1, 0)) `is_manual_dispose`,
                    max(if(process_status in (3,7) and dispose_type=0, 1, 0)) `dispose_is_mistake`,
                    max(if(process_user != '' and dispose_type=1, 1, 0)) `has_audit`,
                    max(if(process_status=3 and dispose_type=1, 1, 0)) `audit_is_mistake`,
                    max(if(dispose_type=7, 1, 0)) `has_check`,
                    max(if(process_status=2 and dispose_type=7, 1, 0)) `check_is_mistake`
                    from gdispx_data.business_alarm_audit_info
                    where toDate(create_time) between '{day}' and '{risk_dispose_end_day}'
                    and alarm_id !=''
                    group by alarm_id) B on A.id = B.alarm_id
            ) C
        group by vehicle_no,vehicle_color
    '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    risk_dlist = db.query_to_dictList(risk_sql_new)
    db.close()
    return risk_dlist


def collect_history_day_acc(day):
    acc_sql = f'''
                select 
                distinct
                vehicle_no,vehicle_color
                from gdispx_data.business_accessories_info
                where toDate(create_time) = '{day}'
            '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    acc_dlist = db.query_to_dictList(acc_sql)
    db.close()
    return acc_dlist


def collect_history_day_track(day):
    track_sql = f'''
                SELECT 
                    vehicle_no,
                    vehicle_color,
                    total_point_count ,
                    drift_point_count,
                    continuous_mileage,
                    total_mileage,
                    error_point_count
                from gdispx_data.track_report_new tr 
                where tr.inc_day = '{day.replace('-', '')}'
            '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    track_dlist = db.query_to_dictList(track_sql)
    db.close()
    return track_dlist


def collect_history_day_gps(day):
    gps_sql = f'''
               SELECT
                       vehicle_no,
                       vehicle_color,
                       if(sum(online_time)>86400,86400,sum(online_time)) AS online_time,
                       if(sum(run_time)>86400,86400,sum(run_time)) AS run_time
                   FROM
                       gdispx_data.statistics_gps_day_all
                   where 
                       toDate(gps_time) = '{day}'
                   group by
                       vehicle_no,
                       vehicle_color
           '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    gps_dlist = db.query_to_dictList(gps_sql)
    db.close()
    return gps_dlist


def collect_history_day_middle(day):
    middle_sql = f'''
                select vehicle_no,vehicle_color,exception_type
                    from gdispx_data.vehicle_related_infos_middle 
                    where toDate(create_time) = '{day}'
        '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    middle_dlist = db.query_to_dictList(middle_sql)
    db.close()
    return middle_dlist


def collect_history_day_acc_ontime(day):
    acc_ontime_sql = f'''
            select vehicle_no,
            vehicle_color,
            alarmNum AS accessoriesNum,
            onTimeNum AS accessoriesonTimeNum
            from gdispx_data.statistics_on_time_accessories_day where toDate(alarm_time) = '{day}'
        '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    acc_ontime_dlist = db.query_to_dictList(acc_ontime_sql)
    db.close()
    return acc_ontime_dlist


def convert_vn_dict(dlist, is_vn_set=False):
    if not is_vn_set:
        vn_dict = {}
        for e in dlist:
            vn = (e['vehicle_no'], str(e['vehicle_color']))
            vn_dict.setdefault(vn, e)
        return vn_dict
    vn_set = set()
    for e in dlist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        vn_set.add(vn)
    return vn_set


def attach_data_to_vehicle(vehicle_list, alarm_dlist, risk_dlist,
                           acc_dlist, track_dlist, gps_dlist, middle_dlist, acc_ontime_dlist):
    vn_alarm = convert_vn_dict(alarm_dlist)
    vn_risk = convert_vn_dict(risk_dlist)
    acc_set = convert_vn_dict(acc_dlist, True)
    vn_track = convert_vn_dict(track_dlist)
    vn_gps = convert_vn_dict(gps_dlist)
    vn_middle = convert_vn_dict(middle_dlist)
    vn_ontime = convert_vn_dict(acc_ontime_dlist)
    for e in vehicle_list:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        if vn in vn_alarm:
            e['alarmNum'] = vn_alarm.get(vn)['alarmNum']
            e['has_cover_camera'] = 1 if vn_alarm.get(vn)['coverAlarm'] else 0
            if e['alarmNum'] > 0:
                e['has_alarm'] = 1
        if vn in vn_ontime:
            e['accessoriesNum'] = vn_ontime.get(vn)['accessoriesNum']
            e['accessoriesonTimeNum'] = vn_ontime.get(vn)['accessoriesonTimeNum']
        e['has_alarm'] = e.get('has_alarm', 0)
        if vn in vn_risk:
            e['riskNum'] = vn_risk.get(vn)['riskNum']
            e['risk_oneLevel'] = vn_risk.get(vn)['risk_oneLevel']
            e['risk_twoLevel'] = vn_risk.get(vn)['risk_twoLevel']
            e['risk_threeLevel'] = vn_risk.get(vn)['risk_threeLevel']
            e['risk_disposeNum'] = vn_risk.get(vn)['risk_disposeNum']
            e['auto_riskNum'] = vn_risk.get(vn)['auto_riskNum']
            e['dispose_mistake_riskNum'] = vn_risk.get(vn)['dispose_mistake_riskNum']
            e['audit_riskNum'] = vn_risk.get(vn)['audit_riskNum']
            e['audit_reject_riskNum'] = vn_risk.get(vn)['audit_reject_riskNum']
            e['check_riskNum'] = vn_risk.get(vn)['check_riskNum']
            e['check_confirm_riskNum'] = vn_risk.get(vn)['check_confirm_riskNum']
            e['check_reject_riskNum'] = vn_risk.get(vn)['check_reject_riskNum']
        if vn in acc_set:
            e['has_accessory'] = 1
        else:
            e['has_accessory'] = 0
        if vn in vn_gps:
            e['run_time'] = vn_gps.get(vn)['run_time']
            e['online_time'] = vn_gps.get(vn)['online_time']
            e['online'] = 2
        if vn in vn_track:
            track = vn_track.get(vn)
            e['online'] = 1
            e['total_point_count'] = track['total_point_count']
            e['drift_point_count'] = track['drift_point_count']
            e['error_point_count'] = track['error_point_count']
            e['total_mileage'] = track['total_mileage']
            e['continuous_mileage'] = track['continuous_mileage']
        e['online'] = e.get('online', 0)
        if vn in vn_middle:
            e['exception_type'] = vn_middle.get(vn)['exception_type']


def adjust_online(vehicle_list, day):
    # db = DBUtil(DBType.SCYLLA.value)
    trackDB = DBUtil(DBType.CLICKHOUSE.value, {
        'host': '**************',
        'port': 9000,
        'database': 'gdispx_data',
        'user': 'default',
        'password': '<EMAIL>'
    })
    n = 0
    tn = 0
    for e in vehicle_list:
        if e['online'] == 2:
            tn += 1
    if tn > 100000:
        # 数量太大，不检查了，直接修正
        for e in vehicle_list:
            if e['online'] == 2:
                e['online'] = 1
        Logger.instance().info(f"共找回{tn} / {tn}辆车轨迹")
        return
    for e in vehicle_list:
        if e['online'] != 2:
            continue

        if dict.get(e,'exception_type',None) is not None:
            e['online'] = 0
            e['run_time'] = None
            e['online_time'] = None
            Logger.instance().info(f"{e['vehicle_no']}:{e['master_sim_code']}在疑似屏蔽信号列表中，已修正")
            continue
        # if e['online'] != 2 and not (e['online'] == 0 and e.get('AI_BOX_sim_code',None)):
        #     continue
        has_gps_info = check_has_gps_info(e, trackDB, day)
        if has_gps_info:
            e['online'] = 1
            n += 1
            Logger.instance().info(f"找回{e['vehicle_no']}:{e['master_sim_code']}的轨迹")
        else:
            e['online'] = 0
            e['run_time'] = None
            e['online_time'] = None
    # db.close()
    trackDB.close()
    Logger.instance().info(f"共找回{n} / {tn}辆车轨迹")


wh = ['inc_day', 'vehicle_id', 'vehicle_no', 'vehicle_color', 'vehicle_type', 'service_result', 'access_time', 'owner_id',
      'owner_name', 'facilitator_id', 'facilitator_name', 'facilitator_social_credit_code', 'third_party_id',
      'third_party_name', 'third_party_social_credit_code', 'third_party_city_id',
      'device_type', 'master_device_id', 'master_sim_code', 'master_producter_id',
      'master_producter_name',
      'master_type_num', 'master_device_brand', 'AI_BOX_device_id', 'AI_BOX_sim_code', 'AI_BOX_producter_id',
      'AI_BOX_producter_name', 'AI_BOX_type_num', 'AI_BOX_device_brand',
      'area_id', 'area_name',
      'area_short_name', 'city_id', 'city_name', 'city_short_name', 'area_manger_name',
      'area_manger_short_name', 'alarmNum',
      'riskNum', 'risk_oneLevel', 'risk_twoLevel', 'risk_threeLevel',
      'risk_disposeNum', 'auto_riskNum', 'dispose_mistake_riskNum', 'audit_riskNum', 'audit_reject_riskNum',
      'check_riskNum', 'check_confirm_riskNum', 'check_reject_riskNum',
      'accessoriesNum', 'accessoriesonTimeNum',
      'run_time', 'online_time', 'total_point_count', 'drift_point_count', 'error_point_count', 'total_mileage',
      'continuous_mileage', 'online', 'exception_type', 'has_alarm', 'has_accessory',
      'has_cover_camera',
      'never_gps', 'never_alarm', 'never_acc']


def collect_cur_jg_dict():
    db = DBUtil(DBType.CLICKHOUSE.value)
    vehicle_sql = f'''
                     select
                 	vria.vehicle_id AS vehicle_id,
                 	vria.vehicle_no AS vehicle_no,
                 	vria.vehicle_color AS vehicle_color,
                 	vria.vehicle_type AS vehicle_type,
                 	vria.service_result AS service_result,
                 	toDate(vria.access_time) AS access_time,
                 	vria.owner_id AS owner_id,
                 	vria.owner_name AS owner_name,
                 	vria.facilitator_id AS facilitator_id,
                 	vria.facilitator_name AS facilitator_name,
                 	vria.facilitator_social_credit_code AS facilitator_social_credit_code,
                 	vria.third_party_id AS third_party_id,
                 	ba1.city_id third_party_city_id,
                 	vria.third_party_name AS third_party_name,
                 	vria.third_party_social_credit_code AS third_party_social_credit_code,
                 	vria.device_type AS device_type,
                 	trim(vria.master_device_id) AS master_device_id,
                 	vria.master_sim_code AS master_sim_code,
                 	vria.master_producter_id AS master_producter_id,
                 	vria.master_producter_name AS master_producter_name,
                 	vria.master_type_num AS master_type_num,
                 	vria.master_device_brand AS master_device_brand,
                 	vria.AI_BOX_device_id AS AI_BOX_device_id,
                 	vria.AI_BOX_sim_code AS AI_BOX_sim_code,
                 	vria.AI_BOX_producter_id AS AI_BOX_producter_id,
                 	vria.AI_BOX_producter_name AS AI_BOX_producter_name,
                 	vria.AI_BOX_type_num AS AI_BOX_type_num,
                 	vria.AI_BOX_device_brand AS AI_BOX_device_brand,
                 	ba.id AS area_id,
                 	ba.name AS area_name,
                 	ba.short_name AS area_short_name,
                 	ba.city_id AS city_id,
                 	splitByChar(',', ba.manger_name)[2] AS city_name,
                 	ba.city_short AS city_short_name,
                 	ba.manger_name AS area_manger_name,
                 	ba.manger_short_name AS area_manger_short_name
                 from gdispx_data.vehicle_related_infos_all vria
                 left join gdispx_data.basics_area ba
                 on vria.area_id = ba.id
                 left join gdispx_data.basics_third_party_info btpi
                 on vria.third_party_id = btpi.id
                 left join gdispx_data.basics_area ba1
                 on ba1.id = btpi.area_id
                 where (vria.vehicle_type in ('1', '2') and vria.service_result in ('1', '0', '-1'))
         		   or (vria.vehicle_type in ('3', '4') and vria.service_result in ('1'))
                 '''
    datalist = db.query_to_dictList(vehicle_sql)
    db.close()
    vn_e = {}
    for e in datalist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        getIntDefault(e, 'third_party_id', None)
        getIntDefault(e, 'third_party_city_id', None)
        if str(e['AI_BOX_device_id']) == '0':
            e['AI_BOX_device_id'] = None
        vn_e.setdefault(vn, e)
    return vn_e


def copy_attr_from_e1_to_e2(ce, e, ts):
    if isinstance(ts, dict):
        for k1, k2 in ts.items():
            e[k2] = ce[k1]
    elif isinstance(ts, list) or isinstance(ts, tuple):
        for k in ts:
            e[k] = ce[k]


def p_vehicle_history_day(day, cur_vn_e, otfile=None):
    db = DBUtil(DBType.CLICKHOUSE.value)
    vehicle_sql = f'''
                      select
                  	vhj.vehicle_id AS vehicle_id,
                  	vhj.vehicle_no AS vehicle_no,
                  	vhj.vehicle_color AS vehicle_color,
                  	vhj.vehicle_type AS vehicle_type,
                  	vhj.service_result AS service_result,
                  	vhj.owner_id AS owner_id,
                  	vhj.owner_name AS owner_name,
                  	vhj.facilitator_id AS facilitator_id,
                  	vhj.facilitator_name AS facilitator_name,
                  	vhj.third_party_id AS third_party_id,
                  	vhj.third_party_name AS third_party_name,
                  	vhj.master_sim_code AS master_sim_code,
                  	ba.id AS area_id,
                  	ba.name AS area_name,
                  	ba.short_name AS area_short_name,
                  	ba.city_id AS city_id,
                  	splitByChar(',', ba.manger_name)[2] AS city_name,
                  	ba.city_short AS city_short_name,
                  	ba.manger_name AS area_manger_name,
                  	ba.manger_short_name AS area_manger_short_name
                  from gdispx_data.vehicle_history_jianguan vhj
                  left join gdispx_data.basics_area ba
                  on toInt32(vhj.area_id) = ba.id
                  where vhj.`create_date` = '{day}'
                  order by city_id,area_id,owner_id,third_party_id,vehicle_no
                  '''
    datalist = db.query_to_dictList(vehicle_sql)
    ts = ['facilitator_social_credit_code', 'third_party_social_credit_code',
          'third_party_city_id',
          'access_time', 'device_type', 'master_device_id', 'master_producter_id',
          'master_producter_name', 'master_type_num', 'master_device_brand',
          'AI_BOX_device_id', 'AI_BOX_sim_code', 'AI_BOX_producter_id',
          'AI_BOX_producter_name', 'AI_BOX_type_num', 'AI_BOX_device_brand',
          ]
    db.close()
    for e in datalist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        ce = cur_vn_e.get(vn)
        getIntDefault(e, 'third_party_id', None)
        vehicle_id = getIntDefault(e, 'vehicle_id', None)
        if not vehicle_id and ce:
            e['vehicle_id'] = ce['vehicle_id']
        if ce:
            copy_attr_from_e1_to_e2(ce, e, ts)
        if not e['third_party_id']:
            e['third_party_social_credit_code'] = ''
            e['third_party_city_id'] = None
        e['inc_day'] = day
        # if e['access_time']:
        #     e['access_time'] = TimeUtils.strptime(e['access_time'], DateFormat.Y_m_d.value)
        # else:
        #     e['access_time'] = None
    return datalist


cur_vn_e, ever_gps, ever_alarm, ever_acc = None, None, None, None


def collect_history_jg(report_day):
    global cur_vn_e
    if cur_vn_e is None:
        cur_vn_e = collect_cur_jg_dict()
    jg_datalist = p_vehicle_history_day(report_day, cur_vn_e)
    return jg_datalist


def adjust_data_types(g):
    for e in g:
        getIntDefault(e, 'vehicle_id', 0)
        getIntDefault(e, 'vehicle_color', 0)
        getIntDefault(e, 'vehicle_type', 0)
        getIntDefault(e, 'owner_id', 0)
        getIntDefault(e, 'facilitator_id', None)
        getIntDefault(e, 'third_party_id', None)
        getIntDefault(e, 'third_party_city_id', None)
        getIntDefault(e, 'device_type', None)
        getIntDefault(e, 'master_producter_id', None)
        getIntDefault(e, 'AI_BOX_producter_id', None)
        getIntDefault(e, 'area_id', 0)
        getIntDefault(e, 'city_id', 0)
        getIntDefault(e, 'alarmNum', 0)
        getIntDefault(e, 'riskNum', 0)
        getIntDefault(e, 'risk_oneLevel', 0)
        getIntDefault(e, 'risk_twoLevel', 0)
        getIntDefault(e, 'risk_threeLevel', 0)
        getIntDefault(e, 'risk_disposeNum', 0)
        getIntDefault(e, 'auto_riskNum', 0)
        getIntDefault(e, 'dispose_mistake_riskNum', 0)
        getIntDefault(e, 'audit_riskNum', 0)
        getIntDefault(e, 'audit_reject_riskNum', 0)
        getIntDefault(e, 'check_riskNum', 0)
        getIntDefault(e, 'check_confirm_riskNum', 0)
        getIntDefault(e, 'check_reject_riskNum', 0)
        getIntDefault(e, 'accessoriesNum', 0)
        getIntDefault(e, 'accessoriesonTimeNum', 0)
        getIntDefault(e, 'run_time', 0)
        getIntDefault(e, 'online_time', 0)
        getIntDefault(e, 'total_point_count', 0)
        getIntDefault(e, 'drift_point_count', 0)
        getIntDefault(e, 'error_point_count', 0)
        getFloatDefault(e, 'total_mileage', 0)
        getFloatDefault(e, 'continuous_mileage', 0)
        getIntDefault(e, 'online', 0)
        getIntDefault(e, 'exception_type', 0)
        getIntDefault(e, 'has_alarm', 0)
        getIntDefault(e, 'has_accessory', 0)
        getIntDefault(e, 'has_cover_camera', 0)
        e['never_gps'] = int(e['never_gps'])
        e['never_alarm'] = int(e['never_alarm'])
        e['never_acc'] = int(e['never_acc'])
        if 'access_time' not in e:
            e['access_time'] = None
        if e['access_time']:
            e['access_time'] = TimeUtils.strftime(e['access_time'], DateFormat.Y_m_d.value)
        else:
            e['access_time'] = None


@countTime("写入数据库")
def do_save_to_db(db, g, has_need_convert=True):
    adjust_data_types(g)
    insert_sql = f'''
                    INSERT into gdispx_data.vehicle_wide_day_all_for_report 
                    (`inc_day`,`vehicle_id`,`vehicle_no`,`vehicle_color`,`vehicle_type`,`service_result`,
                    `access_time`,`owner_id`, `owner_name`, `facilitator_id`, `facilitator_name`,
                     `facilitator_social_credit_code`,`third_party_id`, `third_party_name`,`third_party_social_credit_code`,
                     `third_party_city_id`,
                     `device_type`,`master_device_id`,`master_sim_code`,`master_producter_id`,
                    `master_producter_name`,`master_type_num`,`master_device_brand`,
                    `AI_BOX_device_id`,`AI_BOX_sim_code`,`AI_BOX_producter_id`,
                    `AI_BOX_producter_name`,`AI_BOX_type_num`,`AI_BOX_device_brand`,
                    `area_id`,`area_name`, `area_short_name` ,`city_id`,`city_name`,`city_short_name`,
                    `area_manger_name` , `area_manger_short_name`,
                    `alarmNum`, `riskNum` , 
                    `risk_oneLevel`,`risk_twoLevel`,`risk_threeLevel`,
                    `risk_disposeNum`,`auto_riskNum`,`dispose_mistake_riskNum`,`audit_riskNum`,
                    `audit_reject_riskNum`,`check_riskNum`,`check_confirm_riskNum`,`check_reject_riskNum`,
                    `accessoriesNum`,`accessoriesonTimeNum`, `run_time`,`online_time` ,
                    `total_point_count`,`drift_point_count`,`error_point_count`,
                    `total_mileage`,`continuous_mileage`,`online`,`exception_type`,
                    `has_alarm`, `has_accessory`,`has_cover_camera` ,`never_gps`, `never_alarm`,
                     `never_acc`
                    ) values 
                '''
    db.excute(insert_sql, g)


from math import ceil


def chunk_into_n(lst, n):
    size = ceil(len(lst) / n)
    return list(
        map(lambda x: lst[x * size:x * size + size],
            list(range(n)))
    )


def save_to_db_by_chunks(vehicle_list):
    db = DBUtil(DBType.CLICKHOUSE.value)
    for slice_item in chunk_into_n(vehicle_list, 10):
        for e in slice_item:
            e['vehicle_id'] = str(abs(int(e['vehicle_id'])))[:9]
        do_save_to_db(db, slice_item)
    # for g in chunks(vehicle_list, 50000):


def save_to_db(infile, report_day):
    db = DBUtil(DBType.CLICKHOUSE.value)
    db.excute(f"alter table gdispx_data.vehicle_wide_day_all_for_report drop partition '{report_day}' ")
    for g in read_by_trunk(
            readCsv(infile, print_count=100000, print_title=f'{report_day}'), 50000):
        for e in g:
            e['vehicle_id'] = str(abs(int(e['vehicle_id'])))[:9]
        do_save_to_db(db, g)
    db.close()


def chunks(arr_range, arr_size):
    arr_range = iter(arr_range)
    return iter(lambda: tuple(islice(arr_range, arr_size)), ())


def collect_common_data(report_day):
    global ever_gps, ever_alarm, ever_acc, cur_vn_e
    if ever_gps is None:
        ever_gps, ever_alarm, ever_acc = collect_ever(report_day)
    if cur_vn_e is None:
        cur_vn_e = collect_cur_jg_dict()


@countTime("生产宽表")
def do_create_recent_report_wide(report_day):
    Logger.instance().info(f'开始生产宽表{report_day}的宽表...')
    Logger.instance().info("\t正在加载公共数据..")
    collect_common_data(report_day)
    Logger.instance().info("\t通过历史监管表获取当日监管车辆..")
    vehicle_list = collect_history_jg(report_day)
    Logger.instance().info(f"\t共获取到{len(vehicle_list)}条信息...")
    Logger.instance().info("\t获取报警信息..")
    alarm_dlist = collect_history_day_alarm(report_day)
    Logger.instance().info(f"\t共获取到{len(alarm_dlist)}条信息...")

    Logger.instance().info("\t获取风险信息..")
    risk_dlist = collect_history_day_risk(report_day)
    Logger.instance().info(f"\t共获取到{len(risk_dlist)}条信息...")

    Logger.instance().info("\t获取附件信息..")
    acc_dlist = collect_history_day_acc(report_day)
    Logger.instance().info(f"\t共获取到{len(acc_dlist)}条信息...")

    Logger.instance().info("\t获取车辆在线时长与运行时长..")
    gps_dlist = collect_history_day_gps(report_day)
    Logger.instance().info(f"\t共获取到{len(gps_dlist)}条信息...")

    Logger.instance().info("\t获取车辆漂移信息..")
    track_dlist = collect_history_day_track(report_day)
    Logger.instance().info(f"\t共获取到{len(track_dlist)}条信息...")

    Logger.instance().info("\t获取车辆疑似屏蔽信号信息..")
    middle_dlist = collect_history_day_middle(report_day)
    Logger.instance().info(f"\t共获取到{len(middle_dlist)}条信息...")

    Logger.instance().info("\t获取车辆附件丢失率信息..")
    acc_ontime_dlist = collect_history_day_acc_ontime(report_day)
    Logger.instance().info(f"\t共获取到{len(acc_ontime_dlist)}条信息...")

    Logger.instance().info("开始关联数据...")
    attach_data_to_vehicle(vehicle_list, alarm_dlist, risk_dlist,
                           acc_dlist, track_dlist, gps_dlist, middle_dlist, acc_ontime_dlist)
    Logger.instance().info("根据数据逻辑进行数据修正...")
    adjust_vehicle_list(vehicle_list, report_day)
    Logger.instance().info("修正数据类型...")
    adjust_data_types(vehicle_list)
    Logger.instance().info("正在保存到文件...")
    # write_json(join_path(bpath, f'{report_day}.json'), vehicle_list)
    write_gzip_csv(join_path(bpath, f'{report_day}.csv'), wh, vehicle_list)
    Logger.instance().info("正在写入数据库...")
    write_to_db(report_day)
    # save_to_db(join_path(bpath, f'{report_day}.csv'), report_day)
    # copyfile(join_path(bpath, f'{report_day}.csv'), join_path(r'/data/track_report', f'wide_{report_day}.csv'))
    Logger.instance().info(f'{report_day} 生产宽表完毕!')


@countTime("制作报表宽表")
def create_recent_report_wide(report_day):
    t = TimeUtils.strptime(report_day, DateFormat.Y_m_d.value)
    # 解决风险处置工作日的问题  +3工作日（国庆 7+3）
    for i in range(11, 0, -1):
        try:
            day = TimeUtils.DeltaDay(-i, cur_day=t)
            do_create_recent_report_wide(day)
        except Exception as ex:
            print(ex)
    do_create_recent_report_wide(report_day)

def main(report_day):
    create_recent_report_wide(report_day)



if __name__ == '__main__':
    report_day = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    main(report_day)
