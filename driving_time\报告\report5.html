<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广东省重点监管车辆离线位移情况</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.3.2/papaparse.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f4f8;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 320px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 384px;
            }
        }
        .stat-card {
            background: linear-gradient(135deg, #374c80, #003f5c);
            color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
        .stat-number {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1;
        }
        .stat-label {
            font-size: 1.1rem;
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        .section-title-bar {
            border-left: 5px solid #ff764a;
            padding-left: 1rem;
        }
        .recommendation-card h4::before {
            content: '➔';
            margin-right: 0.75rem;
            font-weight: bold;
        }
        .loader {
            border: 8px solid #f3f3f3;
            border-radius: 50%;
            border-top: 8px solid #3498db;
            width: 60px;
            height: 60px;
            animation: spin 2s linear infinite;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .loading-text {
            margin-top: 20px;
            font-size: 1.2rem;
            color: #333;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

<div id="loadingOverlay" class="loading-overlay">
    <div class="loader"></div>
    <p class="loading-text">正在加载并实时计算分析报告...</p>
</div>

<div class="container mx-auto p-4 md:p-8" style="visibility: hidden;">

    <header class="text-center mb-12">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">广东省重点监管车辆离线位移情况</h1>
        <h2 class="text-xl md:text-2xl font-semibold text-[#003f5c]">可视化分析报告</h2>
        <p class="mt-4 text-gray-600 max-w-3xl mx-auto">本报告旨在通过数据可视化手段，深度剖析重点监管车辆在终端离线期间的位移事件，揭示潜在风险，为提升行业监管效能提供决策支持。本次共计分析了 <strong id="totalEvents" class="text-[#bc5090] font-bold">...</strong> 条有效离线位移事件记录。</p>
    </header>

    <main class="space-y-12">

        <section id="overview">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold">总体态势分析</h3>
                <p class="text-gray-600 mt-1">从宏观层面洞察离线位移事件的分布特征，把握整体风险轮廓。多数事件表现为短时、短距，但隐藏其中的极端案例是本次分析的焦点。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold text-center mb-4">离线小时分布</h4>
                    <p id="duration-text" class="text-sm text-gray-500 text-center mb-4">正在计算...</p>
                    <div class="chart-container h-80 md:h-96">
                        <canvas id="durationChart"></canvas>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold text-center mb-4">离线位移距离分布</h4>
                    <p id="distance-text" class="text-sm text-gray-500 text-center mb-4">正在计算...</p>
                    <div class="chart-container h-80 md:h-96">
                        <canvas id="distanceChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <section id="long-distance">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#bc5090]">核心关注点 (一): 超长距离离线位移</h3>
                <p class="text-gray-600 mt-1">此类“短时-长距”事件强烈暗示车辆可能在完全脱离监管的状态下进行跨区域长途运输，风险等级最高，极可能涉及蓄意规避行为。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="md:col-span-1 flex items-center justify-center">
                    <div class="stat-card w-full">
                        <div id="max-distance" class="stat-number">...<span class="text-2xl">公里</span></div>
                        <div class="stat-label">单次离线最大位移记录</div>
                    </div>
                </div>
                <div class="md:col-span-2 bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold mb-4">部分超长距离位移事件清单</h4>
                    <p class="text-sm text-gray-500 mb-4">以下车辆在数小时内离线位移数百乃至上千公里，其行为严重偏离正常运营，需立即启动专项核查程序。</p>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="bg-gray-100 text-gray-600 uppercase">
                            <tr>
                                <th class="p-3">车牌号</th>
                                <th class="p-3">离线时长(小时)</th>
                                <th class="p-3 text-right">位移距离 (公里)</th>
                            </tr>
                            </thead>
                            <tbody id="long-distance-table">
                            <!-- Data will be populated by script -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <section id="long-duration">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#7a5195]">核心关注点 (二): 长时间离线伴随位移</h3>
                <p class="text-gray-600 mt-1">车辆持续数天脱离监控网络，同时仍有位移记录，反映出设备健康状况或合规管理存在严重问题，是对监管体系的重大挑战。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="md:col-span-1 flex items-center justify-center">
                    <div class="stat-card w-full">
                        <div id="max-duration" class="stat-number">...<span class="text-2xl">天</span></div>
                        <div class="stat-label">单次离线最长持续时间</div>
                    </div>
                </div>
                <div class="md:col-span-2 bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold mb-4">部分长时间离线伴随位移事件</h4>
                    <p class="text-sm text-gray-500 mb-4">这些车辆长时间“失联”，需对车辆终端设备进行强制健康检查，并评估是否存在人为规避或对离线告警漠视的行为。</p>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="bg-gray-100 text-gray-600 uppercase">
                            <tr>
                                <th class="p-3">车牌号</th>
                                <th class="p-3">离线时长(小时)</th>
                                <th class="p-3 text-right">位移距离 (公里)</th>
                            </tr>
                            </thead>
                            <tbody id="long-duration-table">
                            <!-- Data will be populated by script -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <section id="high-frequency">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold">核心关注点 (三): 高频离线位移车辆识别</h3>
                <p class="text-gray-600 mt-1">识别“惯犯”车辆，有助于发现持续性的设备问题或管理漏洞。这些“病灶车”应被列为最高优先级的监管与整改对象。</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h4 class="text-lg font-semibold text-center mb-4">离线位移事件高频车辆 TOP 5</h4>
                <p class="text-sm text-gray-500 text-center mb-4">以下车辆在分析周期内频繁发生离线位移事件。这强烈指向了设备持续故障或管理长期缺位的问题。</p>
                <div class="chart-container h-80 md:h-96">
                    <canvas id="frequencyChart"></canvas>
                </div>
            </div>
        </section>

        <section id="owner-analysis">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#ef5675]">核心关注点 (四): 高发离线位移业户</h3>
                <p class="text-gray-600 mt-1">将风险下钻至企业主体，识别管理中的薄弱环节。事件高发的业户需接受重点监管与专项检查。</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h4 class="text-lg font-semibold text-center mb-4">离线位移事件高发业户 TOP 5</h4>
                <p class="text-sm text-gray-500 text-center mb-4">这些企业名下的车辆离线位移事件总数最多，反映出其在车辆动态监控、设备维护或驾驶员管理上可能存在系统性问题。</p>
                <div class="chart-container h-80 md:h-96">
                    <canvas id="ownerChart"></canvas>
                </div>
            </div>
        </section>

        <section id="platform-analysis">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#ff764a]">核心关注点 (五): 事件集中的第三方平台</h3>
                <p class="text-gray-600 mt-1">从技术服务商维度进行分析，评估不同平台的设备稳定性与服务质量，为技术选型和供应商管理提供参考。</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h4 class="text-lg font-semibold text-center mb-4">离线位移事件集中的第三方平台 TOP 5</h4>
                <p class="text-sm text-gray-500 text-center mb-4">关联事件数较多的平台，可能因其市场份额大，或其终端设备、网络服务的稳定性有待提高。建议对其进行服务质量评估。</p>
                <div class="chart-container h-80 md:h-96">
                    <canvas id="platformChart"></canvas>
                </div>
            </div>
        </section>

        <section id="recommendations">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold">结论与系统性管理建议</h3>
                <p class="text-gray-600 mt-1">基于数据洞察，建议构建包含技术升级、管理规程、监管执法和长效机制的“四位一体”综合治理体系，从根本上解决问题。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#ff764a] recommendation-card">
                    <h4 class="text-xl font-bold text-[#ff764a] mb-3">技术体系优化</h4>
                    <p class="text-gray-600">提升终端准入门槛，推广双模通讯，并升级平台智能分析告警能力，建立“终端健康度”评分体系。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#ffa600] recommendation-card">
                    <h4 class="text-xl font-bold text-[#ffa600] mb-3">企业责任压实</h4>
                    <p class="text-gray-600">要求企业建立车辆技术档案，将在线率与绩效挂钩，并强化监控人员培训，确保告警有效处置。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#7a5195] recommendation-card">
                    <h4 class="text-xl font-bold text-[#7a5195] mb-3">创新执法协同</h4>
                    <p class="text-gray-600">实施基于“企业画像”的差异化监管，通过数据驱动现场稽查，并与公安、高速管理部门联动，核实轨迹。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#003f5c] recommendation-card">
                    <h4 class="text-xl font-bold text-[#003f5c] mb-3">构建长效机制</h4>
                    <p class="text-gray-600">建立常态化分析通报与“红黑榜”制度，完善法规，细化对违规行为的处罚标准，并可引入第三方评估。</p>
                </div>
            </div>
        </section>

    </main>

    <footer class="text-center mt-16 pt-8 border-t border-gray-200">
        <p class="text-sm text-gray-500">广东省道路运输事务中心 | 数据可视化分析报告</p>
        <p class="text-xs text-gray-400 mt-2">数据来源: `5月客运车辆离线漂移明细(已过滤).xlsx - Sheet1.csv` | 分析周期: 2025年5月 | 生成时间: <span id="generation-time"></span></p>
    </footer>

</div>

<script>
    // --- 1. CONFIGURATION & SETUP ---
    const CSV_URL = './5月客运车辆离线漂移明细(已过滤).csv';

    const brilliantBluesPalette = {
        primary: '#003f5c',
        secondary: '#374c80',
        tertiary: '#7a5195',
        accent1: '#bc5090',
        accent2: '#ef5675',
        accent3: '#ff764a',
        accent4: '#ffa600',
    };

    const commonChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { position: 'bottom', labels: { font: { family: "'Noto Sans SC', sans-serif" } } },
            tooltip: {
                titleFont: { family: "'Noto Sans SC', sans-serif" },
                bodyFont: { family: "'Noto Sans SC', sans-serif" },
                callbacks: {
                    title: (tooltipItems) => {
                        const item = tooltipItems[0];
                        if (!item || !item.chart.data.labels) return '';
                        let label = item.chart.data.labels[item.dataIndex];
                        return Array.isArray(label) ? label.join(' ') : label;
                    }
                }
            }
        },
        animation: { duration: 1000, easing: 'easeInOutQuart' }
    };

    // --- 2. DATA PROCESSING & CALCULATION ---

    // Function to parse 'X天 Y小时Z分W秒' or 'Y:Z:W' into total hours
    function parseDurationToHours(durationStr) {
        if (!durationStr || typeof durationStr !== 'string') return 0;
        let totalHours = 0;
        if (durationStr.includes('天')) {
            const parts = durationStr.split('天');
            totalHours += parseInt(parts[0], 10) * 24;
            const timeParts = parts[1].split(/小时|分|秒/).filter(Boolean).map(s => parseInt(s, 10));
            if (timeParts[0]) totalHours += timeParts[0];
            if (timeParts[1]) totalHours += timeParts[1] / 60;
            if (timeParts[2]) totalHours += timeParts[2] / 3600;
        } else {
            const timeParts = durationStr.split(':').map(s => parseInt(s, 10));
            if (timeParts[0]) totalHours += timeParts[0];
            if (timeParts[1]) totalHours += timeParts[1] / 60;
            if (timeParts[2]) totalHours += timeParts[2] / 3600;
        }
        return totalHours;
    }

    // Function to get top N items from a column
    function getTopN(data, columnName, n) {
        const counts = data.reduce((acc, row) => {
            const key = row[columnName];
            if (key) {
                acc[key] = (acc[key] || 0) + 1;
            }
            return acc;
        }, {});
        return Object.entries(counts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, n);
    }

    // --- 3. MAIN EXECUTION ---

    document.addEventListener('DOMContentLoaded', () => {
        fetch(CSV_URL)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.statusText}`);
                }
                return response.text();
            })
            .then(csvText => {
                Papa.parse(csvText, {
                    header: true,
                    skipEmptyLines: true,
                    complete: (results) => {
                        const data = results.data;
                        console.log("CSV Data Loaded and Parsed:", data);
                        processData(data);
                    },
                    error: (error) => {
                        throw new Error(`CSV解析错误: ${error.message}`);
                    }
                });
            })
            .catch(error => {
                console.error("加载或处理数据时发生严重错误:", error);
                const loadingOverlay = document.getElementById('loadingOverlay');
                loadingOverlay.innerHTML = `<div style="color:red; text-align: center;">数据加载失败！<br>${error.message}</div>`;
            });
    });

    function processData(data) {
        // --- Process All Data Points ---

        // General Stats
        document.getElementById('totalEvents').textContent = data.length;
        document.getElementById('generation-time').textContent = new Date().toLocaleString('zh-CN');

        // Duration Analysis
        const durationLabels = ['<1小时', '1-6小时', '6-12小时', '12-24小时', '1-3天', '3-7天', '>7天'];
        const durationCounts = new Array(durationLabels.length).fill(0);
        let totalDurationHours = 0;
        data.forEach(row => {
            const hours = parseDurationToHours(row['离线小时']);
            totalDurationHours += hours;
            if (hours < 1) durationCounts[0]++;
            else if (hours < 6) durationCounts[1]++;
            else if (hours < 12) durationCounts[2]++;
            else if (hours < 24) durationCounts[3]++;
            else if (hours < 72) durationCounts[4]++;
            else if (hours < 168) durationCounts[5]++;
            else durationCounts[6]++;
        });
        const shortDurationPercent = ((durationCounts[0] + durationCounts[1]) / data.length * 100).toFixed(0);
        document.getElementById('duration-text').textContent = `分析显示，约${shortDurationPercent}%的事件离线小时在6小时以内，但仍有超过${100-shortDurationPercent}%的事件离线时间较长，构成了主要的监管风险。`;

        // Distance Analysis
        const distanceLabels = ['0-10公里', '10-50公里', '50-200公里', '超过200公里(高风险)'];
        const distanceCounts = new Array(distanceLabels.length).fill(0);
        data.forEach(row => {
            const dist = parseFloat(row['离线距离']);
            if (dist < 10) distanceCounts[0]++;
            else if (dist < 50) distanceCounts[1]++;
            else if (dist < 200) distanceCounts[2]++;
            else distanceCounts[3]++;
        });
        const shortDistancePercent = ((distanceCounts[0] + distanceCounts[1]) / data.length * 100).toFixed(0);
        document.getElementById('distance-text').textContent = `大多数事件的位移距离较短，超过${shortDistancePercent}%的事件位移在50公里内。但数据中存在的超长距离位移事件是本次分析的重中之重，它们揭示了最严重的监管漏洞。`;

        // Extreme Values & Tables
        const sortedByDistance = [...data].sort((a, b) => parseFloat(b['离线距离']) - parseFloat(a['离线距离']));
        const sortedByDuration = [...data].sort((a, b) => parseDurationToHours(b['离线小时']) - parseDurationToHours(a['离线小时']));

        document.getElementById('max-distance').innerHTML = `${parseFloat(sortedByDistance[0]['离线距离']).toFixed(2)}<span class="text-2xl">公里</span>`;
        document.getElementById('max-duration').innerHTML = `${(parseDurationToHours(sortedByDuration[0]['离线小时']) / 24).toFixed(2)}<span class="text-2xl">天</span>`;

        const longDistTable = document.getElementById('long-distance-table');
        longDistTable.innerHTML = sortedByDistance.slice(0, 5).map(row => `
            <tr class="border-b hover:bg-gray-50">
                <td class="p-3 font-medium text-gray-900">${row['车牌号']}</td>
                <td class="p-3">${row['离线小时']}</td>
                <td class="p-3 text-right font-bold text-red-600">${parseFloat(row['离线距离']).toFixed(2)}</td>
            </tr>
        `).join('');

        const longDurationTable = document.getElementById('long-duration-table');
        longDurationTable.innerHTML = sortedByDuration.slice(0, 5).map(row => `
             <tr class="border-b hover:bg-gray-50">
                <td class="p-3 font-medium text-gray-900">${row['车牌号']}</td>
                <td class="p-3 font-bold text-purple-600">${row['离线小时']}</td>
                <td class="p-3 text-right">${parseFloat(row['离线距离']).toFixed(2)}</td>
            </tr>
        `).join('');

        // Top N Charts
        const topVehicles = getTopN(data, '车牌号', 5);
        const topOwners = getTopN(data, '业户', 5);
        const topPlatforms = getTopN(data, '第三方', 5);

        // --- 4. RENDER CHARTS ---
        renderAllCharts({
            durationCounts, durationLabels,
            distanceCounts, distanceLabels,
            topVehicles, topOwners, topPlatforms
        });

        // Hide loader, show content
        document.getElementById('loadingOverlay').style.display = 'none';
        document.querySelector('.container').style.visibility = 'visible';
    }

    function renderAllCharts(chartData) {
        // Duration Chart
        new Chart(document.getElementById('durationChart'), {
            type: 'doughnut',
            data: {
                labels: chartData.durationLabels,
                datasets: [{
                    label: '事件数量',
                    data: chartData.durationCounts,
                    backgroundColor: Object.values(brilliantBluesPalette),
                    borderColor: '#f0f4f8',
                    borderWidth: 3,
                    hoverOffset: 10
                }]
            },
            options: { ...commonChartOptions }
        });

        // Distance Chart
        new Chart(document.getElementById('distanceChart'), {
            type: 'bar',
            data: {
                labels: chartData.distanceLabels,
                datasets: [{
                    label: '事件数量',
                    data: chartData.distanceCounts,
                    backgroundColor: [
                        brilliantBluesPalette.primary,
                        brilliantBluesPalette.secondary,
                        brilliantBluesPalette.tertiary,
                        brilliantBluesPalette.accent2
                    ],
                    borderRadius: 5, barPercentage: 0.6
                }]
            },
            options: { ...commonChartOptions, scales: { y: { beginAtZero: true, title: { display: true, text: '事件数量 (条)', font: { size: 14 } } } }, plugins: { ...commonChartOptions.plugins, legend: { display: false } } }
        });

        // Top Vehicles Chart
        new Chart(document.getElementById('frequencyChart'), {
            type: 'bar',
            data: {
                labels: chartData.topVehicles.map(item => item[0]),
                datasets: [{ label: '离线位移事件次数', data: chartData.topVehicles.map(item => item[1]), backgroundColor: [ brilliantBluesPalette.accent2, brilliantBluesPalette.accent1, brilliantBluesPalette.tertiary, brilliantBluesPalette.secondary, brilliantBluesPalette.primary ], borderRadius: 5, barPercentage: 0.5 }]
            },
            options: { ...commonChartOptions, indexAxis: 'y', scales: { x: { beginAtZero: true, title: { display: true, text: '事件次数', font: { size: 14 } } } }, plugins: { ...commonChartOptions.plugins, legend: { display: false } } }
        });

        // Top Owners Chart
        new Chart(document.getElementById('ownerChart'), {
            type: 'bar',
            data: {
                labels: chartData.topOwners.map(item => item[0]),
                datasets: [{ label: '离线位移事件次数', data: chartData.topOwners.map(item => item[1]), backgroundColor: [ brilliantBluesPalette.accent2, brilliantBluesPalette.accent1, brilliantBluesPalette.tertiary, brilliantBluesPalette.secondary, brilliantBluesPalette.primary ], borderRadius: 5, barPercentage: 0.5 }]
            },
            options: { ...commonChartOptions, indexAxis: 'y', scales: { x: { beginAtZero: true, title: { display: true, text: '事件次数', font: { size: 14 } } } }, plugins: { ...commonChartOptions.plugins, legend: { display: false }, tooltip: { callbacks: { title: (tooltipItems) => { const item = tooltipItems[0]; let label = item.label || ''; const max = 20; let lines = []; for (let i = 0; i < label.length; i += max) { lines.push(label.substring(i, i + max)); } return lines; } } } } }
        });

        // Top Platforms Chart
        new Chart(document.getElementById('platformChart'), {
            type: 'bar',
            data: {
                labels: chartData.topPlatforms.map(item => item[0]),
                datasets: [{ label: '离线位移事件次数', data: chartData.topPlatforms.map(item => item[1]), backgroundColor: [ brilliantBluesPalette.accent3, brilliantBluesPalette.accent4, brilliantBluesPalette.tertiary, brilliantBluesPalette.secondary, brilliantBluesPalette.primary ], borderRadius: 5, barPercentage: 0.5 }]
            },
            options: { ...commonChartOptions, indexAxis: 'y', scales: { x: { beginAtZero: true, title: { display: true, text: '事件次数', font: { size: 14 } } } }, plugins: { ...commonChartOptions.plugins, legend: { display: false }, tooltip: { callbacks: { title: (tooltipItems) => { const item = tooltipItems[0]; let label = item.label || ''; const max = 20; let lines = []; for (let i = 0; i < label.length; i += max) { lines.push(label.substring(i, i + max)); } return lines; } } } } }
        });
    }

</script>
</body>
</html>
