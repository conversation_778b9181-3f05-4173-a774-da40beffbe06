from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    v_jg_sql = YYSQLTemplate.wide_stats(
        [],
        "city_id,vehicle_no,vehicle_color,vehicle_type",
        '''
        city_id,
        any(city_short_name) city_name,
        vehicle_no,
        vehicle_color,
        vehicle_type,
        if(max(exception_type)=2,1,0) is_ex
        ''',
        start_day=start_day, end_day=end_day)
    jg_sql = SQLHelper().table(v_jg_sql) \
        .groupBy('city_id,vehicle_type')\
        .select(
        '''
        city_id,vehicle_type,
        any(city_name) city_name,
        count(1) jg_count,
        sum(is_ex) exNum
        '''
    )
    final_sql = SQLHelper().table(jg_sql).groupBy('city_id')\
        .select(
        '''
        city_id,
        any(city_name) city_name,
        maxIf(exNum,vehicle_type =1) ke_exNum, 
        maxIf(jg_count,vehicle_type =1) ke_jg_count, 
        maxIf(exNum,vehicle_type =3) w_exNum, 
        maxIf(jg_count,vehicle_type =3) w_jg_count, 
        maxIf(exNum,vehicle_type =4) z_exNum, 
        maxIf(jg_count,vehicle_type =4) z_jg_count, 
        ke_exNum+w_exNum+z_exNum t_exNum,
        ke_jg_count+w_jg_count+z_jg_count t_jg_count,
        FLOOR(t_exNum/t_jg_count,4) ex_rate
        '''
    )
    final_sql1 = SQLHelper().table(final_sql,'A').leftJoin()\
        .table("gdispx_data.basics_area","B").on('A.city_id = B.id')\
        .select(
        '''
        A.city_id city_id,
        B.name city_name,
        ke_exNum,
        ke_jg_count,
        w_exNum,
        w_jg_count,
        z_exNum,
        z_jg_count,
        t_exNum,
        t_jg_count,
        ex_rate
        '''
    )

    return final_sql1


def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        ne = OrderedDict(zip(
            ('pid', 'city_name',
             'ke_exNum', 'w_exNum',
             'z_exNum', 't_exNum',
             'ex_rate'
             ),
            (e['pid'], e['city_name'],
             e['ke_exNum'], e['w_exNum'],
             e['z_exNum'], e['t_exNum'],
             FLOORtoPercent(e['ex_rate'])
             )
        ))
        outlist.append(ne)
    return outlist


def doSum(inlist, days):
    ke_exNum = sum([e['ke_exNum'] for e in inlist])
    w_exNum = sum([e['w_exNum'] for e in inlist])
    z_exNum = sum([e['z_exNum'] for e in inlist])
    t_exNum = sum([e['t_exNum'] for e in inlist])
    t_jg_count = sum([e['t_jg_count'] for e in inlist])
    ex_rate = FLOORtoPercent(t_exNum/t_jg_count)
    ne = OrderedDict(zip(
        ('pid', 'city_name',
         'ke_exNum', 'w_exNum',
         'z_exNum', 't_exNum',
         'ex_rate'
         ),
        ('合计', '-',
         ke_exNum, w_exNum,
         z_exNum, t_exNum,
         ex_rate
         )
    ))
    return ne


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = orderByCity(datalist)
    outlist = export_outlist_orderDict(outlist)
    se = doSum(datalist, days)
    # h = list(se.keys())
    # otfile = join_path(bpath,'exception_sheet1.csv')
    # outlist.append(se)
    # write_csv(otfile,h,outlist)
    return outlist,se

if __name__ == '__main__':
    '''

        '''
    start, end = TimeUtils.getLastWeekRange()
    load_data(start, end, 7)