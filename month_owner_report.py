from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from collections import OrderedDict


def loadSQL(start_day, end_day,days):
    xjg_vt_sql = f'''
        SELECT 
            count(distinct vehicle_no,vehicle_color) xjg_count,
            toUInt8(vehicle_type) vehicle_type,
            toUInt32(owner_id) owner_id
        from gdispx_data.vehicle_history_yunzheng vhy 
        where create_date BETWEEN '{start_day}'  and '{end_day}'
        group by vehicle_type,owner_id
    '''
    jg_vt_sql1 = f'''
        SELECT 
            owner_id ,vehicle_type ,vehicle_no ,vehicle_color,
            sum(risk_oneLevel)+sum(risk_twoLevel)+sum(risk_threeLevel) riskNum,
            sum(risk_oneLevel) riskOneLevel,
            sum(risk_twoLevel) riskTwoLevel ,
            sum(risk_threeLevel) riskThreeLevel,
            if(max(exception_type)=2 ,1,0) has_exception,
            if(max(third_party_id)>0, 1,0) has_third
        from gdispx_data.v_vehicle_wide_day_all vvwda 
        where inc_day BETWEEN '{start_day}'  and '{end_day}'
        group by owner_id ,vehicle_type ,vehicle_no ,vehicle_color 
    '''
    jg_vt_sql2 = SQLHelper().table(jg_vt_sql1,'D').groupBy('owner_id ,vehicle_type')\
            .having(["vehicle_type != 4 or jg_count > 10"]) \
            .select('''
                owner_id ,vehicle_type ,
                count(1) jg_count,
                sum(riskNum) riskNum,
                sum(riskOneLevel) riskOneLevel,
                sum(riskTwoLevel) riskTwoLevel ,
                sum(riskThreeLevel) riskThreeLevel,
                sum(has_exception) has_exception_count,
                sum(has_third) has_third_count
            ''')
    jg_sql = f'''
        SELECT 
        owner_id,
        any(area_manger_short_name) area_manger_short_name,
        any(owner_name) owner_name,
        ceil(count(distinct vehicle_no)/100) jg_count0,
        if(jg_count0=1,2,jg_count0) need_account
        from gdispx_data.v_vehicle_wide_day_all vvwda 
        where inc_day BETWEEN '{start_day}'  and '{end_day}'
        group by owner_id
    '''
    final_sql1 = SQLHelper().table(jg_vt_sql2 ,'A')\
        .leftJoin().table(xjg_vt_sql,'B').on(' A.owner_id = B.owner_id and A.vehicle_type = B.vehicle_type ')\
        .leftJoin().table(jg_sql,'C').on(' A.owner_id = C.owner_id ')\
        .select('''
            splitByChar(',',area_manger_short_name)[2] city_name,
            splitByChar(',',area_manger_short_name)[3] area_name,
            owner_name,
            A.vehicle_type vehicle_type,
            if(xjg_count<jg_count,jg_count,xjg_count) xjg_count,,
            jg_count,
            need_account,
            riskNum,
            riskOneLevel,
            riskTwoLevel,
            riskThreeLevel,
            has_exception_count,
            has_third_count,
            A.owner_id owner_id
        ''')
    print(final_sql1)

    login_sql1 = f'''
        select
            u.user_id,
            d.owner_id,
            ifnull(L.login_days,0) login_days
        from gdispx.sys_user u
        left join gdispx.sys_user_role ur on u.user_id = ur.user_id
        left join gdispx.sys_role r on ur.role_id = r.role_id
        left join gdispx_basics.basics_owner d on d.owner_id = u.dept_id 
        left join
        (
        select
        user_id,count(distinct date(create_time)) login_days
        from gdispx.sys_log_login
        where date(create_time) between '{start_day}' and '{end_day}'
        group by user_id
        ) L on L.user_id = u.user_id
        where u.tenant_id = 1
				and d.is_delete = 0 
    '''
    login_sql2 = SQLHelper().table(login_sql1).groupBy('owner_id')\
        .select(f'''
            owner_id,
            count(1) open_account,
            sum(if(login_days ={days},1,0 )) everyDay_login_account
        ''')
    # print(login_sql2)






def main(start_day, end_day,days):
    loadSQL(start_day, end_day,days)


if __name__ == '__main__':
    start, end, days = TimeUtils.getLastMonthRange()
    main(start, end,days)
    # print(start,end,days)