from yyutils import *
from collections import OrderedDict
import os
from datetime import datetime, timedelta


bpath = r'/data/report_data/user_day_all'
if not os.path.exists(bpath):
    os.makedirs(bpath)

def from_A_to_B(start, end):
    start_day = datetime.strptime(start, '%Y-%m-%d')
    if not end:
        end = start
    end_day = datetime.strptime(end, '%Y-%m-%d')
    n = 0
    while True:
        one = start_day + timedelta(days=n)
        if one <= end_day:
            n += 1
            yield str(one.strftime('%Y-%m-%d'))
        else:
            break


class UserDayAll:
    def __init__(self, e,report_day):
        self.inc_day = TimeUtils.strptime(report_day, DateFormat.Y_m_d.value)
        self.user_id = e['user_id']
        self.username = e['username'] if e['username'] else ''
        self.realname = e['realname'] if e['realname'] else ''
        self.dept_id = e['dept_id']
        self.dept_name = e.get('dept_name') if e.get('dept_name') else ''
        self.has_idcard = e['has_idcard']
        self.role_id = e['role_id']
        self.role_name = e['role_name'] if e.get('role_name') else ''
        self.tenant_id = e['tenant_id']
        self.open_time = e['open_time']
        self.is_online = e['is_online']
        self.area_level = int(e.get('area_level')) if e.get('area_level') else None
        self.area_id = int(e.get('area_id')) if e.get('area_id') else None
        self.area_name = e['area_name'] if e.get('area_name') else ''
        self.city_id = int(e.get('city_id')) if e.get('city_id') else None
        self.city_name = e['city_name'] if e.get('city_name') else ''
        self.risk_disposeNum = e.get('risk_disposeNum', 0)
        self.risk_miss_report_disposeNum = e.get('risk_miss_report_disposeNum', 0)
        self.risk_audit_reject_disposeNum = e.get('risk_audit_reject_disposeNum', 0)
        self.risk_check_reject_disposeNum = e.get('risk_check_reject_disposeNum', 0)
        self.risk_auditNum = e.get('risk_auditNum', 0)
        self.risk_dispose_reject_auditNum = e.get('risk_dispose_reject_auditNum', 0)
        self.risk_check_reject_auditNum = e.get('risk_check_reject_auditNum', 0)
        self.risk_checkNum = e.get('risk_checkNum', 0)
        self.risk_reject_checkNum = e.get('risk_reject_checkNum', 0)

    def to_dict(self):
        return OrderedDict([
            ('inc_day', self.inc_day),
            ('user_id', self.user_id),
            ('username', self.username),
            ('realname', self.realname),
            ('dept_id', self.dept_id),
            ('dept_name', self.dept_name),
            ('has_idcard', self.has_idcard),
            ('role_id', self.role_id),
            ('role_name', self.role_name),
            ('tenant_id', self.tenant_id),
            ('open_time', self.open_time),
            ('is_online', self.is_online),
            ('area_level', self.area_level),
            ('area_id', self.area_id),
            ('area_name', self.area_name),
            ('city_id', self.city_id),
            ('city_name', self.city_name),
            ('risk_disposeNum', self.risk_disposeNum),
            ('risk_miss_report_disposeNum', self.risk_miss_report_disposeNum),
            ('risk_audit_reject_disposeNum', self.risk_audit_reject_disposeNum),
            ('risk_check_reject_disposeNum', self.risk_check_reject_disposeNum),
            ('risk_auditNum', self.risk_auditNum),
            ('risk_dispose_reject_auditNum', self.risk_dispose_reject_auditNum),
            ('risk_check_reject_auditNum', self.risk_check_reject_auditNum),
            ('risk_checkNum', self.risk_checkNum),
            ('risk_reject_checkNum', self.risk_reject_checkNum)])


def collect_data(report_day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=report_day)
    user_sql = f'''
        select 
        u.user_id ,
        u.username,
        u.realname ,
        u.dept_id ,
        if(u.idcard is not null,1,0 ) has_idcard ,
        ur.role_id ,
        r.role_name ,
        r.role_type ,
        u.tenant_id ,
        u.create_time open_time,
        if(l.user_id is not null, 1, 0) is_online,
        if(ba.level_type != 3, '',ba.id) area_id,
        if(ba.level_type != 3, '',ba.short_name) area_name,
        if(ba.level_type in (2,3), ba.city_id,'') city_id,
        if(ba.level_type in (2,3), ba.city_short,'') city_name,
        ba.level_type area_level
        from gdispx.sys_user u
        left join gdispx.sys_user_role ur on u.user_id = ur.user_id
        left join gdispx.sys_role r on ur.role_id = r.role_id
        left join gdispx_basics.basics_area ba on u.dept_id = ba.id 
        left join 
        (select
          distinct  user_id
        from gdispx.sys_log_login 
        where date(create_time) = '{report_day}' 
        ) l on l.user_id = u.user_id
        where u.del_flag =0 and role_name is not null and date(u.create_time) <= '{report_day}'
    '''

    owner_sql = f'''
        select 
        bo.owner_id dept_id,
        bo.owner_name dept_name,
        if(ba.city_id = ba.id,boa.area_id +1 ,boa.area_id) area_id,
        if(ba.city_id = ba.id,concat(ba.name ,'本级') ,ba.short_name) area_name,
        ba.city_id,
        ba.city_short city_name
        from gdispx_basics.basics_owner bo 
        left join gdispx_basics.basics_owner_area boa 
        on bo.owner_id = boa.owner_id 
        left join gdispx_basics.basics_area ba 
        on boa.area_id = ba.id 
        where bo.is_delete =0
    '''
    third_sql = f'''
        select 
        btp.id dept_id ,
        btp.name dept_name,
        0 area_id,
        '' area_name,
        btp.area_id city_id,
        ba.short_name city_name
        from gdispx_basics.basics_third_party btp 
        left join gdispx_basics.basics_area ba 
        on btp.area_id = ba.id 
        where btp.is_delete =0
    '''
    fac_sql = f'''
        select 
        bf.id dept_id,
        bf.name dept_name,
        0 area_id,
        '' area_name,
        bf.area_id city_id,
        ba.short_name city_name
        from gdispx_basics.basics_facilitator bf 
        left join gdispx_basics.basics_area ba 
        on bf.area_id = ba.id 
        where bf.is_delete =0
    '''
    productor_sql = f'''
        select 
        bp.id dept_id,
        bp.producter_name dept_name,
        0 area_id,
        '' area_name,
        0 city_id,
        '' city_name
        from gdispx_basics.basics_producter bp
        where bp.is_delete =0
    '''
    insurance_sql = f'''
        select 
        bim.id dept_id,
        bim.company dept_name,
        if(ba.city_id = ba.id,bim.area_id +1 ,bim.area_id) area_id,
        if(ba.city_id = ba.id,concat(ba.name ,'本级') ,ba.short_name) area_name,
        ba.city_id ,
        ba.city_short city_name
        from gdispx_basics.basics_insurance_manage bim 
        left join gdispx_basics.basics_area ba 
        on bim.area_id = ba.id 
        where is_delete =0
    '''
    risk_sql = f'''
        SELECT 
        vehicle_no,alarm_time,alarm_id ,
        process_status ,dispose_type ,toInt32(process_user_id) user_id,process_time
        from 
        (SELECT 
        id,vehicle_no,toDate(alarm_time) alarm_time
        from gdispx_data.business_ai_alarm_info baai 
        where toDate(alarm_time) = '{report_day}'
        ) A 
        left join 
        (select 
        alarm_id ,process_status ,dispose_type ,process_user_id ,process_time
        from gdispx_data.business_alarm_audit_info final
        where toDate(create_time) between '{report_day}' and '{risk_dispose_end_day}'
        and alarm_id !=''
        and process_user != ''
        and process_user != '监管系统'
        ) B on A.id = B.alarm_id
        where B.alarm_id != ''
        order by B.alarm_id,B.process_time,B.process_user_id
    '''
    # where toDate(create_time) between '{report_day}' and '{risk_dispose_end_day}'
    db_mysql = DBUtil(DBType.MYSQL.value)
    user_list = db_mysql.query_to_dictList(user_sql)
    owner_list = db_mysql.query_to_dictList(owner_sql)
    third_list = db_mysql.query_to_dictList(third_sql)
    fac_list = db_mysql.query_to_dictList(fac_sql)
    productor_list = db_mysql.query_to_dictList(productor_sql)
    insurance_list = db_mysql.query_to_dictList(insurance_sql)
    db_mysql.close()
    db_ck = DBUtil(DBType.CLICKHOUSE.value)
    risk_list = db_ck.query_to_dictList(risk_sql)
    db_ck.close()
    return user_list, owner_list, third_list, fac_list, productor_list, insurance_list, risk_list


def package_detpInfo(e, de):
    if not de:
        return
    for k, v in de.items():
        e[k] = v


def do_stats_risk(risk_list):
    dispose_stats = {}
    audit_stats = {}
    check_stats = {}
    for g in read_a_group(risk_list, 'alarm_id'):
        d1_g = list(filter(lambda o: o['dispose_type'] == 0, g))
        d2_g = list(filter(lambda o: o['dispose_type'] == 1, g))
        d3_g = list(filter(lambda o: o['dispose_type'] == 7, g))
        if d1_g:
            e1 = d1_g[0]
            user_id = e1['user_id']
            dispose_stats.setdefault(user_id, {
                'risk_disposeNum': 0,
                'risk_miss_report_disposeNum': 0,
                'risk_audit_reject_disposeNum': 0,
                'risk_check_reject_disposeNum': 0
            })
            dispose_stats[user_id]['risk_disposeNum'] += 1
            if e1['process_status'] != 3 or not d2_g:
                continue
            dispose_stats[user_id]['risk_miss_report_disposeNum'] += 1
            e2 = d2_g[0]
            audit_user_id = e2['user_id']
            audit_stats.setdefault(audit_user_id, {
                'risk_auditNum': 0,
                'risk_dispose_reject_auditNum': 0,
                'risk_check_reject_auditNum': 0,
            })
            audit_stats[audit_user_id]['risk_auditNum'] += 1
            if e2['process_status'] == 1:
                dispose_stats[audit_user_id]['risk_audit_reject_disposeNum'] += 1
                audit_stats[audit_user_id]['risk_dispose_reject_auditNum'] += 1
                continue
            if not d3_g:
                continue
            e3 = d3_g[0]
            check_user_id = e3['user_id']
            check_stats.setdefault(check_user_id, {
                'risk_checkNum': 0,
                'risk_reject_checkNum': 0
            })
            check_stats[check_user_id]['risk_checkNum'] += 1
            if e3['process_status'] == 1:
                dispose_stats[user_id]['risk_check_reject_disposeNum'] += 1
                audit_stats[audit_user_id]['risk_check_reject_auditNum'] += 1
                check_stats[check_user_id]['risk_reject_checkNum'] += 1
    return dispose_stats, audit_stats, check_stats


def package_report(user_list, owner_list, third_list,
                   fac_list, productor_list, insurance_list, risk_list,report_day):
    dpid_owner = list_2_dict(owner_list, 'dept_id')
    dpid_fac = list_2_dict(fac_list, 'dept_id')
    dpid_productor = list_2_dict(productor_list, 'dept_id')
    dpid_insrance = list_2_dict(insurance_list, 'dept_id')
    dpid_third = list_2_dict(third_list, 'dept_id')
    dispose_stats, audit_stats, check_stats = do_stats_risk(risk_list)
    outlist = []
    for e in user_list:
        user_id = e['user_id']
        tenant_id = e['tenant_id']
        dept_id = e['dept_id']
        if tenant_id == 1:
            package_detpInfo(e, dpid_owner.get(dept_id))
            if dispose_stats.get(user_id):
                e.update(dispose_stats.get(user_id))
            if audit_stats.get(user_id):
                e.update(audit_stats.get(user_id))
            userDayall = UserDayAll(e,report_day)
            ne = userDayall.to_dict()
            outlist.append(ne)
            continue
        if tenant_id == 3:
            if check_stats.get(user_id):
                e.update(check_stats.get(user_id))
            userDayall = UserDayAll(e,report_day)
            ne = userDayall.to_dict()
            outlist.append(ne)
            continue
        if tenant_id == 4:
            package_detpInfo(e, dpid_fac.get(dept_id))
            userDayall = UserDayAll(e,report_day)
            ne = userDayall.to_dict()
            outlist.append(ne)
            continue
        if tenant_id == 5:
            package_detpInfo(e, dpid_productor.get(dept_id))
            userDayall = UserDayAll(e,report_day)
            ne = userDayall.to_dict()
            outlist.append(ne)
            continue
        if tenant_id == 6:
            package_detpInfo(e, dpid_insrance.get(dept_id))
            if dispose_stats.get(user_id):
                e.update(dispose_stats.get(user_id))
            if audit_stats.get(user_id):
                e.update(audit_stats.get(user_id))
            userDayall = UserDayAll(e,report_day)
            ne = userDayall.to_dict()
            outlist.append(ne)
            continue
        if tenant_id == 7:
            package_detpInfo(e, dpid_third.get(dept_id))
            userDayall = UserDayAll(e,report_day)
            ne = userDayall.to_dict()
            outlist.append(ne)
    return outlist


@countTime("生产当日宽表")
def do_export_cur_day_report(report_day):
    user_list, owner_list, third_list, fac_list, \
    productor_list, insurance_list, risk_list = collect_data(report_day)
    outlist = package_report(user_list, owner_list, third_list,
                             fac_list, productor_list, insurance_list, risk_list,report_day)
    otfile = join_path(bpath, f'{report_day}.csv')

    if outlist:
        h = list(outlist[0].keys())
        write_csv(otfile, h, outlist)
        save_to_db(outlist)
    Logger.instance().info(f"{report_day} 当日用户宽表({len(outlist)})生产完毕.")


def save_to_db(outlist):
    db = DBUtil(DBType.CLICKHOUSE.value)
    for g in read_by_trunk(outlist, 1000):
        insert_sql = f'''
            INSERT into gdispx_data.user_wide_day_all 
            (
              `inc_day`, `user_id`, `username` ,  `realname`,
               `dept_id`, `dept_name`,`has_idcard`,`role_id` ,
               `role_name` ,`tenant_id`,`open_time`, `is_online` ,
               `area_level`, `area_id`, `area_name`,`city_id`,
               `city_name`,`risk_disposeNum` , `risk_miss_report_disposeNum` ,
                `risk_audit_reject_disposeNum` ,  `risk_check_reject_disposeNum` ,
                 `risk_auditNum`, `risk_dispose_reject_auditNum`,`risk_check_reject_auditNum` ,
                 `risk_checkNum` , `risk_reject_checkNum`
             ) values
            '''
        db.excute(insert_sql, g)
    db.close()


def do_inc_update_byDay(day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=day)
    wide_sql = f'''
        select
        inc_day,user_id,username,realname,
        dept_id,dept_name,has_idcard,role_id,role_name,
        tenant_id,open_time,is_online,area_level,
        area_id,area_name,city_id,city_name,
        risk_disposeNum,risk_miss_report_disposeNum,
        risk_audit_reject_disposeNum,risk_check_reject_disposeNum,
        risk_auditNum,risk_dispose_reject_auditNum,
        risk_check_reject_auditNum,risk_checkNum,
        risk_reject_checkNum
        from gdispx_data.user_wide_day_all final
        where inc_day = '{day}'
        and tenant_id in (1,3,6)
    '''
    risk_sql = f'''
            SELECT 
            vehicle_no,alarm_time,alarm_id ,
            process_status ,dispose_type ,toInt32(process_user_id) user_id,process_time
            from 
            (SELECT 
            id,vehicle_no,toDate(alarm_time) alarm_time
            from gdispx_data.business_ai_alarm_info baai 
            where toDate(alarm_time) = '{day}'
            ) A 
            left join 
            (select 
            alarm_id ,process_status ,dispose_type ,process_user_id ,process_time
            from gdispx_data.business_alarm_audit_info final
            where toDate(create_time) between '{day}' and '{risk_dispose_end_day}'
            and alarm_id !=''
            and process_user != ''
            and process_user != '监管系统'
            ) B on A.id = B.alarm_id
            where B.alarm_id != ''
            order by B.alarm_id,B.process_time,B.process_user_id
        '''
    db_ck = DBUtil(DBType.CLICKHOUSE.value)
    wide_list = db_ck.query_to_dictList(wide_sql)
    if not wide_list:
        db_ck.close()
        return
    risk_list = db_ck.query_to_dictList(risk_sql)
    db_ck.close()
    dispose_stats, audit_stats, check_stats = do_stats_risk(risk_list)
    outlist = []
    for e in wide_list:
        user_id = e['user_id']
        if not dispose_stats.get(user_id) and not \
                audit_stats.get(user_id) and not check_stats.get(user_id):
            continue
        tenant_id = e['tenant_id']
        if tenant_id == 3:
            if not check_stats.get(user_id):
                continue
            se = check_stats.get(user_id)
            if e['risk_checkNum'] != se['risk_checkNum'] \
                    or e['risk_reject_checkNum'] != se['risk_reject_checkNum']:
                e['risk_checkNum'] = se['risk_checkNum']
                e['risk_reject_checkNum'] = se['risk_reject_checkNum']
                outlist.append(e)
            continue
        if tenant_id in (1, 6):
            if not dispose_stats.get(user_id) and not audit_stats.get(user_id):
                continue
            has_changed = False
            se1 = dispose_stats.get(user_id)
            se2 = audit_stats.get(user_id)
            if se1 and (e['risk_disposeNum'] != se1['risk_disposeNum'] or
                        e['risk_miss_report_disposeNum'] != se1['risk_miss_report_disposeNum'] or
                        e['risk_audit_reject_disposeNum'] != se1['risk_audit_reject_disposeNum'] or
                        e['risk_check_reject_disposeNum'] != se1['risk_check_reject_disposeNum']):
                has_changed = True
                e['risk_disposeNum'] = se1['risk_disposeNum']
                e['risk_miss_report_disposeNum'] = se1['risk_miss_report_disposeNum']
                e['risk_audit_reject_disposeNum'] = se1['risk_audit_reject_disposeNum']
                e['risk_check_reject_disposeNum'] = se1['risk_check_reject_disposeNum']
            if se2 and (
                    e['risk_auditNum'] != se2['risk_auditNum'] or
                    e['risk_dispose_reject_auditNum'] != se2['risk_dispose_reject_auditNum'] or
                    e['risk_check_reject_auditNum'] != se2['risk_check_reject_auditNum']):
                has_changed = True
                e['risk_auditNum'] = se2['risk_auditNum']
                e['risk_dispose_reject_auditNum'] = se2['risk_dispose_reject_auditNum']
                e['risk_check_reject_auditNum'] = se2['risk_check_reject_auditNum']
            if has_changed:
                outlist.append(e)
    if outlist:
        save_to_db(outlist)
        Logger.instance().info(f"{day} 增量更新{len(outlist)}...")


def do_inc_update_report(report_day):
    t = TimeUtils.strptime(report_day, DateFormat.Y_m_d.value)
    for i in range(11, 0, -1):
        day = TimeUtils.DeltaDay(-i, cur_day=t)
        Logger.instance().info(f"{day} 开始增量更新...")
        do_inc_update_byDay(day)
        Logger.instance().info(f"{day} 增量更新完毕...")


def main(report_day):
    Logger.instance("用户日结", join_path(bpath, 'log.log'))
    do_inc_update_report(report_day)
    do_export_cur_day_report(report_day)


if __name__ == '__main__':
    # report_day = TimeUtils.DeltaDay(-1)
    # main(report_day)
    for day in from_A_to_B('2022-01-01','2022-05-31'):
        main(day)
