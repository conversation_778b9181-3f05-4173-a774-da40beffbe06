from datetime import datetime

import os
import socket
import time
from clickhouse_driver import Client
from datetime import datetime, timedelta
import pandas as pd
import csv

# 获取昨天的日期
yesterday = datetime.now() - timedelta(days=1)

# 格式化日期
yesterday_date = yesterday.strftime("%Y-%m-%d")
yesterday_file = yesterday.strftime("%Y.%m.%d")
sql1 = f"""
        ---轨迹完整率小于等于20%“重型货运”车辆明细（2024.12.21）
        ---轨迹完整率小于等于90%“两客一危”车辆明细（2024.12.21）
        SELECT --vehicle_id,
            any(vehicle_no)as `车牌号`,
            CASE
            when any(vehicle_color) = '1' then '蓝色'
            when any(vehicle_color) = '2' then '黄色'
            when any(vehicle_color) = '3' then '黑色'
            when any(vehicle_color) = '4' then '白色'
            when any(vehicle_color) = '5' then '绿色'
            when any(vehicle_color) = '9' then '其他'
            when any(vehicle_color) = '91' then '农黄色'
            when any(vehicle_color) = '92' then '农绿色'
            when any(vehicle_color) = '93' then '黄绿色'
            when any(vehicle_color) = '94' then '渐变绿色'
            end  as `车牌颜色`,
            CASE
            when any(vehicle_type) = '1' then '客运'
            when any(vehicle_type) = '3' then '危运'
            when any(vehicle_type) = '4' then '重货'
            end as "经营范围",
            any(city_short_name) as `地市`,
            any(area_name) as `区县`,
            any(owner_name) as `业户`,
            any(third_party_name) as `第三方`,
            any(facilitator_name) as `设备服务商`,
            any(master_producter_name) as `主设备厂商`,
            sum(continuous_mileage) as `连续里程数`,sum(total_mileage) as  `总里程数`,
            sum(continuous_mileage)/sum(total_mileage) as `轨迹完整率`
        from vehicle_wide_day_all vwda final
        where inc_day =  '{yesterday_date}'
        and vehicle_type in (1,3)
        --and vehicle_type = 4
        --where inc_day between '2024-01-01' and '2024-01-07'
        --and city_short_name = '深圳'
        group by vehicle_id
        -- ,inc_day
        HAVING sum(continuous_mileage)/sum(total_mileage) <= 0.9 
        -- HAVING sum(continuous_mileage)/sum(total_mileage) <= 0.2 
        and sum(total_mileage) >0

    """
sql2 = f"""
        ---轨迹完整率小于等于20%“重型货运”车辆明细（2024.12.21）
        ---轨迹完整率小于等于90%“两客一危”车辆明细（2024.12.21）
        SELECT --vehicle_id,
            any(vehicle_no)as `车牌号`,
            CASE
            when any(vehicle_color) = '1' then '蓝色'
            when any(vehicle_color) = '2' then '黄色'
            when any(vehicle_color) = '3' then '黑色'
            when any(vehicle_color) = '4' then '白色'
            when any(vehicle_color) = '5' then '绿色'
            when any(vehicle_color) = '9' then '其他'
            when any(vehicle_color) = '91' then '农黄色'
            when any(vehicle_color) = '92' then '农绿色'
            when any(vehicle_color) = '93' then '黄绿色'
            when any(vehicle_color) = '94' then '渐变绿色'
            end  as `车牌颜色`,
            CASE
            when any(vehicle_type) = '1' then '客运'
            when any(vehicle_type) = '3' then '危运'
            when any(vehicle_type) = '4' then '重货'
            end as "经营范围",
            any(city_short_name) as `地市`,
            any(area_name) as `区县`,
            any(owner_name) as `业户`,
            any(third_party_name) as `第三方`,
            any(facilitator_name) as `设备服务商`,
            any(master_producter_name) as `主设备厂商`,
            sum(continuous_mileage) as `连续里程数`,sum(total_mileage) as  `总里程数`,
            sum(continuous_mileage)/sum(total_mileage) as `轨迹完整率`
        from vehicle_wide_day_all vwda final
        where inc_day =  '{yesterday_date}'
        -- and vehicle_type in (1,3)
        and vehicle_type = 4
        --where inc_day between '2024-01-01' and '2024-01-07'
        --and city_short_name = '深圳'
        group by vehicle_id
        -- ,inc_day
        -- HAVING sum(continuous_mileage)/sum(total_mileage) <= 0.9 
        HAVING sum(continuous_mileage)/sum(total_mileage) <= 0.2 
        and sum(total_mileage) >0

    """

class CkDB2:
    def __init__(self):
        self.client = Client(host='**************', port=9000, user='default', password='<EMAIL>',
                             database='gdispx_data', connect_timeout=10, send_receive_timeout=180)

    def get_client(self):
        # 设置发送请求超时时间和接收响应超时时间（以秒为单位�?
        # self.client.execute("SET send_timeout = 300")  # 将发送请求超时时间设置为 300 �?
        # self.client.execute("SET receive_timeout = 300")  # 将接收响应超时时间设置为 300 �?
        return self.client

    def disconnect(self):
        self.client.disconnect()


def ex_sql_with_retry(sql,client):
    # date_str = date_day.strftime('%Y-%m-%d')
    # print(date_str)


    try:
        result_df = client.execute(sql)
        return result_df
    except Exception as e:
        print(f"Error executing query for date {yesterday_date} 1: {e}")
        time.sleep(1)  # 等待1秒后重试


    return None

def guiji_main():
    db2 = CkDB2()
    client2 = db2.get_client()
    result_df1  = ex_sql_with_retry(sql1,client2)
    result_df2 = ex_sql_with_retry(sql2,client2)
    result_df1.to_csv(f'轨迹完整率小于等于90%“两客一危”车辆明细({yesterday_file}).csv', index=False)
    result_df2.to_csv(f'轨迹完整率小于等于20%“重型货运”车辆明细({yesterday_file}).csv', index=False)

if __name__ == '__main__':
    guiji_main()
