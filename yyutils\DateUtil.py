from datetime import datetime, timedelta, date
from enum import Enum
import time


class TimeFormat(Enum):
    Y_m_d_H_M_S = '%Y-%m-%d %H:%M:%S'
    YmdHMS = '%Y%m%d%H%M%S'
    ymdHMS = '%y%m%d%H%M%S'


class DateFormat(Enum):
    Ymd = '%Y%m%d'
    Y_m_d = '%Y-%m-%d'
    ymd = '%y%m%d'
    y_m_d = '%y-%m-%d'


class TimeUtils:
    @staticmethod
    def now():
        return datetime.now()

    @staticmethod
    def Now(format=TimeFormat.Y_m_d_H_M_S.value):
        return datetime.strftime(datetime.now(), format)

    @staticmethod
    def Today(format=DateFormat.Y_m_d.value):
        return datetime.strftime(datetime.now(), format)

    @staticmethod
    def DeltaDay(days=1, format=DateFormat.Y_m_d.value, cur_day=None):
        if not cur_day:
            return datetime.strftime(datetime.now() + timedelta(days=days), format)
        if isinstance(cur_day, str):
            return datetime.strftime(datetime.strptime(cur_day, format) + timedelta(days=days), format)
        return datetime.strftime(cur_day + timedelta(days=days), format)

    @staticmethod
    def strptime(date_string, format=DateFormat.Y_m_d.value) -> datetime:
        return datetime.strptime(date_string, format)

    @staticmethod
    def strftime(dtime, format=DateFormat.Y_m_d.value) -> str:
        return datetime.strftime(dtime, format)

    @staticmethod
    def timeToSeconds(time_string: str = None, format=TimeFormat.Y_m_d_H_M_S.value) -> int:
        if not time_string:
            return int(time.time())
        return int(time.mktime(time.strptime(time_string, format)))

    @staticmethod
    def secondsToTimeStr(seconds, format=TimeFormat.Y_m_d_H_M_S.value):
        return time.strftime(format, time.localtime(int(seconds)))

    @staticmethod
    def getLastWeekRange():
        today = TimeUtils.strptime(TimeUtils.Today())
        w = today.weekday()
        start_delta_n = -w - 7
        end_delta_n = -w - 1
        return TimeUtils.DeltaDay(start_delta_n, cur_day=today), TimeUtils.DeltaDay(end_delta_n, cur_day=today)

    @staticmethod
    def getLastMonthRange():
        today = TimeUtils.strptime(TimeUtils.Today())
        last_day = date(year=today.year, month=today.month, day=1) - timedelta(1)
        first_day = date(year=last_day.year, month=last_day.month, day=1)
        return TimeUtils.strftime(first_day), TimeUtils.strftime(last_day),(last_day-first_day).days+1


if __name__ == '__main__':
    # print(TimeUtils.getLastWeekRange())
    # print(TimeUtils.Today())
    # print(TimeUtils.Now())
    # print(TimeUtils.DeltaDay(-1))
    # print(TimeUtils.strptime('20220101', '%Y%m%d'))
    # print(TimeUtils.strftime(TimeUtils.now()))
    # print(TimeUtils.timeToSeconds())
    # print(TimeUtils.secondsToTimeStr(TimeUtils.timeToSeconds()))
    print(TimeUtils.getLastMonthRange())
