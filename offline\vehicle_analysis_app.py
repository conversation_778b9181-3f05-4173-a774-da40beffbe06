import streamlit as st
import pandas as pd
import altair as alt

# --- 页面配置 ---
st.set_page_config(
    page_title="交互式车辆数据分析看板",
    page_icon="🚗",
    layout="wide"
)

st.title("🚗 可交互的车辆数据分析看板")
st.info("👈 请先在侧边栏 **指定数据列**，然后使用 **筛选控件** 来探索数据。")

# --- 数据加载与预处理 ---
@st.cache_data
def load_data(file_path):
    """
    加载数据，并尝试使用常见的中文编码格式。
    """
    try:
        # 尝试使用 UTF-8 编码
        return pd.read_csv(file_path, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            # 如果 UTF-8 失败，尝试使用 GBK 编码
            return pd.read_csv(file_path, encoding='gbk')
        except Exception as e:
            st.error(f"无法使用 UTF-8 或 GBK 编码读取文件。错误: {e}")
            return None
    except FileNotFoundError:
        st.error(f"错误：找不到文件 '{file_path}'。请确保文件与应用脚本在同一目录下。")
        return None
    except Exception as e:
        st.error(f"加载数据时出错: {e}")
        return None

# --- 主逻辑 ---
file_name = "X:\\track_report\offline\\5月客运车辆离线漂移明细(已过滤).csv"
df_original = load_data(file_name)

if df_original is not None:
    column_list = df_original.columns.tolist()

    with st.sidebar:
        st.header("1. 指定数据列")
        st.write("请将您的CSV文件列名与看板所需的数据项进行匹配。")

        # --- 列名动态匹配 ---
        plate_col = st.selectbox("选择 **车牌号** 对应的列:", column_list, index=0)
        industry_col = st.selectbox("选择 **所属行业** 对应的列:", column_list, index=1 if len(column_list) > 1 else 0)
        location_col = st.selectbox("选择 **车辆属地** 对应的列:", column_list, index=2 if len(column_list) > 2 else 0)
        duration_col = st.selectbox("选择 **离线时长(小时)** 对应的列:", column_list, index=3 if len(column_list) > 3 else 0)
        distance_col = st.selectbox("选择 **漂移距离(米)** 对应的列:", column_list, index=4 if len(column_list) > 4 else 0)

        # --- 创建一个列名映射 ---
        # 使用标准化的内部列名，方便后续处理
        standard_names = {
            '车牌号': plate_col,
            '所属行业': industry_col,
            '车辆属地': location_col,
            '离线时长': duration_col,
            '漂移距离(m)': distance_col,
        }

    # --- 数据处理 ---
    # 检查用户是否为不同数据项选择了重复的列
    if len(set(standard_names.values())) != len(standard_names):
        st.warning("⚠️ **警告**：您为不同的数据类别选择了相同的列。请在侧边栏确保每个类别对应唯一的列。")
        st.stop()

    # 复制DataFrame以进行处理
    df_processed = df_original.copy()

    try:
        # 根据用户的选择重命名列，以便后续代码统一处理
        rename_map = {v: k for k, v in standard_names.items()}
        df_processed.rename(columns=rename_map, inplace=True)

        # 对指定列进行数据清洗和类型转换
        df_processed['离线时长'] = pd.to_numeric(df_processed['离线时长'], errors='coerce')
        df_processed['漂移距离(m)'] = pd.to_numeric(df_processed['漂移距离(m)'], errors='coerce')

        # 删除关键信息缺失的行
        df_processed.dropna(subset=['离线时长', '漂移距离(m)', '所属行业', '车辆属地'], inplace=True)

        # 创建新的计算列（将秒转换为分钟）
        df_processed['离线时长(分钟)'] = df_processed['离线时长'] * 60

        # --- 侧边栏筛选器 ---
        with st.sidebar:
            st.header("2. 筛选数据")
            # 行业选择
            industries = sorted(df_processed['所属行业'].unique())
            selected_industries = st.multiselect("筛选所属行业:", options=industries, default=industries)
            # 车辆属地选择
            locations = sorted(df_processed['车辆属地'].unique())
            selected_locations = st.multiselect("筛选车辆属地:", options=locations, default=locations)
            # 离线时长滑块
            min_dur, max_dur = int(df_processed['离线时长(分钟)'].min()), int(df_processed['离线时长(分钟)'].max())
            selected_duration = st.slider("筛选离线时长 (分钟):", min_dur, max_dur, (min_dur, max_dur))
            # 漂移距离滑块
            min_dist, max_dist = int(df_processed['漂移距离(m)'].min()), int(df_processed['漂移距离(m)'].max())
            selected_distance = st.slider("筛选漂移距离 (米):", min_dist, max_dist, (min_dist, max_dist))

        # --- 数据筛选逻辑 ---
        filtered_df = df_processed[
            (df_processed['所属行业'].isin(selected_industries)) &
            (df_processed['车辆属地'].isin(selected_locations)) &
            (df_processed['离线时长(分钟)'].between(selected_duration[0], selected_duration[1])) &
            (df_processed['漂移距离(m)'].between(selected_distance[0], selected_distance[1]))
            ]

        # --- 创建多视图标签页 ---
        tab1, tab2, tab3 = st.tabs(["📈 数据总览", "🔎 时长与距离分析", "📊 分类统计"])

        if filtered_df.empty:
            st.warning("根据当前筛选条件，没有可显示的数据。请调整侧边栏的筛选器。")
        else:
            with tab1:
                # ... (以下代码与之前版本相同，因为使用的是标准化的列名)
                st.subheader("数据概览与关键指标")
                col1, col2, col3 = st.columns(3)
                col1.metric("离线事件总数", f"{filtered_df.shape[0]:,}")
                col2.metric("涉及车辆总数", f"{filtered_df['车牌号'].nunique():,}")
                avg_duration = filtered_df['离线时长(分钟)'].mean()
                col3.metric("平均离线时长(分钟)", f"{avg_duration:,.2f}")
                st.subheader("筛选后数据详情")
                st.dataframe(filtered_df, use_container_width=True)

            with tab2:
                st.subheader("离线时长与漂移距离关联分析")
                duration_hist = alt.Chart(filtered_df).mark_bar().encode(x=alt.X('离线时长(分钟):Q', bin=alt.Bin(maxbins=10), title='离线时长 (分钟)'),y=alt.Y('count()', title='事件数量')).properties(title='离线时长分布')
                st.altair_chart(duration_hist, use_container_width=True)

                scatter_plot = alt.Chart(filtered_df).mark_circle(size=60, opacity=0.5).encode(x=alt.X('离线时长(分钟):Q', title='离线时长 (分钟)', scale=alt.Scale(zero=False)),y=alt.Y('漂移距离(m):Q', title='漂移距离 (米)', scale=alt.Scale(zero=False)),color=alt.Color('所属行业:N', title='所属行业'),tooltip=['车牌号', '所属行业', '车辆属地', '离线时长(分钟)', '漂移距离(m)']).properties(title='离线时长 vs. 漂移距离').interactive()
                st.altair_chart(scatter_plot, use_container_width=True)

            with tab3:
                st.subheader("按类别统计分析")
                location_chart = alt.Chart(filtered_df).mark_bar().encode(x=alt.X('count()', title='离线事件数量'),y=alt.Y('车辆属地:N', title='车辆属地', sort='-x')).properties(title='各地区离线事件数量排行')
                st.altair_chart(location_chart, use_container_width=True)
                industry_chart = alt.Chart(filtered_df).mark_bar().encode(x=alt.X('count()', title='离线事件数量'),y=alt.Y('所属行业:N', title='所属行业', sort='-x')).properties(title='各行业离线事件数量排行')
                st.altair_chart(industry_chart, use_container_width=True)

    except KeyError as e:
        st.error(f"发生列名错误：`{e}`。请检查您在侧边栏选择的列是否正确，并确保它们真实存在于文件中。")
    except Exception as e:
        st.error(f"处理数据时发生未知错误: {e}")

else:
    st.warning("数据未能成功加载，看板无法显示。")
