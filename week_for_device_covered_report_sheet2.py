from yyutils import *
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT,orderByCityVT
from collections import OrderedDict


def loadSQL(start_day, end_day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=end_day)
    sql = f'''
    
    select 
    vehicle_no,vehicle_color,vehicle_color_name,vehicle_type,vehicle_type_name,
    area_id,ba1.name area_name , ba1.city_id ,ba1.city_short city_name,
    third_party_name,owner_name,except_days
    from 
    (
    SELECT 
        vehicle_no ,
        any(vehicle_type) vehicle_type,
        any(vehicle_color) vehicle_color ,
        dictGetString('gdispx_data.sys_dict_item','label',(toString(vehicle_type),'vehicle_type')) vehicle_type_name,
        dictGetString('gdispx_data.sys_dict_item','label',(toString(vehicle_color),'vehicle_color')) vehicle_color_name,
        any(area_id) area_id,
        any(third_party_name) third_party_name ,
        any(owner_name) owner_name ,
        count(distinct toDate(alarm_time) ) except_days
        from gdispx_data.business_ai_alarm_info baai 
        where toDate(alarm_time) BETWEEN '{start_day}' and '{end_day}'
        and alarm_code = 10405
        and id not in 
        (
        select 
        alarm_id
        from gdispx_data.business_alarm_audit_info baai2 
        where toDate(create_time) BETWEEN '{start_day}' and '{risk_dispose_end_day}'
        and process_status=2 and dispose_type=7
        )
    group by vehicle_no
    order by except_days desc
    ) A 
    left join gdispx_data.basics_area ba1 on toInt32(ba1.id) = toInt32(A.area_id )
    '''
    return sql



def getTop10(datalist):
    city_g = list_2_dictlist(datalist,'city_id')
    outlist = []
    for _,g in city_g.items():
        vt_g = list_2_dictlist(g,'vehicle_type')
        for vt,sg in vt_g.items():
            sg.sort(key=lambda o: - o['except_days'])
            if len(sg) > 10:
                tmplist = list(filter(lambda o:o['except_days']>= sg[9]['except_days'],sg))
            else:
                tmplist = sg[:10]
            outlist.extend(tmplist)
    outlist1 = []
    for e in outlist:
        ne = OrderedDict(zip([
            'city_name','area_name','vehicle_no','vehicle_color',
            'vehicle_type_name','third_party_name','owner_name','except_days','vehicle_type'
        ],[
            e['city_name'],e['area_name'],e['vehicle_no'],
            e['vehicle_color_name'],
            e['vehicle_type_name'],e['third_party_name'],
            e['owner_name'] , e['except_days'],e['vehicle_type']
        ]))
        outlist1.append(ne)
    outlist = orderByCityVT(outlist1,pid=False)
    for e in outlist:
        e.pop('vehicle_type')
    return outlist



def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = getTop10(datalist)
    return outlist

if __name__ == '__main__':
    '''

        '''
    start, end = TimeUtils.getLastWeekRange()
    load_data(start, end, 7)