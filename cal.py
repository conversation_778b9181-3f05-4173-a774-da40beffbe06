#!/usr/bin/env python
# -*- coding: utf-8 -*-
import math

# 速度阈值160km/h
SPEED_MAX = 160
# 地址半径
EARTH_RADIUS = 6378.137
INF = float('inf')


def cal(track_points):
    """
    track_points内每个点格式为dict(x: 112.32343, y: 23.34343, time: 1628151798)
    先根据gps_time进行正向排序, 然后两两计算距离,速度
    :return: {漂移次数,连续里程数(km),总里程数(km)}
    """
    total_point_count = len(track_points)  # 总点数
    drift_point_count = 0  # 漂移点数
    # 去掉异常点（经纬度为0）
    #track_points = [p for p in track_points if p['x'] > 0 and p['y'] > 0]
    track_points = [p for p in track_points if p['x'] > 0 and p['y'] > 0]
    # 第一轮循环,计算漂移点
    track_points.sort(key=lambda x: x['time'])  # 排序
    track_point_count = len(track_points)
    if track_point_count > 0:
        last_point = track_points[0]  # 上一个点
    i = 1
    # print(last_point)
    drift_points = []
    while i < track_point_count:
        cur_point = track_points[i]  # 当前点
        distance = cp_dist(last_point['x'], last_point['y'], cur_point['x'], cur_point['y'])  # 当前点和上一点距离
        speed = cp_speed(distance, (cur_point['time'] - last_point['time']).seconds)  # 当前点速度(m/s)
        cur_point['dist'] = distance
        cur_point['speed'] = speed
        # cur_point['cost'] = (cur_point['time'] - last_point['time']).seconds

        if speed > SPEED_MAX and i + 1 < track_point_count:
            next_point = track_points[i + 1]  # 下一个点
            distance = cp_dist(cur_point['x'], cur_point['y'], next_point['x'], next_point['y'])
            speed = cp_speed(distance, (next_point['time'] - cur_point['time']).seconds)
            next_point['dist'] = distance
            next_point['speed'] = speed
            # next_point['cost'] = (next_point['time'] - cur_point['time']).seconds
            if speed > SPEED_MAX and i + 1 < track_point_count:  # 前后2段的速度都大于阈值, 则该点位漂移点
                cur_point['flag'] = True
                cur_point['drift_distance'] = cur_point['dist']
                drift_points.append(cur_point)
                drift_point_count += 1
                i += 1
                # print(cur_point)
            else:
                # print(cur_point)
                # print(next_point)
                last_point = next_point
                i += 2
        else:
            last_point = cur_point
            i += 1
            # print(cur_point)

    # 第二轮循环,计算里程
    continuous_mileage = 0.0  # 连续里程
    total_mileage = 0.0  # 总里程
    cnt = 0  # 计数器
    for track_point in track_points[1:]:
        # 过滤掉漂移掉
        if 'flag' in track_point and track_point['flag']:
            continue
        distance = track_point['dist']
        total_mileage += distance
        if distance <= 2:
            continuous_mileage += distance
            cnt = 0
        elif distance > 10:
            cnt = 0
        else:
            cnt += 1
            if cnt <= 5:
                continuous_mileage += distance

    return {'total_point_count': total_point_count,
            'error_point_count': total_point_count - track_point_count,
            'drift_point_count': drift_point_count,
            'continuous_mileage': round(continuous_mileage, 3),
            'total_mileage': round(total_mileage, 3)},drift_points


def cp_speed(d, t):
    """
    计算速度(km/h)
    :param d: 距离(km)
    :param t: 时间(s)
    :return: 速度
    """
    if t == 0:
        return 0 if d <= 20 else INF
    else:
        return round(d * 3600 / t, 3)


def rad(d):
    """to弧度"""
    return d * math.pi / 180.0


def cp_dist(lng1, lat1, lng2, lat2):
    """通过经纬度计算距离(km)"""
    try:
        radLat1 = rad(float(lat1))
        radLat2 = rad(float(lat2))
        a = radLat1 - radLat2
        b = rad(float(lng1)) - rad(float(lng2))
        s = 2 * math.asin(
            math.sqrt(math.pow(math.sin(a / 2), 2) + math.cos(radLat1) * math.cos(radLat2) * math.pow(math.sin(b / 2), 2)))
        s = s * EARTH_RADIUS
        return round(s, 3)
    except:
        return 0


def to_geo_dist(distance):
    """距离转地球球面距离"""
    return distance / 60 * 6371000

