import sys

from yyutils import *
from cal import cal
from datetime import datetime,timedelta

def from_A_to_B(start, end):
    start_day = datetime.strptime(start, '%Y-%m-%d')
    if not end:
        end = start
    end_day = datetime.strptime(end, '%Y-%m-%d')
    n = 0
    while True:
        one = start_day + timedelta(days=n)
        if one <= end_day:
            n += 1
            yield str(one.strftime('%Y-%m-%d'))
        else:
            break

bpath = r'/data/report_data/track_report_new'


def check_is_time_out(report_day):
    report_time = TimeUtils.strptime(report_day, DateFormat.Y_m_d.value)
    now = TimeUtils.now()
    if now.hour - report_time.hour > 9:
        return True
    return False


def load_jg(otfile, report_day):
    is_time_out = check_is_time_out(report_day)
    if is_time_out:
        sql = f'''
                select distinct vehicle_no,vehicle_color,master_sim_code
                from gdispx_data.vehicle_history_jianguan 
                where toDate(create_date) = '{report_day}'
            '''
    else:
        sql = f'''
            SELECT  distinct
            vehicle_no,vehicle_color,master_sim_code
            from gdispx_data.vehicle_related_infos_all 
            where (vehicle_type in ('1', '2') and service_result in ('1', '0', '-1'))
		   or (vehicle_type in ('3', '4') and service_result in ('1'))
        '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    db.query_to_csv(sql, otfile)
    db.excute(f"alter table gdispx_data.track_report_new delete where inc_day = '{report_day.replace('-','')}' ")
    db.close()


def query_track_infos(scyllaDB, imei, day):
    sql = f'''
        select gps_time, latitude, longitude from dhiot.gps_data_{day} where imei = '{imei}'
    '''
    rows = scyllaDB.query(sql)
    outlist = []
    for row in rows:
        try:
            ne = {
                'x': row.longitude / 1000000,
                'y': row.latitude / 1000000,
                'time': TimeUtils.strptime(row.gps_time, TimeFormat.ymdHMS.value)
            }
        except Exception as ex:
            continue
        outlist.append(ne)
    return outlist


def insert_results_to_ck(items,drift_points):
    insert_sql = f'''
        INSERT into gdispx_data.track_report_new (`inc_day`, `vehicle_no`, `vehicle_color`,
        `master_sim_code`,
        `total_point_count`, `error_point_count`, `drift_point_count`,
        `continuous_mileage`, `total_mileage`) values
    '''
    drift_insert_sql =f'''
        insert into gdispx_data.track_drift_points_detail ( 
        `vehicle_no`,`vehicle_color`,`master_sim_code`,`inc_day`,`gps_time`,`speed`,`drift_distance`,`x`,`y`
        ) values
    '''
    try:
        ckDB = DBUtil(DBType.CLICKHOUSE.value)
        ckDB.excute(insert_sql, items)
        if drift_points:
            for g in read_by_trunk(drift_points, 1000):
                ckDB.excute(drift_insert_sql, g)
        ckDB.close()
    except Exception as ex:
        Logger.instance().info("漂移数据入库失败...")



def run(args):
    try:
        inlist, day, process_id, log_info = args
        Logger.instance().info(f"{log_info} 开始轨迹计算...")
        # scyllaDB = DBUtil(DBType.SCYLLA.value)
        trackDB = DBUtil(DBType.CLICKHOUSE.value,{
            'host':'**************',
            'port': 9000,
            'database':'gdispx_data',
            'user':'default',
            'password':'<EMAIL>'
        })
        inc_day = day.replace('-', '')
        inlist = list(filter(lambda o:o['master_sim_code'],inlist))
        outlist = []
        drift_points = []
        for g in read_by_trunk(inlist,100):
            sim_vehicle = list_2_dict(g,'master_sim_code')
            sims = ','.join(["'"+e['master_sim_code']+"'" for e in g])
            sql = f'''
            select 
            sim_code,longitude as x,latitude as y,gps_time as time
            from gdispx_data.business_raw_gps_info_all
            where sim_code in ({sims})
            and toDate(gps_time) = '{day}'
            order by sim_code,time
            '''
            datalist = trackDB.query_to_dictList(sql)
            for sg in read_a_group(datalist,'sim_code'):
                e = sim_vehicle.get(sg[0]['sim_code'])
                e['inc_day'] = inc_day
                try:
                    e['vehicle_color'] = int(e['vehicle_color']) if e['vehicle_color'] else 0
                    ret, cur_drift_points = cal(sg)
                    ret.update(e)
                    outlist.append(ret)
                    for d in cur_drift_points:
                        d['inc_day'] = TimeUtils.strptime(day,DateFormat.Y_m_d.value)
                        d['gps_time'] = d['time']
                        d['vehicle_no'] = e['vehicle_no']
                        d['vehicle_color'] = int(e['vehicle_color']) if e['vehicle_color'] else 0
                        d['master_sim_code'] = e['master_sim_code']
                    drift_points.extend(cur_drift_points)
                except Exception as ex:
                    Logger.instance().info(ex)
                    Logger.instance().info(f'车辆{e["vehicle_no"]}设备号{e["master_sim_code"]}计算异常')
        trackDB.close()
        if outlist:
            insert_results_to_ck(outlist,drift_points)
        Logger.instance().info(f"{log_info} 完成轨迹计算!")
    except Exception as ex:
        print(ex)


def main(report_day):
    Logger.instance("轨迹日结", join_path(bpath, 'track_log.log'))
    vfile = join_path(bpath, f'vehicle_{report_day}.csv')
    load_jg(vfile, report_day)
    inlist = read_csv(vfile)
    try:
        runner = ProcessRunner(run, pro_count=8)
        runner.create_params_by_inlist(inlist, 1000, report_day)
        runner.run()
    except Exception as ex:
        print(ex)
    Logger.instance().info("轨迹日结汇总完毕")





if __name__ == '__main__':
    # report_day = TimeUtils.DeltaDay(-1)
    for report_day in ['2023-07-11','2023-07-16']:
        main(report_day)
