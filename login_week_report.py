from yyutils import *
from utils.YYSQLTemplate import YYSQLTemplate
from utils.config import orderByCityVT, orderByCity, vehicle_type_dict
from collections import OrderedDict


def do_third_user_login_stats(start_day, end_day):
    v_sql = f'''
            select
            vehicle_no,
            vehicle_color,
            third_party_id,
            any(third_party_name) third_party_name
        from
            gdispx_data.vehicle_wide_day_all final
        where
            inc_day BETWEEN '{start_day}' and '{end_day}'
        group by vehicle_no,vehicle_color,third_party_id
        '''
    ck_db = DBUtil(DBType.CLICKHOUSE.value)
    vlist = ck_db.query_to_dictList(v_sql)
    ck_db.close()
    third_user_login_sql = f'''
                select
                    ba.city_short city_name,
                    u.user_id,
                    u.dept_id,
                    d.id third_party_id,
        			d.name third_party_name,
                    u.idcard,
                    r.role_type,
                    u.tenant_id,
                    ifnull(L.login_days,0) login_days
                from gdispx.sys_user u
                left join gdispx.sys_user_role ur on u.user_id = ur.user_id
                left join gdispx.sys_role r on ur.role_id = r.role_id
                right join gdispx_basics.basics_third_party d on d.id = u.dept_id 
                left join gdispx_basics.basics_area ba on d.area_id = ba.id
                left join
                (
                select
                user_id,count(distinct date(create_time)) login_days
                from gdispx.sys_log_login 
                where date(create_time) between '{start_day}' and '{end_day}'
                group by user_id
                ) L on L.user_id = u.user_id
                where u.tenant_id = 6
        			and d.is_delete = 0 
            '''
    mysql_db = DBUtil(DBType.MYSQL.value)
    third_list = mysql_db.query_to_dictList(third_user_login_sql)
    mysql_db.close()
    tid_g = list_2_dictlist(vlist, 'third_party_id')
    dp_g = list_2_dictlist(third_list, 'dept_id')
    pid = 0
    outlist = []
    for dept_id, g in dp_g.items():
        id = g[0]['third_party_id']
        vn = len(tid_g.get(id, []))
        city_name = g[0]['city_name']
        total_n = len(g)
        name = g[0]['third_party_name']
        tlist1 = list(filter(lambda o: o['role_type'] == 1, g))
        n1 = len(tlist1)
        tlist2 = list(filter(lambda o: o['idcard'], tlist1))
        n2 = len(tlist2)
        n3, n4, n5, n6 = 0, 0, 0, 0
        for e in tlist1:
            if e['login_days'] == 7:
                n3 += 1
            elif e['login_days'] in (4, 5, 6):
                n4 += 1
            elif e['login_days'] in (1, 2, 3):
                n5 += 1
            else:
                n6 += 1
        pid += 1
        ne = OrderedDict()
        ne['pid'] = 0
        ne['city_name'] = city_name
        ne['third_party_name'] = name
        ne['jg_count'] = vn
        ne['total_acc_count'] = total_n
        ne['running_acc_count'] = n1
        # ne['已实名认证的账号数'] = n2
        ne['login_7'] = n3
        ne['login_4_6'] = n4
        ne['login_1_3'] = n5
        ne['login_none'] = n6
        outlist.append(ne)
    outlist = orderByCity(outlist, secondary_order='jg_count')
    return outlist


def check_has_vt13(vlist):
    if not vlist:
        return None
    tlist = list(filter(lambda o: o['vehicle_type'] in (1, 3), vlist))
    if tlist:
        return tlist[0]['vehicle_type']
    return None


def do_stats_accounts(logins):
    tlist1 = list(filter(lambda o: o['role_type'] == 1, logins))
    running_account = len(tlist1)
    card_account = len(list(filter(lambda o: o['idcard'], tlist1)))
    n_7, n_4_6, n_1_3, n_0 = 0, 0, 0, 0
    for e in tlist1:
        if e['login_days'] == 7:
            n_7 += 1
        elif e['login_days'] in (4, 5, 6):
            n_4_6 += 1
        elif e['login_days'] in (1, 2, 3):
            n_1_3 += 1
        else:
            n_0 += 1
    return running_account, card_account, n_7, n_4_6, n_1_3, n_0


def check_is_fully_agent_consigned(jg_list):
    tlist = list(filter(lambda o: o['third_party_id'], jg_list))
    if len(tlist) < len(jg_list):
        return '否'
    return ', '.join(set([e['third_party_name'] for e in tlist]))


def do_owner_user_login_stats(start_day, end_day):
    v_sql = f'''
            select
            owner_id,vehicle_no,
            any(vehicle_type) vehicle_type,
            any(third_party_id) third_party_id,
            any(third_party_name) third_party_name
        from
            gdispx_data.v_vehicle_wide_day_all
        where
            inc_day BETWEEN '{start_day}' and '{end_day}'
        group by owner_id,vehicle_no
        '''
    xjg_sql = YYSQLTemplate.xjg_stats([],
                                      "owner_id",
                                      " owner_id, count(distinct vehicle_no,vehicle_color) xjg_count ",
                                      start_day, end_day)
    ck_db = DBUtil(DBType.CLICKHOUSE.value)
    vlist = ck_db.query_to_dictList(v_sql)
    xjgList = ck_db.query_to_dictList(xjg_sql)
    ck_db.close()
    owner_user_login_sql = f'''
                select
                ba.city_short city_name,
                u.user_id,
                u.dept_id,
                d.owner_id,
                d.owner_no,
    			d.owner_name,
    			d.scope_details,
                u.idcard,
                r.role_type,
                u.tenant_id,
                ifnull(L.login_days,0) login_days
            from gdispx.sys_user u
            left join gdispx.sys_user_role ur on u.user_id = ur.user_id
            left join gdispx.sys_role r on ur.role_id = r.role_id
            left join gdispx_basics.basics_owner d on d.owner_id = u.dept_id 
            left join gdispx_basics.basics_owner_area oa on oa.owner_id = d.owner_id
            left join gdispx_basics.basics_area ba on ba.id = oa.area_id
            left join
            (
            select
            user_id,count(distinct date(create_time)) login_days
            from gdispx.sys_log_login 
            where date(create_time) between '{start_day}' and '{end_day}'
            group by user_id
            ) L on L.user_id = u.user_id
            where u.tenant_id = 1
    				and d.is_delete = 0 
        '''
    mysql_db = DBUtil(DBType.MYSQL.value)
    owner_user_login_list = mysql_db.query_to_dictList(owner_user_login_sql)
    mysql_db.close()
    id_jg = list_2_dictlist(vlist, 'owner_id')
    id_xjg = list_2_dict(xjgList, 'owner_id')
    id_logins = list_2_dictlist(owner_user_login_list, 'owner_id')
    outlist = []
    for owner_id, logins in id_logins.items():
        vehicle_type = check_has_vt13(id_jg.get(owner_id))
        if not vehicle_type:
            continue
        jg_list = id_jg.get(owner_id)
        jg_count = len(jg_list)
        xjg_e = id_xjg.get(owner_id)
        xjg_count = xjg_e['xjg_count'] if xjg_e else len(jg_list)
        running_account, card_account, n_7, n_4_6, n_1_3, n_0 = do_stats_accounts(logins)
        third_party_name = check_is_fully_agent_consigned(jg_list)
        need_account = int(jg_count / 100) if jg_count % 100 == 0 else int(jg_count / 100) + 1
        if need_account < 2:
            need_account = 2
        ne = OrderedDict()
        ne['city_name'] = logins[0]['city_name']
        ne['owner_no'] = logins[0]['owner_no']
        ne['owner_name'] = logins[0]['owner_name']
        ne['third_party_name'] = third_party_name
        ne['vehicle_type'] = vehicle_type_dict.get(vehicle_type)
        ne['xjg_count'] = xjg_count if xjg_count > jg_count else jg_count
        ne['jg_count'] = jg_count
        ne['need_account'] = need_account
        ne['running_account'] = running_account
        ne['card_account'] = card_account
        ne['login_7'] = n_7
        ne['login_4_6'] = n_4_6
        ne['login_1_3'] = n_1_3
        ne['login_none'] = n_0
        outlist.append(ne)
    outlist = orderByCityVT(outlist, third_order="xjg_count", pid=False)
    return outlist


def main(start_day, end_day):
    third_user_login_list = do_third_user_login_stats(start_day, end_day)
    owner_user_login_list = do_owner_user_login_stats(start_day, end_day)
    return third_user_login_list, owner_user_login_list


if __name__ == '__main__':
    start, end = TimeUtils.getLastWeekRange()
    main(start, end)
