#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB, MySqlDB


def get_city_stat_ck_sql(begin_date, end_date):
    query_sql = """
    select b.city_id
    ,dictGet('gdispx_data.basics_area', 'city_short', (toInt32(any(a.area_id)),toInt32(440000))) as `所属地市`
    ,sum(a.jg_cnt) as `系统监控车辆总数`
    ,sum(b.firstAlarmNum) as `一级风险数`
    ,sum(b.secondAlarmNum) as `二级风险数`
    ,sum(b.thirdAlarmNum) as `三级风险数`
    ,sum(b.allAlarmNum) as `合计风险数`
    ,sum(c.fxjq_db_cnt) as `督办风险警情数`
    , -- ,sum(c.fxjq_cnt) as `风险警情总数`
    sum(b.monitorIntervene) as `风险警情总数`
    ,sum(c.fxjq_cz_cnt) as `风险警情处置数`
    ,round(sum(fxjq_cz_cnt)/sum(monitorIntervene)*100,2) as `风险警情处置率`
    ,sum(d.xfzl_cnt) as `下发指令数量`
    from 
    (SELECT toUInt32(area_id) area_id
    , SUM(online_num) as jg_cnt
    FROM gdispx_data.v_tree_area_sum group by area_id) a
    
    left join (SELECT sad.area_id, sad.city_id
    ,SUM(sad.alarmNum) AS allAlarmNum
    ,SUM(sad.oneLevel) AS firstAlarmNum
    ,SUM(sad.twoLevel) AS secondAlarmNum
    ,SUM(sad.threeLevel) AS thirdAlarmNum
    ,SUM(sad.monitorIntervene) AS monitorIntervene
    FROM gdispx_data.statistics_alarm_day AS sad
    where toDate(sad.alarm_time) >= toDate('{begin_date}') and toDate(sad.alarm_time) < toDate('{end_date}')
    and sad.vehicle_type in (1,2,3,4)
    GROUP BY sad.area_id, sad.city_id) b on a.area_id = b.area_id
    
    left join (SELECT t.area_id
    -- ,count() as fxjq_cnt
    -- ,countIf(a.dispose_type = 3) as fxjq_db_cnt
    -- ,countIf(a.dispose_type = 0) as fxjq_cz_cnt
    ,count(distinct if(a.dispose_type = 3, t.id, null)) as fxjq_db_cnt
    ,count(distinct if(a.dispose_type = 0, t.id, null)) as fxjq_cz_cnt
    FROM gdispx_data.business_ai_alarm_info AS t
    join (select distinct alarm_id, dispose_type from gdispx_data.business_alarm_audit_info 
         where toDate(process_time) >= toDate('{begin_date}')) AS a on t.id = a.alarm_id
    WHERE t.create_time >= toDate('{begin_date}') and t.create_time < toDate('{end_date}') and t.monitor_intervene = 1
    GROUP BY t.area_id) c on a.area_id = c.area_id
    
    left join (SELECT area_id
    , count() as xfzl_cnt
    FROM gdispx_data.business_message_log
    where create_time >= toDate('{begin_date}') and create_time < toDate('{end_date}') and type = 1 
    group by area_id) d on a.area_id = d.area_id
    
    group by b.city_id
    """
    return query_sql.format(begin_date=begin_date, end_date=end_date)


def get_city_stat_mysql_sql(begin_date, end_date):
    query_sql = """
    select a.city_id
    , -- , a.city_short as `所属城市`
    a.login_cnt as `本周登录账号数`
    ,sum(b.cg_cnt) as `查岗次数`
    ,sum(b.cgxy_cnt) as `查岗响应次数`
    ,round(sum(b.cgxy_cnt)/sum(b.cg_cnt)*100,2) as `查岗响应率`
    
    from 
    (SELECT ba.city_short,ba.city_id,su.area_id,count(DISTINCT su.user_id) login_cnt
    from 
    (select su.user_id, if(su.tenant_id = 1, boa.area_id, su.dept_id) area_id
        from gdispx.sys_user su 
        left join gdispx_basics.basics_owner bo on su.dept_id = bo.owner_id 
        left join gdispx_basics.basics_owner_area boa on boa.owner_id = bo.owner_id 
        where su.tenant_id in (1, 3)) su 
    join gdispx_basics.basics_area ba on ba.id = su.area_id 
    join gdispx.sys_log sl on su.user_id  = sl.user_id
    WHERE sl.create_time >= '{begin_date}' and sl.create_time < '{end_date}'  
    group by su.area_id) a
    
    left join (SELECT area_id
    ,count(1) as cg_cnt
    ,sum(if(answer_user is not null, 1, 0)) as cgxy_cnt
    FROM gdispx_basics.business_inquire_station 
    where inquire_time >= '{begin_date}' and inquire_time < '{end_date}'
    GROUP BY area_id) b on a.area_id = b.area_id
    
    group by a.city_id
    """
    return query_sql.format(begin_date=begin_date, end_date=end_date)


def get_owner_stat_ck_sql(begin_date, end_date):
    query_sql = """
    select a.owner_id
    ,dictGet('gdispx_data.basics_area', 'city_short', (a.area_id,toInt32(440000))) as `所属地市`
    ,a.name as `所属企业`
    ,a.jg_cnt as `系统监控车辆总数`
    ,b.firstAlarmNum as `一级风险数`
    ,b.secondAlarmNum as `二级风险数`
    ,b.thirdAlarmNum as `三级风险数`
    ,b.allAlarmNum as `合计风险数`
    ,c.fxjq_db_cnt as `督办风险警情数`
    , -- ,c.fxjq_cnt as `风险警情总数`
    b.monitorIntervene as `风险警情总数`
    ,c.fxjq_cz_cnt as `风险警情处置数`
    ,round(fxjq_cz_cnt/monitorIntervene*100,2) as `风险警情处置率`
    ,d.xfzl_cnt as `下发指令数量`
    from 
    (SELECT toUInt32(owner_id) owner_id, name, pid as area_id
    , SUM(online_num) as jg_cnt
    FROM gdispx_data.v_tree_owner_sum group by owner_id, name, pid) a
    
    left join (select sad.owner_id, sad.area_id
    ,SUM(sad.alarmNum) AS allAlarmNum
    ,SUM(sad.oneLevel) AS firstAlarmNum
    ,SUM(sad.twoLevel) AS secondAlarmNum
    ,SUM(sad.threeLevel) AS thirdAlarmNum
    ,SUM(sad.monitorIntervene) AS monitorIntervene
    FROM gdispx_data.statistics_alarm_day AS sad
    where toDate(sad.alarm_time) >= toDate('{begin_date}') and toDate(sad.alarm_time) < toDate('{end_date}')
    and sad.vehicle_type in (1,2,3,4)
    GROUP BY sad.owner_id, sad.area_id) b on a.owner_id = b.owner_id and a.area_id = toInt32(b.area_id)
    
    left join (SELECT t.owner_id, t.area_id
    -- ,count() as fxjq_cnt
    -- ,countIf(a.dispose_type = 3) as fxjq_db_cnt
    -- ,countIf(a.dispose_type = 0) as fxjq_cz_cnt
    ,count(distinct if(a.dispose_type = 3, t.id, null)) as fxjq_db_cnt
    ,count(distinct if(a.dispose_type = 0, t.id, null)) as fxjq_cz_cnt
    FROM gdispx_data.business_ai_alarm_info AS t
    join (select distinct alarm_id, dispose_type from gdispx_data.business_alarm_audit_info 
         where toDate(process_time) >= toDate('{begin_date}')) AS a on t.id = a.alarm_id
    WHERE t.create_time >= toDate('{begin_date}') and t.create_time < toDate('{end_date}') and t.monitor_intervene = 1
    GROUP BY t.owner_id, t.area_id) c on a.owner_id = c.owner_id and a.area_id = toInt32(c.area_id)
    
    left join (SELECT owner_id, area_id
    , count() as xfzl_cnt
    FROM gdispx_data.business_message_log
    where create_time >= toDate('{begin_date}') and create_time < toDate('{end_date}') and type = 1 
    group by owner_id, area_id) d on a.owner_id = d.owner_id and a.area_id = toInt32(d.area_id)
    """
    return query_sql.format(begin_date=begin_date, end_date=end_date)


def get_owner_stat_mysql_sql(begin_date, end_date):
    query_sql = """
    select a.owner_id
    , -- 。a.owner_name as `所属企业`
    a.login_cnt as `本周登录次数`
    ,b.cg_cnt as `查岗次数`
    ,b.cgxy_cnt as `查岗响应次数`
    ,round(b.cgxy_cnt/b.cg_cnt*100,2) as `查岗响应率`
    
    from 
    (SELECT bo.owner_id, bo.owner_name
    ,COUNT(1) login_cnt
    FROM gdispx.sys_user su
    join gdispx_basics.basics_owner bo on bo.owner_id = su.dept_id
    join gdispx.sys_log sl on su.user_id  = sl.user_id
    WHERE sl.create_time >= '{begin_date}' and sl.create_time < '{end_date}' and su.tenant_id = 1
    GROUP BY bo.owner_id) a
    
    left join (SELECT owner_id
    ,count(1) as cg_cnt
    ,sum(if(answer_user is not null, 1, 0)) as cgxy_cnt
    FROM gdispx_basics.business_inquire_station 
    where inquire_time >= '{begin_date}' and inquire_time < '{end_date}'
    GROUP BY owner_id) b on a.owner_id = b.owner_id
    """
    return query_sql.format(begin_date=begin_date, end_date=end_date)


def query_mysql_then_to_dict(query_sql, key, mysql_cursor):
    mysql_cursor.execute(query_sql)
    rows = mysql_cursor.fetchall()
    results = {}
    row0 = rows[0]
    value0 = row0.pop(key)
    cols = ',' + ','.join(row0.keys())
    results[value0] = ',' + ','.join([str(x) if x or x == 0 else '' for x in row0.values()])
    for row in rows[1:]:
        value = row[key]
        row.pop(key)
        results[value] = ',' + ','.join([str(x) if x or x == 0 else '' for x in row.values()])
    return cols, results


def query_then_write_csv(ck_query_sql, mysql_query_sql, join_key, ck_client, mysql_cursor, output_file):
    ck_rows = ck_client.execute(ck_query_sql, with_column_types=True)
    mysql_cols, mysql_results = query_mysql_then_to_dict(mysql_query_sql, join_key, mysql_cursor)
    output_file.write(','.join([col[0] for col in ck_rows[1][1:]]) + mysql_cols + '\n')
    none_str = ',' * mysql_cols.count(',')
    for row in ck_rows[0]:
        value = row[0]
        output_file.write(','.join([str(x) for x in list(row)[1:]]) + mysql_results.get(value, none_str) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    mysql = MySqlDB()
    try:
        ck_client = ck.get_client()
        mysql_cursor = mysql.get_cursor()

        output_file_name = '车辆智能监管系统运行情况监测表（周报表）_{}_{}.csv'.format(begin_date.replace('-', '')[-4:],
                                                                   end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            output_file.write('广东省“两客一危一重”车辆智能监管系统运行情况监测表（周报表）\n')
            query_then_write_csv(get_city_stat_ck_sql(begin_date, end_date),
                                 get_city_stat_mysql_sql(begin_date, end_date),
                                 'city_id', ck_client, mysql_cursor, output_file)

        output_file_name = '试点企业系统使用及风险处置监测表（周报表）_{}_{}.csv'.format(begin_date.replace('-', '')[-4:],
                                                                    end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            output_file.write('试点企业系统使用及风险处置监测表（周报表）\n')
            query_then_write_csv(get_owner_stat_ck_sql(begin_date, end_date),
                                 get_owner_stat_mysql_sql(begin_date, end_date),
                                 'owner_id', ck_client, mysql_cursor, output_file)
    finally:
        ck.disconnect()
        mysql.close()


if __name__ == '__main__':
    from datetime import datetime, timedelta
    today = datetime.today()
    # 一周之前
    begin_date = (today - timedelta(days=7)).strftime('%Y-%m-%d')
    end_date = today.strftime('%Y-%m-%d')
    export(begin_date, end_date)

