#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

GROUP_BY_WHOLE = 0
GROUP_BY_CITY = 1
GROUP_BY_FACILITATOR = 2
GROUP_BY_PRODUCTER = 3


def export_whole():
    query_sql = """
    select b.`车辆类型`, b.`需监管车辆`, a.`已监管车辆`,a.`入网车辆`,a.`上线车辆` from 
    (select
            transform(vrvri.vehicle_type, ['1','3','4'], ['客运车辆','危险货运','重型货车'])as `车辆类型`
            , count() as `已监管车辆`
            , countIf(bg.device_id != '') as `入网车辆`
            , countIf(bg.gps_time > addDays(now(),-7)) as `上线车辆`
        from gdispx_data.v_regulator_vehicle_related_infos vrvri
        left join gdispx_data.business_gpsdatas bg on trim(bg.device_id) = trim(vrvri.master_sim_code)
        where vrvri.master_sim_code != '' 
        and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
           or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
        group by vrvri.vehicle_type) a
    join    
    (select vehicle_type_name as `车辆类型`
        , count() as `需监管车辆`
        from gdispx_data.jg_cheliang
    where	((vehicle_type in (1,2) and service_result in (1,2,3,4))
           or (vehicle_type in (3,4) and service_result in (1)))
    group by vehicle_type_name) b on a.`车辆类型` = b.`车辆类型`
    """
    return query_sql


def export_F5(group_type, begin_date, end_date, ruiming=False):
    query_sql = "SELECT "
    if group_type == GROUP_BY_CITY:
        query_sql += "city as `城市`,"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += "if(facilitator_name = '', toString(facilitator_id), facilitator_name) as `运营商`,"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += "if(master_producter_name = '', toString(master_producter_id), master_producter_name) as `设备厂家`,"
    if group_type in [GROUP_BY_WHOLE, GROUP_BY_CITY]:
        query_sql += """
            incon_num as `需监管车辆总数`
           , has_incon_num as `已监管车辆总数`
           , innet_num as `入网车辆数`
           , round(innet_num/incon_num*100,2) as `入网率`
        """
    else:
        query_sql += "innet_num as `入网车辆数`"
    query_sql += """
       , online_num as `上线车辆数`
       , round(online_num/innet_num*100,2) as `上线率`
       , drift_num as `高频漂移车辆数`
       , round(drift_num/online_num*100,2) as `漂移率`
       , total_mileage as `总里程km`
       , continuous_mileage as `轨迹完整里程km`
       , round(continuous_mileage/total_mileage*100,2) as `轨迹完整率`
       , (innet_num-invalid_num)`合格车辆数`
       , noGps_num `定位异常车辆数`
       , noAlarm_num `长期未报警车辆数`
       , noAccessories_num `附件异常车辆`
       , round((innet_num-invalid_num)/innet_num*100,2) `数据合格率`
       from
     (
        WITH (select round(sum(drift_point_count_sum)/count())
            from gdispx_data.v_regulator_vehicle_related_infos vrvri
            join (select master_sim_code, sum(drift_point_count) drift_point_count_sum
                 from gdispx_data.track_report_new where `inc_day` >= '{begin_date}' and `inc_day` < '{end_date}'
                 group by master_sim_code) tr on tr.master_sim_code = trim(vrvri.master_sim_code)
            where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                    or (vrvri.vehicle_type in ('3','4') and vrvri.service_result = '1' ))
            ) as avg_drift_point_count
        select
    """
    if group_type == GROUP_BY_CITY:
        query_sql += " splitByChar(',', vrvri.manger_short_name)[2] city,"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += " vrvri.facilitator_id as facilitator_id, any(vrvri.facilitator_name) facilitator_name,"
    elif group_type == GROUP_BY_PRODUCTER:
            query_sql += " vrvri.master_producter_id as master_producter_id,any(vrvri.master_producter_name) master_producter_name,"
    query_sql += """
            count() has_incon_num
            , count() innet_num
            , countIf(bg.gps_time > addDays(now(),-7)) online_num
            , countIf(tr.imei_drift_point_count > avg_drift_point_count) drift_num
            , sum(tr.imei_total_mileage) total_mileage
            , sum(tr.imei_continuous_mileage) continuous_mileage
            , countIf(bves.noGps = 1) noGps_num
            , countIf(bves.noAlarm = 1) noAlarm_num
            , countIf(bves.noAccessories = 1) noAccessories_num
            , count(distinct if(bves.noGps = 1 or bves.noAlarm = 1 or bves.noAccessories = 1, bves.vehicle_id, null)) invalid_num
    """
    query_sql += """
        from gdispx_data.v_regulator_vehicle_related_infos vrvri
        left join gdispx_data.business_gpsdatas bg on bg.device_id = trim(vrvri.master_sim_code)
        left join (select master_sim_code
                        , sum(drift_point_count) imei_drift_point_count
                        , sum(total_mileage) imei_total_mileage
                        , sum(continuous_mileage) imei_continuous_mileage
                     from gdispx_data.track_report_new
                     where `inc_day` >= '{begin_date}' and `inc_day` < '{end_date}'
                       -- and total_mileage > 1 AND total_mileage < 2400
                     group by master_sim_code) tr
                     on tr.master_sim_code = trim(vrvri.master_sim_code)
        left join gdispx_data.business_vehicle_error_state bves on bves.vehicle_id = vrvri.vehicle_id
        where vrvri.master_sim_code != ''
          and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
    """
    if ruiming:
        query_sql += " and vrvri.master_producter_name = '深圳市锐明技术股份有限公司'"
    if group_type == GROUP_BY_CITY:
        query_sql += " group by splitByChar(',', vrvri.manger_short_name)[2]"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += " group by vrvri.facilitator_id"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += " group by vrvri.master_producter_id"

    query_sql += ") t"

    if group_type in [GROUP_BY_CITY]:
        query_sql += """
             left join (select `XiaQuShi`, count() as incon_num
             from gdispx_data.jg_cheliang
             where	((vehicle_type in (1,2) and service_result in (1,2,3,4,5))
                       or (vehicle_type in (3,4) and service_result in (1)))
             group by `XiaQuShi`) b on t.city = b.`XiaQuShi`
        """
    elif group_type == GROUP_BY_WHOLE:
        query_sql += """
                 , (select count() as incon_num
                 from gdispx_data.jg_cheliang
                 where	((vehicle_type in (1,2) and service_result in (1,2,3,4,5))
                           or (vehicle_type in (3,4) and service_result in (1)))
                 ) b 
            """
    return query_sql.format(begin_date=begin_date.replace('-', ''),
                            end_date=end_date.replace('-', ''))


def export_F6(group_type, begin_date, end_date):
    if group_type == 1:
        query_sql = """
        SELECT
            city as `城市`,
            incon_num as `需监管车辆总数`, 
            has_incon_num as `已监管车辆总数`,
            innet_num as `入网车辆数`,
            round(innet_num/incon_num*100,2) as `入网率`,
            online_num as `上线车辆数` ,
            round(online_num/innet_num*100,2) as `上线率`,
            drift_num as `高频漂移车辆数`,
            round(drift_num/online_num*100,2) as `漂移率`,
            total_mileage as `总里程km`,
            continuous_mileage as `轨迹完整里程km`,
            round(continuous_mileage/total_mileage*100,2) as `轨迹完整率`,
            (innet_num-invalid_num) `合格车辆数` ,
            noGps_num `定位异常车辆数`,
            noAlarm_num `长期未报警车辆数`,
            noAccessories_num `附件异常车辆`,
            round((innet_num-invalid_num)/innet_num*100,2)  `数据合格率`
        from
            (WITH (
                select 
                    round(sum(drift_point_count_sum)/count())
                from (
                    select 
                        t.city_id city_id,
                        ba.name name,
                        t.vehicle_no vehicle_no,
                        t.vehicle_color vehicle_color,
                        t.vehicle_id vehicle_id,
                        t.master_sim_code master_sim_code,
                        t.vehicle_type vehicle_type,
                        t.service_result service_result
                    from
                    (select 
                        vehicle_no,
                        vehicle_color,
                        any(city_id) city_id,
                        any(vehicle_id) vehicle_id,
                        any(master_sim_code) master_sim_code,
                        any(vehicle_type) vehicle_type,
                        any(service_result) service_result
                    from gdispx_data.vehicle_history_jianguan vhj 
                    where create_date >= toDate('{begin_date}') and create_date < toDate('{end_date}')
                        and ((vhj.vehicle_type in ('1') and vhj.service_result in ('1','2')) or (vhj.vehicle_type in ('3','4') and vhj.service_result in ('1')))
                     group by vehicle_no, vehicle_color) t
        LEFT JOIN gdispx_data.basics_area ba on toString(ba.id) = toString(t.city_id)) vrvri
                join (
                    select 
                        master_sim_code, 
                        sum(drift_point_count) drift_point_count_sum
                    from gdispx_data.track_report_new where `inc_day` >= '{begin_date1}' and `inc_day` < '{end_date1}'
                    group by master_sim_code) tr on tr.master_sim_code = trim(vrvri.master_sim_code)
                    where ((vrvri.vehicle_type in ('1') and vrvri.service_result in ('1','2'))
                        or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
            ) as avg_drift_point_count
            select 
                vrvri.name city,
                count() has_incon_num, 
                count() innet_num, 
                countIf(bg.gps_time > addDays(now(),-7)) online_num, 
                countIf(tr.imei_drift_point_count > avg_drift_point_count) drift_num, 
                sum(tr.imei_total_mileage) total_mileage, 
                sum(tr.imei_continuous_mileage) continuous_mileage, 
                countIf(bves.noGps = 1) noGps_num, 
                countIf(bves.noAlarm = 1) noAlarm_num, 
                countIf(bves.noAccessories = 1) noAccessories_num,
                count(distinct if(bves.noGps = 1 or bves.noAlarm = 1 or bves.noAccessories = 1, bves.vehicle_id, null)) invalid_num
            from (
                    select 
                        t.city_id city_id,
                        ba.name name,
                        t.vehicle_no vehicle_no,
                        t.vehicle_color vehicle_color,
                        t.vehicle_id vehicle_id,
                        t.master_sim_code master_sim_code,
                        t.vehicle_type vehicle_type,
                        t.service_result service_result
                    from
                    (select 
                        vehicle_no,
                        vehicle_color,
                        any(city_id) city_id,
                        any(vehicle_id) vehicle_id,
                        any(master_sim_code) master_sim_code,
                        any(vehicle_type) vehicle_type,
                        any(service_result) service_result
                    from gdispx_data.vehicle_history_jianguan vhj 
                    where create_date >= toDate('{begin_date}') and create_date < toDate('{end_date}')
                        and ((vhj.vehicle_type in ('1') and vhj.service_result in ('1','2')) or (vhj.vehicle_type in ('3','4') and vhj.service_result in ('1')))
                     group by vehicle_no, vehicle_color) t
        LEFT JOIN gdispx_data.basics_area ba on toString(ba.id) = toString(t.city_id)) vrvri
            left join (SELECT device_id,
                    max(gps_time) gps_time
                FROM gdispx_data.statistics_gps_day_all sgda WHERE toDate(sgda.gps_time) >= toDate('{begin_date}') and toDate(sgda.gps_time) < toDate('{end_date}')
                GROUP BY device_id) bg on bg.device_id = trim(vrvri.master_sim_code)
            left join (
                select 
                    master_sim_code, 
                    sum(drift_point_count) imei_drift_point_count, 
                    sum(total_mileage) imei_total_mileage, 
                    sum(continuous_mileage) imei_continuous_mileage
                from gdispx_data.track_report_new 
                where  `inc_day` >= '{begin_date1}'  and `inc_day` < '{end_date1}'
                group by master_sim_code
                ) tr on tr.master_sim_code = trim(vrvri.master_sim_code)
            left join gdispx_data.business_vehicle_error_state bves on bves.vehicle_id = vrvri.vehicle_id
            where vrvri.master_sim_code != ''
            and ((vrvri.vehicle_type in ('1') and vrvri.service_result in ('1','2'))
            or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
            group by vrvri.name
            ) t
        left join (
            SELECT 
            ss.name name,
            COUNT(1) incon_num
        FROM
        (select 
            t.city_id city_id,
            ba.name name,
            t.vehicle_no,
            t.vehicle_color
        from
        (select 
            any(city_id) city_id,
            vehicle_no,
            vehicle_color   
        from gdispx_data.vehicle_history_yunzheng vhy 
        where create_date >= toDate('{begin_date}') and create_date < toDate('{end_date}')
            and ((vhy.vehicle_type in ('1') and vhy.jg_service_result in ('1','2')) 
            or (vhy.vehicle_type in ('3','4') and vhy.jg_service_result in ('1')))
        group by vehicle_no, vehicle_color) t
        LEFT JOIN gdispx_data.basics_area ba on toString(ba.id) = t.city_id) ss
        GROUP BY ss.name) b on t.city = b.name     
        """
    elif group_type == 3:
        query_sql = """
        SELECT  producter_name as `设备厂家`,
              innet_num as `入网车辆数`,
              online_num as `上线车辆数`
                   , round(online_num/innet_num*100,2) as `上线率`
                   , drift_num as `高频漂移车辆数`
                   , round(drift_num/online_num*100,2) as `漂移率`
                   , total_mileage as `总里程km`
                   , continuous_mileage as `轨迹完整里程km`
                   , round(continuous_mileage/total_mileage*100,2) as `轨迹完整率`
                   , (innet_num-invalid_num)`合格车辆数`
                   , noGps_num `定位异常车辆数`
                   , noAlarm_num `长期未报警车辆数`
                   , noAccessories_num `附件异常车辆`
                   , round((innet_num-invalid_num)/innet_num*100,2) `数据合格率`
                   from
                 (
                    WITH (select round(sum(drift_point_count_sum)/count())
                        from (select 
                            bp.producter_name producter_name,
                            t.vehicle_no vehicle_no,
                            t.vehicle_color vehicle_color,
                            t.vehicle_id vehicle_id,
                            t.master_sim_code master_sim_code,
                            t.vehicle_type vehicle_type,
                            t.service_result service_result
                        from
                        (select 
                            vehicle_no,
                            vehicle_color,
                            any(city_id) city_id,
                            any(vehicle_id) vehicle_id,
                            any(master_sim_code) master_sim_code,
                            any(vehicle_type) vehicle_type,
                            any(service_result) service_result
                        from gdispx_data.vehicle_history_jianguan vhj 
                        where create_date >= toDate('{begin_date}') and create_date < toDate('{end_date}')
                            and ((vhj.vehicle_type in ('1') and vhj.service_result in ('1','2')) or (vhj.vehicle_type in ('3','4') and vhj.service_result in ('1')))
                            group by vehicle_no, vehicle_color)  t
                        LEFT JOIN gdispx_data.basics_sim AS bs ON toString(t.vehicle_id) = toString(bs.vehicle_id) AND toString(bs.is_master) = '0'
                        LEFT JOIN gdispx_data.basics_device_type AS bdt ON bs.device_type_id = bdt.id
                        LEFT JOIN gdispx_data.basics_producter AS bp ON bdt.producter_id = bp.id) vrvri
                        join (select master_sim_code, sum(drift_point_count) drift_point_count_sum
                             from gdispx_data.track_report_new 
                             where `inc_day` >= '{begin_date1}' and `inc_day` < '{end_date1}'
                             group by master_sim_code) tr on tr.master_sim_code = trim(vrvri.master_sim_code)
                        where ((vrvri.vehicle_type in ('1') and vrvri.service_result in ('1','2'))
                                or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
                        ) as avg_drift_point_count
                    select  any(vrvri.producter_name) producter_name,
                         count() has_incon_num
                        , count() innet_num
                        , countIf(bg.gps_time > addDays(now(),-7)) online_num
                        , countIf(tr.imei_drift_point_count > avg_drift_point_count) drift_num
                        , sum(tr.imei_total_mileage) total_mileage
                        , sum(tr.imei_continuous_mileage) continuous_mileage
                        , countIf(bves.noGps = 1) noGps_num
                        , countIf(bves.noAlarm = 1) noAlarm_num
                        , countIf(bves.noAccessories = 1) noAccessories_num
                        , count(distinct if(bves.noGps = 1 or bves.noAlarm = 1 or bves.noAccessories = 1, bves.vehicle_id, null)) invalid_num
                        from (select 
                                bp.producter_name producter_name,
                                t.vehicle_no vehicle_no,
                                t.vehicle_color vehicle_color,
                                t.vehicle_id vehicle_id,
                                t.master_sim_code master_sim_code,
                                t.vehicle_type vehicle_type,
                                t.service_result service_result
                            from
                            (select 
                                vehicle_no,
                                vehicle_color,
                                any(city_id) city_id,
                                any(vehicle_id) vehicle_id,
                                any(master_sim_code) master_sim_code,
                                any(vehicle_type) vehicle_type,
                                any(service_result) service_result
                            from gdispx_data.vehicle_history_jianguan vhj 
                            where create_date >= toDate('{begin_date}') and create_date < toDate('{end_date}')
                                and ((vhj.vehicle_type in ('1') and vhj.service_result in ('1','2')) or (vhj.vehicle_type in ('3','4') and vhj.service_result in ('1')))
                                group by vehicle_no, vehicle_color)  t
                            LEFT JOIN gdispx_data.basics_sim AS bs ON toString(t.vehicle_id) = toString(bs.vehicle_id) AND toString(bs.is_master) = '0'
                            LEFT JOIN gdispx_data.basics_device_type AS bdt ON bs.device_type_id = bdt.id
                            LEFT JOIN gdispx_data.basics_producter AS bp ON bdt.producter_id = bp.id) vrvri
                    left join 
                    (SELECT device_id,
                        max(gps_time) gps_time
                    FROM gdispx_data.statistics_gps_day_all sgda WHERE toDate(sgda.gps_time) >= toDate('{begin_date}') and toDate(sgda.gps_time) < toDate('{end_date}')
                    GROUP BY device_id) bg on bg.device_id = trim(vrvri.master_sim_code)
                    left join (select master_sim_code
                                    , sum(drift_point_count) imei_drift_point_count
                                    , sum(total_mileage) imei_total_mileage
                                    , sum(continuous_mileage) imei_continuous_mileage
                                 from gdispx_data.track_report_new 
                                 where `inc_day` >= '{begin_date1}' and `inc_day` < '{end_date1}'
                                 group by master_sim_code) tr
                                 on tr.master_sim_code = trim(vrvri.master_sim_code)
                    left join gdispx_data.business_vehicle_error_state bves on bves.vehicle_id = vrvri.vehicle_id
                    where vrvri.master_sim_code != ''
                      and ((vrvri.vehicle_type in ('1') and vrvri.service_result in ('1','2'))
                            or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
                           group by vrvri.producter_name) t
        """
    return query_sql.format(begin_date=begin_date,
                            end_date=end_date,
                            begin_date1=begin_date.replace('-', ''),
                            end_date1=end_date.replace('-', ''))


def query_then_write_csv(query_sql, client, output_file):
    print("query_then_write_csv \n")
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join([col[0] for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join([str(x) for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        output_file_name = '5项指标统计_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            output_file.write('整体\n')
            query_then_write_csv(export_whole(), client, output_file)
            output_file.write('\n')
            query_then_write_csv(export_F5(GROUP_BY_WHOLE, begin_date, end_date), client, output_file)
            output_file.write('\n\n按城市\n')
            query_then_write_csv(export_F5(GROUP_BY_CITY, begin_date, end_date), client, output_file)
            output_file.write('\n\n按设备厂商\n')
            query_then_write_csv(export_F5(GROUP_BY_PRODUCTER, begin_date, end_date), client, output_file)
            output_file.write('\n\n按运营商\n')
            query_then_write_csv(export_F5(GROUP_BY_FACILITATOR, begin_date, end_date), client, output_file)

            # output_file.write('\n\n锐明专项\n')
            # output_file.write('整体\n')
            # query_then_write_csv(export_F5(GROUP_BY_WHOLE, begin_date, end_date, True), client, output_file)
            # output_file.write('\n\n按城市\n')
            # query_then_write_csv(export_F5(GROUP_BY_CITY, begin_date, end_date, True), client, output_file)
            # output_file.write('\n\n按运营商\n')
            # query_then_write_csv(export_F5(GROUP_BY_FACILITATOR, begin_date, end_date, True), client, output_file)
    finally:
        ck.disconnect()


def export1(begin_date, end_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        output_file_name = '新5项指标统计_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            output_file.write('按城市-新\n')
            query_then_write_csv(export_F6(GROUP_BY_CITY, begin_date, end_date), client, output_file)
            output_file.write('\n\n按设备厂商-新\n')
            query_then_write_csv(export_F6(GROUP_BY_PRODUCTER, begin_date, end_date), client, output_file)
    finally:
        ck.disconnect()


if __name__ == '__main__':
    export('2023-09-29', '2023-10-06')
