import month_for_city_report as mc
import month_for_county_report as mc1
import month_for_exception_report as me
import month_v4_stats as m4
import month_for_owner_risk_report as mo
import month_for_productor_report as mp
import month_for_device_covered_report as md
from yyutils import *


def main(start, end, days):
    mc.main(start, end, days)
    mc1.main(start, end, days)
    me.main(start, end, days)
    m4.main(start, end)
    mo.main(start, end, days)
    mp.main(start, end, days)
    md.main(start,end,days)


if __name__ == '__main__':
    start, end, days = TimeUtils.getLastMonthRange()
    main(start, end, days)
