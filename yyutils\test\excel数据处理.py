import openpyxl
from openpyxl.cell import Cell
from collections import OrderedDict
import os


class ExcelHandler:
    def __init__(self, src_xlfile=None, dest_xlfile=None):
        if src_xlfile:
            self.workbook = openpyxl.load_workbook(src_xlfile)
        else:
            self.workbook = openpyxl.Workbook()
        self.dest_xlfile = dest_xlfile
        self.dest_workbook =None
        if dest_xlfile:
            self.dest_workbook = openpyxl.Workbook()

    def lsSheetNames(self):
        return self.workbook.sheetnames

    def getSheetDictData(self,sheetName,header_idx = 1):
        sheet = self.workbook[sheetName]
        header = []
        outlist = []
        for i in range(1,sheet.max_row+1):
            if i < header_idx:
                continue
            elif i == header_idx:
                header = [cell.value for cell in sheet[i]]
                continue
            e = OrderedDict(zip(header,[cell.value for cell in sheet[i]]))
            outlist.append(e)
        return outlist

    def activeWorksheet(self, sheetName: str = None):
        if sheetName not in self.workbook:
            self.workbook.create_sheet(sheetName)
        return self.workbook[sheetName]

    def copyRowStyle(self, sheet, row_idx):
        return [cell._style for cell in sheet[row_idx]]

    def copyRow(self, sheet, row_idx):
        return [Cell(sheet, value=cell.value, style_array=cell._style) for cell in sheet[row_idx]]

    def copyRows(self, sheet, row_from=1, row_end=1):
        outlist = []
        for i in range(row_from , row_end+1):
            outlist.append(self.copyRow(sheet, i))
        return outlist

    def getSheetRowCount(self, sheet):
        return sheet.max_row

    def clearData(self, sheet, row_from=1, order_col_idx=None):
        orders = []
        if order_col_idx is not None:
            i = 0
            for row in sheet:
                i += 1
                if i < row_from:
                    continue
                orders.append(row[order_col_idx - 1].value)
        for i in range(sheet.max_row, row_from-1, -1):
            sheet.delete_rows(i)
        return orders

    def fillData(self, sheet, datalist: list, rowStyles):
        '''
        支持 OrderDict list tuple 类型的行元素
        :param sheet:
        :param datalist:
        :param rowStyles:
        :return:
        '''
        for row in datalist:
            if isinstance(row,list) or isinstance(row,tuple):
                newRow = [Cell(sheet, value=row[i], style_array=rowStyles[i]) for i in range(len(row))]
            else:
                keys = list(row.keys())
                newRow = [Cell(sheet, value=row[keys[i]], style_array=rowStyles[i]) for i in range(len(keys))]
            sheet.append(newRow)

    def save(self):
        self.workbook.save(self.dest_xlfile)


def getAllSheetData(infile,otfile):
    hander = ExcelHandler(infile,otfile)
    sheetNames = hander.lsSheetNames()
    headers = []
    templist = []
    for sheetName in sheetNames:
        sheetlist = hander.getSheetDictData(sheetName,1)
        h = list(sheetlist[0].keys())
        for t in h:
            if t not in headers:
                headers.append(t)
        for e in sheetlist:
            e['类型'] = sheetName
        templist.extend(sheetlist)
    headers.append('类型')
    outlist = []
    for e in templist:
        ne = OrderedDict(zip(headers,[e.get(t) for t in headers]))
        outlist.append(ne)
    dest_workbook = hander.dest_workbook
    sheet = dest_workbook.active
    sheet.title = '汇总'
    header_sheet = [Cell(sheet,value=t) for t in headers]
    sheet.append(header_sheet)
    for e in outlist:
        row = [Cell(sheet,value=v) for k,v in e.items()]
        sheet.append(row)
    dest_workbook.save(otfile)



def main(bpath):
    infile = os.path.join(bpath,'RM-2022-8-17.xlsx')
    otfile = os.path.join(bpath,'汇总.xlsx')
    getAllSheetData(infile,otfile)


if __name__ == '__main__':
    bpath = r'/Users/<USER>/Documents/杂项'
    main(bpath)

