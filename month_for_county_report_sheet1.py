from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    xjg_sql = YYSQLTemplate.xjg_stats(
        [],
        "city_id,area_id",
        '''
        toInt32(city_id) city_id,
        toInt32(area_id) area_id,
        count(distinct vehicle_no,vehicle_color) xjg_count
        ''',
        start_day, end_day)
    wide_sql = YYSQLTemplate.wide_stats(
        [],
        'city_id ,area_id,vehicle_no ,vehicle_color',
        '''
        city_id,
        area_id,
        any(area_manger_short_name) area_manger_short_name,
        vehicle_no,
        vehicle_color,
        if(max(online) =1,1,0 ) is_online,
        if(min(never_gps) +  min(never_alarm) + min(never_acc) > 0 , 0 , 1) is_ok,
        sum(total_mileage) total_mileage,
        sum(continuous_mileage) continuous_mileage,
        sum(accessoriesNum) accessoriesNum,
        sum(accessoriesonTimeNum) accessoriesonTimeNum,
        sum(risk_oneLevel) l1_riskNum,
        sum(risk_twoLevel) l2_riskNum,
        sum(risk_threeLevel) l3_riskNum
        ''',
        start_day=start_day, end_day=end_day
    )
    jg_sql = SQLHelper().table(wide_sql) \
        .groupBy('city_id,area_id') \
        .select('''
                city_id,
                area_id,
                any(area_manger_short_name) area_manger_short_name,
                count(1) jg_count,
                sum(is_online) online_count,
                sum(is_ok) ok_count,
                sum(total_mileage) total_mileage,
                sum(continuous_mileage) continuous_mileage,
                sum(accessoriesNum) accessoriesNum,
                sum(accessoriesonTimeNum) accessoriesonTimeNum,
                sum(l1_riskNum) l1_riskNum,
                sum(l2_riskNum) l2_riskNum,
                sum(l3_riskNum) l3_riskNum
            ''')
    final_sql = SQLHelper().table(xjg_sql, 'A') \
        .leftJoin().table(jg_sql, 'B').on(
        '''
        A.city_id=B.city_id
        and A.area_id=B.area_id
        '''
    ).select(
        f'''
                A.city_id city_id,
                A.area_id area_id,
                splitByChar(',', area_manger_short_name)[2] AS city_name,
                splitByChar(',', area_manger_short_name)[3] AS area_name,
                if(xjg_count>jg_count,xjg_count,jg_count) xjg_count,
                jg_count,
                online_count,
                ok_count,
                jg_count - ok_count ng_count,
                l1_riskNum,
                l2_riskNum,
                l3_riskNum,
                total_mileage,
                continuous_mileage,
                accessoriesNum,
                accessoriesonTimeNum,
                FLOOR(jg_count/xjg_count,4) jg_rate,
                FLOOR(online_count/jg_count,4) online_rate,
                FLOOR(continuous_mileage/total_mileage,4) track_rate,
                FLOOR(ok_count/jg_count,4) ok_rate,
                FLOOR((accessoriesNum - accessoriesonTimeNum)/accessoriesNum,4) acc_rate
            '''
    )
    return final_sql


def export_outlist_orderDict(inlist):
    outlist = []
    h = [
        'city_name','area_name','xjg_count','jg_count',
        'online_count','ng_count','l1_riskNum','l2_riskNum','l3_riskNum',
        'jg_rate','online_rate','track_rate','ok_rate','acc_rate'
    ]
    for e in inlist:
        v = [
            e['city_name'],e['area_name'],e['xjg_count'],e['jg_count'],
            e['online_count'],e['ng_count'],e['l1_riskNum'],e['l2_riskNum'],
            e['l3_riskNum'],
            FLOORtoPercent(e['jg_rate']),
            FLOORtoPercent(e['online_rate']),
            FLOORtoPercent(e['track_rate']),
            FLOORtoPercent(e['ok_rate']),
            FLOORtoPercent(e['acc_rate']),
        ]
        ne = OrderedDict(zip(h,v))
        outlist.append(ne)
    return outlist


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    datalist = orderByCity(datalist)
    outlist = export_outlist_orderDict(datalist)
    return outlist


if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
