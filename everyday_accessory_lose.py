from db import CkDB


everydat_accessory_lose_query_sql = """
SELECT 
	toDate(baai.alarm_time) as `日期`,
	vri.master_producter_name as `厂商`,
	sum(if(LENGTH (bai.file_list) > 0,1,0) )as `(有)报警数量`,
	sum(if(empty(bai.file_list),1,0)) as `(无)报警数量`
FROM gdispx_data.business_alarm_info AS baai
left join (select * from gdispx_data.business_accessories_info 
	where create_time >='{end_date} 00:00:00'  and create_time <='{end_date} 23:59:59' ) bai ON bai.alarm_tag = baai.alarm_tag 
left join(select * from gdispx_data.vehicle_related_infos) vri on (vri.vehicle_no = baai.vehicle_no and toString(vri.vehicle_color) = toString(baai.vehicle_color)) 
	where   baai.alarm_time >=  toDateTime('{end_date} 00:00:00')  AND baai.alarm_time <=  toDateTime('{end_date} 23:59:59')   
    AND  baai.alarm_code IN(10400,10401,10402,10410,10412,10413)
    GROUP BY vri.master_producter_name,toDate(baai.alarm_time)
    ORDER BY any(toDate(baai.alarm_time)) ASC;
"""

everydat_accessory_lose_detail_query_sql = """
SELECT 
	    any(toDate(baai.alarm_time)) as `日期`,
	    any(vri.master_producter_name) as `厂商`,
	    any(baai.vehicle_no) as `车牌`,
	    any(baai.vehicle_color_name) as `车牌颜色`,
	    any(baai.device_id) as `SIM卡号`,
	    any(baai.owner_name) as `业户`,
	    any(baai .facilitator_name) as `服务商`,
		sum(if(LENGTH (bai.file_list) > 0,1,0) )as `(有)报警数量`,
		sum(if(empty(bai.file_list),1,0)) as `(无)报警数量`
	FROM gdispx_data.business_alarm_info AS baai
	left join (select * from gdispx_data.business_accessories_info 
		where create_time >='{end_date} 00:00:00' 
		and create_time <='{end_date} 23:59:59')　bai　ON bai.alarm_tag = baai.alarm_tag 
	left join(select * from gdispx_data.vehicle_related_infos)　vri　 on (vri.vehicle_no = baai.vehicle_no and toString(vri.vehicle_color) = toString(baai.vehicle_color)) 
		where	baai.alarm_time >=  toDateTime('{end_date} 00:00:00')  
		AND baai.alarm_time <=  toDateTime('{end_date} 23:59:59')        
        AND  baai.alarm_code IN(10400,10401,10402,10410,10412,10413)
        GROUP BY  baai.vehicle_no,baai .vehicle_color_name,toDate(baai.alarm_time)
        ORDER BY any(toDate(baai.alarm_time)) ASC;
"""

def query_then_write_csv(query_sql, client, output_file):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join(['"' + col[0] + '"' for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join(['"' + str(x) + '"' for x in list(row)]) + '\n')


def export(end_date):
    ck = CkDB()
    # TODO: 这里的open都需要增加encoding = utf-8:
    try:
        client = ck.get_client()
        file_suffix = '_{}.csv'.format(end_date.replace('-', '')[-4:])
        with open('每日附件丢失' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(everydat_accessory_lose_query_sql.format(end_date=end_date), client, output_file)

        with open('每日附件丢失明细' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(everydat_accessory_lose_detail_query_sql.format(end_date=end_date), client, output_file)
    finally:
        ck.disconnect()


if __name__ == '__main__':
    from datetime import datetime, timedelta
    today = datetime.today()
    # 2天之前
    begin_date = (today - timedelta(days=2)).strftime('%Y-%m-%d')
    end_date = (today - timedelta(days=1)).strftime('%Y-%m-%d')
    export(end_date)
