import csv
from io import StringIO
import pandas as pd
import requests
from urllib.parse import quote


class httpDBCK253():
    def __init__(self):
        # 设置数据库连接参数
        self.host = '**************'
        self.port = 8868
        self.user = 'default'
        self.password = '<EMAIL>'
        self.database = 'gdispx_data'

    def execute_sql(self, query, title=None) -> pd.DataFrame:
        encoded_query = quote(query)
        url = f'http://{self.host}:{self.port}/?query={encoded_query}&user={self.user}&password={self.password}&database={self.database}'
        response = requests.get(url, timeout=3600)
        if response.status_code != 200:
            raise Exception(
                f"HTTP request failed with status code {response.status_code}: {response.text}")

        if title:
            # df = pd.read_csv(StringIO(response.text), delimiter='\t', names=title)
            df = pd.read_csv(StringIO(response.text), delimiter='\t', names=title,
                quoting=csv.QUOTE_NONE,
                error_bad_lines=False,
                engine='python'
            )
        else:
            # df = pd.read_csv(StringIO(response.text), delimiter='\t', header=None)
            df = pd.read_csv(StringIO(response.text), delimiter='\t', header=None,
                quoting=csv.QUOTE_NONE,
                error_bad_lines=False,
                engine='python'
            )
        return df


class httpDBCK237():
    def __init__(self):
        # 设置数据库连接参数
        self.host = '**************'
        self.port = 8123
        self.user = 'default'
        self.password = '<EMAIL>'
        self.database = 'gdispx_data'

    def execute_sql(self, query, title=None) -> pd.DataFrame:
        encoded_query = quote(query)
        url = f'http://{self.host}:{self.port}/?query={encoded_query}&user={self.user}&password={self.password}&database={self.database}'
        response = requests.get(url, timeout=3600)
        if response.status_code != 200:
            raise Exception(
                f"HTTP request failed with status code {response.status_code}: {response.text}")
        df = pd.read_csv(StringIO(response.text))
        return df


class httpDBCK151():
    def __init__(self):
        # 设置数据库连接参数
        self.host = '**************'
        self.port = 8123
        self.user = 'default'
        self.password = '<EMAIL>'
        self.database = 'gdispx_data'

    def execute_sql(self, query, title=None) -> pd.DataFrame:
        encoded_query = quote(query)
        url = f'http://{self.host}:{self.port}/?query={encoded_query}&user={self.user}&password={self.password}&database={self.database}'
        response = requests.get(url, timeout=3600)
        if response.status_code != 200:
            raise Exception(
                f"HTTP request failed with status code {response.status_code}: {response.text}")

        if title:
            df = pd.read_csv(StringIO(response.text), delimiter='\t', names=title)
        else:
            df = pd.read_csv(StringIO(response.text), delimiter='\t', header=None)
        return df


class httpDBCK188():
    def __init__(self):
        # 设置数据库连接参数
        self.host = '**************'
        self.port = 8123
        self.user = 'default'
        self.password = '<EMAIL>'
        self.database = 'gdispx_nk_data'

    def execute_sql(self, query, title=None) -> pd.DataFrame:
        encoded_query = quote(query)
        url = f'http://{self.host}:{self.port}/?query={encoded_query}&user={self.user}&password={self.password}&database={self.database}'
        response = requests.get(url, timeout=3600)
        if response.status_code != 200:
            raise Exception(
                f"HTTP request failed with status code {response.status_code}: {response.text}")

        if title:
            df = pd.read_csv(StringIO(response.text), delimiter='\t', names=title)
        else:
            df = pd.read_csv(StringIO(response.text), delimiter='\t', header=None)
        return df


class httpDBCK202():
    def __init__(self):
        # 设置数据库连接参数
        self.host = '**************'
        self.port = 8868
        self.user = 'default'
        self.password = '<EMAIL>'
        self.database = 'gdispx_data'

    def execute_sql(self, query, title) -> pd.DataFrame:
        encoded_query = quote(query)
        url = f'http://{self.host}:{self.port}/?query={encoded_query}&user={self.user}&password={self.password}&database={self.database}'
        response = requests.get(url, timeout=3600)
        if response.status_code != 200:  # <span style="color:red">新增</span>
            raise Exception(
                f"HTTP request failed with status code {response.status_code}: {response.text}")  # <span style="color:red">新增</span>
        df = pd.read_csv(StringIO(response.text), delimiter='\t', header=None, names=title)
        return df


class httpDBCK84():
    def __init__(self):
        # 设置数据库连接参数
        self.host = '*************'
        self.port = 8868
        self.user = 'default'
        self.password = '<EMAIL>'
        self.database = 'gdispx_data'

    def execute_sql(self, query, title) -> pd.DataFrame:
        encoded_query = quote(query)
        url = f'http://{self.host}:{self.port}/?query={encoded_query}&user={self.user}&password={self.password}&database={self.database}'
        response = requests.get(url, timeout=3600)
        if response.status_code != 200:  # <span style="color:red">新增</span>
            raise Exception(
                f"HTTP request failed with status code {response.status_code}: {response.text}")  # <span style="color:red">新增</span>
        df = pd.read_csv(StringIO(response.text), delimiter='\t', header=None, names=title)
        return df


if __name__ == '__main__':
    DBCK = httpDBCK253()
    query = """
        SELECT
        any(city_short_name) as `地市`, count(DISTINCT
        vehicle_id) as `车辆数`
        from vehicle_wide_day_all vwda3
        final
        where
        inc_day
        BETWEEN
        '2024-06-10'
        AND
        '2024-06-10'
        group
        by
        city_id
    """
    title = ['地市', '车辆数']
    df = DBCK.execute_sql(query, title)
    print(df.head())
