import sys

from yyutils import *
from datetime import datetime, timedelta
from owner_day_all_yy import load_owner_info

bpath = r'/data/report_data/vehicle_wide_day_all'


def from_A_to_B(start, end):
    start_day = datetime.strptime(start, '%Y-%m-%d')
    if not end:
        end = start
    end_day = datetime.strptime(end, '%Y-%m-%d')
    n = 0
    while True:
        one = start_day + timedelta(days=n)
        if one <= end_day:
            n += 1
            yield str(one.strftime('%Y-%m-%d'))
        else:
            break


def getIntDefault(e, key, default_value=None):
    if not e[key] or int(e[key]) == 0:
        e[key] = default_value
    else:
        e[key] = int(e[key])


def attach_cur_state(e, cur_acc):
    vn = (e['vehicle_no'], e['vehicle_color'])
    if vn in cur_acc:
        e['has_accessory'] = 1
    else:
        e['has_accessory'] = 0
    if e.get('total_point_count', 0) > 0:
        e['online'] = 1
    elif e.get('online_time', 0) > 0:
        e['online'] = 1
    else:
        e['online'] = 0
    if e.get('alarmNum', 0) > 0:
        e['has_alarm'] = 1
    else:
        e['has_alarm'] = 0


def check_is_online(e, db, day):
    if not e["master_sim_code"]:
        return False
    sim = e["master_sim_code"]
    sql = f'''
                select  gps_time, latitude, longitude
                from gdispx_data.business_raw_gps_info_all 
                where toDate(gps_time) = '{day}'
                and sim_code = '{sim}' limit 10
        '''
    rows = db.query_to_dictList(sql)
    return len(rows) > 0


def collect_cur_jg(day):
    db = DBUtil(DBType.CLICKHOUSE.value)
    vehicle_sql = f'''
                 select
             	vria.vehicle_id AS vehicle_id,
             	vria.vehicle_no AS vehicle_no,
             	vria.vehicle_color AS vehicle_color,
             	vria.vehicle_type AS vehicle_type,
             	vria.service_result AS service_result,
             	toDate(vria.access_time) AS access_time,
             	vria.owner_id AS owner_id,
             	vria.owner_name AS owner_name,
             	vria.facilitator_id AS facilitator_id,
             	vria.facilitator_name AS facilitator_name,
             	vria.facilitator_social_credit_code AS facilitator_social_credit_code,
             	vria.third_party_id AS third_party_id,
             	vria.third_party_name AS third_party_name,
             	vria.third_party_social_credit_code AS third_party_social_credit_code,
             	vria.device_type AS device_type,
             	vria.master_device_id AS master_device_id,
             	vria.master_sim_code AS master_sim_code,
             	vria.master_producter_id AS master_producter_id,
             	vria.master_producter_name AS master_producter_name,
             	vria.master_type_num AS master_type_num,
             	vria.master_device_brand AS master_device_brand,
             	vria.AI_BOX_device_id AS AI_BOX_device_id,
             	vria.AI_BOX_sim_code AS AI_BOX_sim_code,
             	vria.AI_BOX_producter_id AS AI_BOX_producter_id,
             	vria.AI_BOX_producter_name AS AI_BOX_producter_name,
             	vria.AI_BOX_type_num AS AI_BOX_type_num,
             	vria.AI_BOX_device_brand AS AI_BOX_device_brand,
             	ba.id AS area_id,
             	ba.name AS area_name,
             	ba.short_name AS area_short_name,
             	ba.city_id AS city_id,
             	splitByChar(',', ba.manger_name)[2] AS city_name,
             	ba.city_short AS city_short_name,
             	ba.manger_name AS area_manger_name,
             	ba.manger_short_name AS area_manger_short_name
             from gdispx_data.vehicle_related_infos_all vria
             left join gdispx_data.basics_area ba
             on vria.area_id = ba.id
             where (vria.vehicle_type in ('1', '2') and vria.service_result in ('1', '0', '-1'))
     		   or (vria.vehicle_type in ('3', '4') and vria.service_result in ('1'))
             '''

    datalist = db.query_to_dictList(vehicle_sql)
    for e in datalist:
        e['inc_day'] = day
        getIntDefault(e, 'AI_BOX_producter_id', None)
        getIntDefault(e, 'third_party_id', None)
    db.close()
    return datalist


def collect_ever(report_day):
    start_day = TimeUtils.DeltaDay(-5,cur_day=report_day)
    end_day = TimeUtils.DeltaDay(-1,cur_day=report_day)

    ever_gps_sql = f'''
            select 
            distinct
            vehicle_no,vehicle_color
            from gdispx_data.vehicle_wide_day_all  final 
            where never_gps = 0 and inc_day between '{start_day}' and '{end_day}'
        '''

    ever_alarm_sql = f'''
            select 
            distinct
            vehicle_no,vehicle_color
            from gdispx_data.vehicle_wide_day_all  final 
            where never_alarm = 0 and inc_day between '{start_day}' and '{end_day}'
        '''

    ever_acc_sql = f'''
                select 
                distinct
                vehicle_no,vehicle_color
                from gdispx_data.vehicle_wide_day_all  final 
                where never_acc = 0 and inc_day between '{start_day}' and '{end_day}'
            '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist1 = db.query_to_dictList(ever_gps_sql)
    ever_gps = set([(e['vehicle_no'], str(e['vehicle_color'])) for e in datalist1])
    datalist2 = db.query_to_dictList(ever_alarm_sql)
    ever_alarm = set([(e['vehicle_no'], str(e['vehicle_color'])) for e in datalist2])
    datalist3 = db.query_to_dictList(ever_acc_sql)
    ever_acc = set([(e['vehicle_no'], str(e['vehicle_color'])) for e in datalist3])
    db.close()
    return ever_gps, ever_alarm, ever_acc


def collect_history_dayInfo(day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    alarm_sql = f'''
                    SELECT
                        vehicle_no,
                        vehicle_color,
                        sum(alarmNum) AS alarmNum,
                        sum(type10405) coverAlarm
                    FROM
                        gdispx_data.statistics_original_alarm_day_all soada
                    where
                        alarm_time = '{day}'
                    group by
                        vehicle_no,
                        vehicle_color
        '''
    alarm_dlist = db.query_to_dictList(alarm_sql)

    risk_sql = f'''
                    SELECT
                        vehicle_no,
                        vehicle_color,
                        sum(alarmNum) AS riskNum,
                        sum(disposeNum) AS risk_disposeNum,
                        sum(oneLevel) AS risk_oneLevel,
                        sum(twoLevel) AS risk_twoLevel,
                        sum(threeLevel) AS risk_threeLevel,
                        sum(monitorIntervene) AS risk_monitorIntervene
                    FROM
                        gdispx_data.risk_statistics_day_all rsda
                    where
                        alarm_time = '{day}'
                    group by
                        vehicle_no,
                        vehicle_color
            '''
    # risk_dlist = db.query_to_dictList(risk_sql)

    risk_sql_new = f'''
        select
            vehicle_no ,
            toUInt8(vehicle_color) vehicle_color,
            risk_oneLevel+risk_twoLevel+risk_threeLevel riskNum,
            sum(is_manual_dispose) risk_disposeNum,
            count(1) - risk_disposeNum auto_riskNum,
            sum(dispose_is_mistake) dispose_mistake_riskNum,
            sum(has_audit) audit_riskNum,
            audit_riskNum - sum(audit_is_mistake) audit_reject_riskNum,
            sum(has_check) check_riskNum,
            sum(check_is_mistake) check_confirm_riskNum,
            check_riskNum - check_confirm_riskNum check_reject_riskNum,
            sum(oneLevel) risk_oneLevel,
            sum(twoLevel) risk_twoLevel,
            sum(threeLevel) risk_threeLevel
        from 
            (SELECT 
                id,
                vehicle_no,
                vehicle_color,
                is_manual_dispose,
                dispose_is_mistake,
                has_audit,
                audit_is_mistake,
                has_check,
                check_is_mistake,
                if(check_is_mistake=1 and  oneLevel =1, 0,oneLevel) oneLevel,
                if(check_is_mistake=1 and  twoLevel =1, 0,twoLevel) twoLevel,
                if(check_is_mistake=1 and  threeLevel =1, 0,threeLevel) threeLevel
             from 
                    (SELECT 
                    distinct
                    vehicle_no ,
                    vehicle_color,
                    id,
                    if(alarm_level=1,1,0) oneLevel,
                    if(alarm_level=2,1,0) twoLevel,
                    if(alarm_level=3,1,0) threeLevel
                    from gdispx_data.business_ai_alarm_info 
                    where toDate(alarm_time) = '{day}' 
                    and alarm_code in (10402,10401,10413,90001,90002,90007,10410,10405,10412)
                    and id != '') A
                left JOIN 
                    (SELECT 
                    alarm_id,
                    max(if(dispose_type=0 and process_unit !='监管系统', 1, 0)) `is_manual_dispose`,
                    max(if(process_status in (3,7) and dispose_type=0, 1, 0)) `dispose_is_mistake`,
                    max(if(process_user != '' and dispose_type=1, 1, 0)) `has_audit`,
                    max(if(process_status=3 and dispose_type=1, 1, 0)) `audit_is_mistake`,
                    max(if(dispose_type=7, 1, 0)) `has_check`,
                    max(if(process_status=2 and dispose_type=7, 1, 0)) `check_is_mistake`
                    from gdispx_data.business_alarm_audit_info final
                    where toDate(create_time) between '{day}' and '{risk_dispose_end_day}'
                    and alarm_id !=''
                    group by alarm_id) B on A.id = B.alarm_id
            ) C
        group by vehicle_no,vehicle_color
    '''
    risk_dlist = db.query_to_dictList(risk_sql_new)

    acc_sql = f'''
                select 
                distinct
                vehicle_no,vehicle_color
                from gdispx_data.business_accessories_info
                where toDate(create_time) = '{day}'
            '''
    acc_dlist = db.query_to_dictList(acc_sql)

    track_sql = f'''
                SELECT 
                    vehicle_no,
                    vehicle_color,
                    total_point_count ,
                    drift_point_count,
                    continuous_mileage,
                    total_mileage,
                    error_point_count
                from gdispx_data.track_report_new tr 
                where tr.inc_day = '{day.replace('-', '')}'
            '''
    track_dlist = db.query_to_dictList(track_sql)

    gps_sql = f'''
               SELECT
                       vehicle_no,
                       vehicle_color,
                       if(sum(online_time)>86400,86400,sum(online_time)) AS online_time,
                       if(sum(run_time)>86400,86400,sum(run_time)) AS run_time
                   FROM
                       gdispx_data.statistics_gps_day_all
                   where
                       toDate(gps_time) = '{day}'
                   group by
                       vehicle_no,
                       vehicle_color
           '''
    gps_dlist = db.query_to_dictList(gps_sql)

    middle_sql = f'''
                select vehicle_no,vehicle_color,exception_type
                    from gdispx_data.vehicle_related_infos_middle 
                    where toDate(create_time) = '{day}'
        '''
    middle_dlist = db.query_to_dictList(middle_sql)
    acc_ontime_sql = f'''
            select vehicle_no,
            vehicle_color,
            alarmNum AS accessoriesNum,
            onTimeNum AS accessoriesonTimeNum
            from gdispx_data.statistics_on_time_accessories_day where toDate(alarm_time) = '{day}'
        '''
    acc_ontime_dlist = db.query_to_dictList(acc_ontime_sql)
    db.close()
    return alarm_dlist, risk_dlist, acc_dlist, track_dlist, gps_dlist, middle_dlist, acc_ontime_dlist


def convert_vn_dict(dlist, is_vn_set=False):
    if not is_vn_set:
        vn_dict = {}
        for e in dlist:
            vn = (e['vehicle_no'], str(e['vehicle_color']))
            vn_dict.setdefault(vn, e)
        return vn_dict
    vn_set = set()
    for e in dlist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        vn_set.add(vn)
    return vn_set


def attach_data_to_vehicle(vehicle_list, alarm_dlist, risk_dlist,
                           acc_dlist, track_dlist, gps_dlist, middle_dlist, acc_ontime_dlist):
    vn_alarm = convert_vn_dict(alarm_dlist)
    vn_risk = convert_vn_dict(risk_dlist)
    acc_set = convert_vn_dict(acc_dlist, True)
    vn_track = convert_vn_dict(track_dlist)
    vn_gps = convert_vn_dict(gps_dlist)
    vn_middle = convert_vn_dict(middle_dlist)
    vn_ontime = convert_vn_dict(acc_ontime_dlist)
    for e in vehicle_list:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        if vn in vn_alarm:
            e['alarmNum'] = vn_alarm.get(vn)['alarmNum']
            e['has_cover_camera'] = 1 if vn_alarm.get(vn)['coverAlarm'] else 0
            if e['alarmNum'] > 0:
                e['has_alarm'] = 1
        if vn in vn_ontime:
            e['accessoriesNum'] = vn_ontime.get(vn)['accessoriesNum']
            e['accessoriesonTimeNum'] = vn_ontime.get(vn)['accessoriesonTimeNum']
        e['has_alarm'] = e.get('has_alarm', 0)
        if vn in vn_risk:
            e['riskNum'] = vn_risk.get(vn)['riskNum']
            e['risk_oneLevel'] = vn_risk.get(vn)['risk_oneLevel']
            e['risk_twoLevel'] = vn_risk.get(vn)['risk_twoLevel']
            e['risk_threeLevel'] = vn_risk.get(vn)['risk_threeLevel']
            e['risk_disposeNum'] = vn_risk.get(vn)['risk_disposeNum']
            e['auto_riskNum'] = vn_risk.get(vn)['auto_riskNum']
            e['dispose_mistake_riskNum'] = vn_risk.get(vn)['dispose_mistake_riskNum']
            e['audit_riskNum'] = vn_risk.get(vn)['audit_riskNum']
            e['audit_reject_riskNum'] = vn_risk.get(vn)['audit_reject_riskNum']
            e['check_riskNum'] = vn_risk.get(vn)['check_riskNum']
            e['check_confirm_riskNum'] = vn_risk.get(vn)['check_confirm_riskNum']
            e['check_reject_riskNum'] = vn_risk.get(vn)['check_reject_riskNum']
        if vn in acc_set:
            e['has_accessory'] = 1
        else:
            e['has_accessory'] = 0
        if vn in vn_gps:
            e['run_time'] = vn_gps.get(vn)['run_time']
            e['online_time'] = vn_gps.get(vn)['online_time']
            e['online'] = 2
        if vn in vn_track:
            track = vn_track.get(vn)
            e['online'] = 1
            e['total_point_count'] = track['total_point_count']
            e['drift_point_count'] = track['drift_point_count']
            e['error_point_count'] = track['error_point_count']
            e['total_mileage'] = track['total_mileage']
            e['continuous_mileage'] = track['continuous_mileage']
        e['online'] = e.get('online', 0)
        if vn in vn_middle:
            e['exception_type'] = vn_middle.get(vn)['exception_type']

        # # 查询条件
        # vehicle_no_to_find = '粤AHB860'
        #
        # # 如果字典中的 'vehicle_no' 键的值与要查找的值匹配，则打印所有字典值
        # if e['vehicle_no'] == vehicle_no_to_find:
        #     for key, value in e.items():
        #         print(f'{key}: {value}')
    # return vehicle_list


def check_online(inlist, day):
    # db = DBUtil(DBType.SCYLLA.value)
    trackDB = DBUtil(DBType.CLICKHOUSE.value, {
        'host': '**************',
        'port': 9000,
        'database': 'gdispx_data',
        'user': 'default',
        'password': '<EMAIL>'
    })
    n = 0
    tn = 0
    for e in inlist:
        if e['online'] == 2:
            tn += 1
    if tn > 100000:
        for e in inlist:
            if e['online'] == 2:
                e['online'] = 1
        Logger.instance().info(f"共找回{tn} / {tn}辆车轨迹")
        return
    for e in inlist:
        if e['online'] != 2:
            continue
        # if e['online'] != 2 and not (e['online'] == 0 and e.get('AI_BOX_sim_code',None)):
        #     continue
        is_online = check_is_online(e, trackDB, day)
        if is_online:
            e['online'] = 1
            n += 1
        else:
            e['online'] = 0
            e['run_time'] = None
            e['online_time'] = None
        if e['online'] == 1:
            e['exception_type'] = None
    # db.close()
    trackDB.close()
    Logger.instance().info(f"共找回{n} / {tn}辆车轨迹")


def adjust_never(jg_datalist, ever_gps, ever_alarm, ever_acc):
    for e in jg_datalist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        if e['online'] == 1:
            e['exception_type'] = ''
        else:
            if str(e.get('exception_type', '')) != '2':
                e['exception_type'] = ''
        if vn in ever_alarm:
            e['never_alarm'] = 0
        elif e.get('alarmNum', 0) > 0:
            e['never_alarm'] = 0
        else:
            e['never_alarm'] = 1
        if vn in ever_acc:
            e['never_acc'] = 0
        elif e['has_accessory']:
            e['never_acc'] = 0
        else:
            e['never_acc'] = 1
        if vn in ever_gps:
            e['never_gps'] = 0
        elif e['online'] == 1:
            e['never_gps'] = 0
        else:
            e['never_gps'] = 1


wh = ['inc_day', 'vehicle_id', 'vehicle_no', 'vehicle_color', 'vehicle_type', 'service_result', 'owner_id',
      'owner_name', 'facilitator_id', 'facilitator_name', 'facilitator_social_credit_code', 'third_party_id',
      'third_party_name', 'third_party_social_credit_code', 'third_party_city_id',
      'access_time', 'device_type', 'master_device_id', 'master_sim_code', 'master_producter_id',
      'master_producter_name',
      'master_type_num', 'master_device_brand', 'AI_BOX_device_id', 'AI_BOX_sim_code', 'AI_BOX_producter_id',
      'AI_BOX_producter_name', 'AI_BOX_type_num', 'AI_BOX_device_brand',
      'area_id', 'area_name',
      'area_short_name', 'city_id', 'city_name', 'city_short_name', 'area_manger_name',
      'area_manger_short_name', 'alarmNum',
      'riskNum', 'risk_oneLevel', 'risk_twoLevel', 'risk_threeLevel',
      'risk_disposeNum', 'auto_riskNum', 'dispose_mistake_riskNum', 'audit_riskNum', 'audit_reject_riskNum',
      'check_riskNum', 'check_confirm_riskNum', 'check_reject_riskNum',
      'accessoriesNum', 'accessoriesonTimeNum',
      'run_time', 'online_time', 'total_point_count', 'drift_point_count', 'error_point_count', 'total_mileage',
      'continuous_mileage', 'online', 'exception_type', 'has_alarm', 'has_accessory',
      'has_cover_camera',
      'never_gps', 'never_alarm', 'never_acc']


def check_is_time_out(report_day):
    report_time = TimeUtils.strptime(report_day, DateFormat.Y_m_d.value)
    now = TimeUtils.now()
    if now.hour - report_time.hour > 9:
        return True
    return False


def collect_cur_jg_dict():
    db = DBUtil(DBType.CLICKHOUSE.value)
    vehicle_sql = f'''
                     select
                 	vria.vehicle_id AS vehicle_id,
                 	vria.vehicle_no AS vehicle_no,
                 	vria.vehicle_color AS vehicle_color,
                 	vria.vehicle_type AS vehicle_type,
                 	vria.service_result AS service_result,
                 	toDate(vria.access_time) AS access_time,
                 	vria.owner_id AS owner_id,
                 	vria.owner_name AS owner_name,
                 	vria.facilitator_id AS facilitator_id,
                 	vria.facilitator_name AS facilitator_name,
                 	vria.facilitator_social_credit_code AS facilitator_social_credit_code,
                 	vria.third_party_id AS third_party_id,
                 	ba1.city_id third_party_city_id,
                 	vria.third_party_name AS third_party_name,
                 	vria.third_party_social_credit_code AS third_party_social_credit_code,
                 	vria.device_type AS device_type,
                 	vria.master_device_id AS master_device_id,
                 	vria.master_sim_code AS master_sim_code,
                 	vria.master_producter_id AS master_producter_id,
                 	vria.master_producter_name AS master_producter_name,
                 	vria.master_type_num AS master_type_num,
                 	vria.master_device_brand AS master_device_brand,
                 	vria.AI_BOX_device_id AS AI_BOX_device_id,
                 	vria.AI_BOX_sim_code AS AI_BOX_sim_code,
                 	vria.AI_BOX_producter_id AS AI_BOX_producter_id,
                 	vria.AI_BOX_producter_name AS AI_BOX_producter_name,
                 	vria.AI_BOX_type_num AS AI_BOX_type_num,
                 	vria.AI_BOX_device_brand AS AI_BOX_device_brand,
                 	ba.id AS area_id,
                 	ba.name AS area_name,
                 	ba.short_name AS area_short_name,
                 	ba.city_id AS city_id,
                 	splitByChar(',', ba.manger_name)[2] AS city_name,
                 	ba.city_short AS city_short_name,
                 	ba.manger_name AS area_manger_name,
                 	ba.manger_short_name AS area_manger_short_name
                 from gdispx_data.vehicle_related_infos_all vria
                 left join gdispx_data.basics_area ba
                 on vria.area_id = ba.id
                 left join gdispx_data.basics_third_party_info btpi
                 on vria.third_party_id = btpi.id
                 left join gdispx_data.basics_area ba1
                 on ba1.id = btpi.area_id
                 where (vria.vehicle_type in ('1', '2') and vria.service_result in ('1', '0', '-1'))
         		   or (vria.vehicle_type in ('3', '4') and vria.service_result in ('1'))
                 '''
    datalist = db.query_to_dictList(vehicle_sql)
    db.close()
    vn_e = {}
    for e in datalist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        getIntDefault(e, 'third_party_id', None)
        getIntDefault(e, 'third_party_city_id', None)
        if str(e['AI_BOX_device_id']) == '0':
            e['AI_BOX_device_id'] = None
        vn_e.setdefault(vn, e)
    return vn_e


def copy_attr_from_e1_to_e2(ce, e, ts):
    if isinstance(ts, dict):
        for k1, k2 in ts.items():
            e[k2] = ce[k1]
    elif isinstance(ts, list) or isinstance(ts, tuple):
        for k in ts:
            e[k] = ce[k]


def p_vehicle_history_day(day, cur_vn_e, otfile=None):
    db = DBUtil(DBType.CLICKHOUSE.value)
    vehicle_sql = f'''
                      select
                  	vhj.vehicle_id AS vehicle_id,
                  	vhj.vehicle_no AS vehicle_no,
                  	vhj.vehicle_color AS vehicle_color,
                  	vhj.vehicle_type AS vehicle_type,
                  	vhj.service_result AS service_result,
                  	vhj.owner_id AS owner_id,
                  	vhj.owner_name AS owner_name,
                  	vhj.facilitator_id AS facilitator_id,
                  	vhj.facilitator_name AS facilitator_name,
                  	vhj.third_party_id AS third_party_id,
                  	vhj.third_party_name AS third_party_name,
                  	vhj.master_sim_code AS master_sim_code,
                  	ba.id AS area_id,
                  	ba.name AS area_name,
                  	ba.short_name AS area_short_name,
                  	ba.city_id AS city_id,
                  	splitByChar(',', ba.manger_name)[2] AS city_name,
                  	ba.city_short AS city_short_name,
                  	ba.manger_name AS area_manger_name,
                  	ba.manger_short_name AS area_manger_short_name
                  from gdispx_data.vehicle_history_jianguan vhj
                  left join gdispx_data.basics_area ba
                  on toInt32(vhj.area_id) = ba.id
                  where vhj.`create_date` = '{day}'
                  '''
    datalist = db.query_to_dictList(vehicle_sql)
    ts = ['facilitator_social_credit_code', 'third_party_social_credit_code',
          'third_party_city_id',
          'access_time', 'device_type', 'master_device_id', 'master_producter_id',
          'master_producter_name', 'master_type_num', 'master_device_brand',
          'AI_BOX_device_id', 'AI_BOX_sim_code', 'AI_BOX_producter_id',
          'AI_BOX_producter_name', 'AI_BOX_type_num', 'AI_BOX_device_brand',
          ]
    for e in datalist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        ce = cur_vn_e.get(vn)
        getIntDefault(e, 'third_party_id', None)
        vehicle_id = getIntDefault(e, 'vehicle_id', None)
        if not vehicle_id and ce:
            e['vehicle_id'] = ce['vehicle_id']
        if ce:
            copy_attr_from_e1_to_e2(ce, e, ts)
        if not e['third_party_id']:
            e['third_party_social_credit_code'] = ''
            e['third_party_city_id'] = None
        e['inc_day'] = day
        # if e['access_time']:
        #     e['access_time'] = TimeUtils.strptime(e['access_time'], DateFormat.Y_m_d.value)
        # else:
        #     e['access_time'] = None
    h = ['inc_day',
         'vehicle_id', 'vehicle_no', 'vehicle_color', 'vehicle_type', 'service_result',
         'owner_id', 'owner_name',
         'facilitator_id', 'facilitator_name', 'facilitator_social_credit_code',
         'third_party_id', 'third_party_name', 'third_party_social_credit_code',
         'third_party_city_id',
         'access_time', 'device_type',
         'master_device_id', 'master_sim_code', 'master_producter_id',
         'master_producter_name', 'master_type_num', 'master_device_brand',
         'AI_BOX_device_id', 'AI_BOX_sim_code', 'AI_BOX_producter_id',
         'AI_BOX_producter_name', 'AI_BOX_type_num', 'AI_BOX_device_brand',
         'area_id', 'area_name', 'area_short_name',
         'city_id', 'city_name', 'city_short_name',
         'area_manger_name', 'area_manger_short_name']
    if otfile:
        write_csv(otfile, h, datalist)
        return None
    else:
        return datalist


def collect_history_jg(report_day):
    try:
        load_owner_info()
    except Exception as ex:
        pass
    cur_vn_e = collect_cur_jg_dict()
    jg_datalist = p_vehicle_history_day(report_day, cur_vn_e)
    return jg_datalist


def getFloatDefault(e, key, default_value=None):
    if not e[key] or float(e[key]) == 0:
        e[key] = default_value
    else:
        e[key] = float(e[key])
    return e[key]


def convert_g(g):
    for e in g:
        getIntDefault(e, 'vehicle_id', 0)
        getIntDefault(e, 'vehicle_color', 0)
        getIntDefault(e, 'vehicle_type', 0)
        getIntDefault(e, 'owner_id', 0)
        getIntDefault(e, 'facilitator_id', None)
        getIntDefault(e, 'third_party_id', None)
        getIntDefault(e, 'third_party_city_id', None)
        getIntDefault(e, 'device_type', None)
        getIntDefault(e, 'master_producter_id', None)
        getIntDefault(e, 'AI_BOX_producter_id', None)
        getIntDefault(e, 'area_id', 0)
        getIntDefault(e, 'city_id', 0)
        getIntDefault(e, 'alarmNum', 0)
        getIntDefault(e, 'riskNum', 0)
        getIntDefault(e, 'risk_oneLevel', 0)
        getIntDefault(e, 'risk_twoLevel', 0)
        getIntDefault(e, 'risk_threeLevel', 0)
        getIntDefault(e, 'risk_disposeNum', 0)
        getIntDefault(e, 'auto_riskNum', 0)
        getIntDefault(e, 'dispose_mistake_riskNum', 0)
        getIntDefault(e, 'audit_riskNum', 0)
        getIntDefault(e, 'audit_reject_riskNum', 0)
        getIntDefault(e, 'check_riskNum', 0)
        getIntDefault(e, 'check_confirm_riskNum', 0)
        getIntDefault(e, 'check_reject_riskNum', 0)
        getIntDefault(e, 'accessoriesNum', 0)
        getIntDefault(e, 'accessoriesonTimeNum', 0)
        getIntDefault(e, 'run_time', 0)
        getIntDefault(e, 'online_time', 0)
        getIntDefault(e, 'total_point_count', 0)
        getIntDefault(e, 'drift_point_count', 0)
        getIntDefault(e, 'error_point_count', 0)
        getFloatDefault(e, 'total_mileage', 0)
        getFloatDefault(e, 'continuous_mileage', 0)
        getIntDefault(e, 'online', 0)
        getIntDefault(e, 'exception_type', 0)
        getIntDefault(e, 'has_alarm', 0)
        getIntDefault(e, 'has_accessory', 0)
        getIntDefault(e, 'has_cover_camera', 0)
        e['never_gps'] = int(e['never_gps'])
        e['never_alarm'] = int(e['never_alarm'])
        e['never_acc'] = int(e['never_acc'])
        e['inc_day'] = TimeUtils.strptime(e['inc_day'], DateFormat.Y_m_d.value)
        if e['access_time']:
            e['access_time'] = TimeUtils.strptime(e['access_time'], DateFormat.Y_m_d.value)
        else:
            e['access_time'] = None


def save_to_db_by_inlist(db, g, has_need_convert=True):
    if has_need_convert:
        convert_g(g)
    insert_sql = f'''
                    INSERT into gdispx_data.vehicle_wide_day_all 
                    (`inc_day`,`vehicle_id`,`vehicle_no`,`vehicle_color`,`vehicle_type`,`service_result`,
                    `access_time`,`owner_id`, `owner_name`, `facilitator_id`, `facilitator_name`,
                     `facilitator_social_credit_code`,`third_party_id`, `third_party_name`,`third_party_social_credit_code`,
                     `third_party_city_id`,
                     `device_type`,`master_device_id`,`master_sim_code`,`master_producter_id`,
                    `master_producter_name`,`master_type_num`,`master_device_brand`,
                    `AI_BOX_device_id`,`AI_BOX_sim_code`,`AI_BOX_producter_id`,
                    `AI_BOX_producter_name`,`AI_BOX_type_num`,`AI_BOX_device_brand`,
                    `area_id`,`area_name`, `area_short_name` ,`city_id`,`city_name`,`city_short_name`,
                    `area_manger_name` , `area_manger_short_name`,
                    `alarmNum`, `riskNum` , 
                    `risk_oneLevel`,`risk_twoLevel`,`risk_threeLevel`,
                    `risk_disposeNum`,`auto_riskNum`,`dispose_mistake_riskNum`,`audit_riskNum`,
                    `audit_reject_riskNum`,`check_riskNum`,`check_confirm_riskNum`,`check_reject_riskNum`,
                    `accessoriesNum`,`accessoriesonTimeNum`, `run_time`,`online_time` ,
                    `total_point_count`,`drift_point_count`,`error_point_count`,
                    `total_mileage`,`continuous_mileage`,`online`,`exception_type`,
                    `has_alarm`, `has_accessory`,`has_cover_camera` ,`never_gps`, `never_alarm`,
                     `never_acc`
                    ) values 
                '''
    db.excute(insert_sql, g)


def save_to_db(infile, report_day):
    db = DBUtil(DBType.CLICKHOUSE.value)
    db.excute(f"alter table gdispx_data.vehicle_wide_day_all drop partition '{report_day}' ")
    for g in read_by_trunk(
            readCsv(infile, print_count=100000, print_title=f'{report_day}'), 1000):
        for e in g:
            e['vehicle_id'] = str(abs(int(e['vehicle_id'])))[:9]
        save_to_db_by_inlist(db, g)
    db.close()


@countTime("生产当日宽表")
def do_export_cur_day_report(report_day):
    Logger.instance().info(f'{report_day} 开始生产宽表...')
    # is_time_out = check_is_time_out(report_day)
    is_time_out = True
    if not is_time_out:
        Logger.instance().info("通过实时监管表获取当日监管车辆..")
        vehicle_list = collect_cur_jg(report_day)
    else:
        Logger.instance().info("通过历史监管表获取当日监管车辆..")
        vehicle_list = collect_history_jg(report_day)
    ever_gps, ever_alarm, ever_acc = collect_ever(report_day)
    alarm_dlist, risk_dlist, acc_dlist, track_dlist, \
    gps_dlist, middle_dlist, acc_ontime_dlist = collect_history_dayInfo(report_day)
    attach_data_to_vehicle(vehicle_list, alarm_dlist, risk_dlist,
                           acc_dlist, track_dlist, gps_dlist, middle_dlist, acc_ontime_dlist)
    check_online(vehicle_list, report_day)
    vehicle_no_to_find = '粤AHB860'
    for shujuzu_key in vehicle_list:
        # 如果字典中的 'vehicle_no' 键的值与要查找的值匹配
        if shujuzu_key['vehicle_no'] == vehicle_no_to_find:
            # 遍历字典并打印所有键值对
            for key, value in shujuzu_key.items():
                print(f'{key}: {value}')
    sys.exit()
    adjust_never(vehicle_list, ever_gps, ever_alarm, ever_acc)
    write_csv(join_path(bpath, f'{report_day}.csv'), wh, vehicle_list)
    save_to_db(join_path(bpath, f'{report_day}.csv'), report_day)
    copyfile(join_path(bpath, f'{report_day}.csv'), join_path(r'/data/track_report', f'wide_{report_day}.csv'))

    Logger.instance().info(f'{report_day} 生产宽表完毕!')


def collect_change(day, check_risk_only):
    db = DBUtil(DBType.CLICKHOUSE.value)
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=day)
    yesterday = TimeUtils.DeltaDay(-1, cur_day=day)
    alarm_change_sql = f'''
        select A.vehicle_no AS vehicle_no, A.vehicle_color AS vehicle_color,B.alarmNum AS alarmNum
        from 
        (select vehicle_no,vehicle_color,alarmNum from gdispx_data.vehicle_wide_day_all final where inc_day = '{day}') A
        left join
        (
        SELECT vehicle_no, vehicle_color, sum(alarmNum) AS alarmNum
        FROM gdispx_data.statistics_original_alarm_day_all soada
        where alarm_time = '{day}'
        group by vehicle_no, vehicle_color
        ) B
        on A.vehicle_no = B.vehicle_no and A.vehicle_color = B.vehicle_color
        where A.alarmNum != B.alarmNum
    '''

    risk_change_sql = f'''
        select 
        H.vehicle_no AS vehicle_no, H.vehicle_color AS vehicle_color,
        D.riskNum riskNum,
        D.risk_oneLevel risk_oneLevel,
        D.risk_twoLevel risk_twoLevel,
        D.risk_threeLevel risk_threeLevel,
        D.risk_disposeNum risk_disposeNum,
        D.auto_riskNum auto_riskNum,
        D.dispose_mistake_riskNum dispose_mistake_riskNum,
        D.audit_riskNum audit_riskNum,
        D.audit_reject_riskNum audit_reject_riskNum,
        D.check_riskNum check_riskNum,
        D.check_confirm_riskNum check_confirm_riskNum,
        D.check_reject_riskNum check_reject_riskNum
        from 
        (
        select vehicle_no,vehicle_color,riskNum , risk_oneLevel,risk_twoLevel,
        risk_threeLevel,risk_disposeNum,auto_riskNum,dispose_mistake_riskNum,
        audit_riskNum,audit_reject_riskNum,check_riskNum,check_confirm_riskNum,check_reject_riskNum
        from gdispx_data.vehicle_wide_day_all final  where inc_day = '{day}'
        ) H
        left join
        (select
            vehicle_no ,
            toUInt8(vehicle_color) vehicle_color,
            risk_oneLevel+risk_twoLevel+risk_threeLevel riskNum,
            sum(is_manual_dispose) risk_disposeNum,
            count(1) - risk_disposeNum auto_riskNum,
            sum(dispose_is_mistake) dispose_mistake_riskNum,
            sum(has_audit) audit_riskNum,
            audit_riskNum - sum(audit_is_mistake) audit_reject_riskNum,
            sum(has_check) check_riskNum,
            sum(check_is_mistake) check_confirm_riskNum,
            check_riskNum - check_confirm_riskNum check_reject_riskNum,
            sum(oneLevel) risk_oneLevel,
            sum(twoLevel) risk_twoLevel,
            sum(threeLevel) risk_threeLevel
        from 
            (SELECT 
                id,
                vehicle_no,
                vehicle_color,
                is_manual_dispose,
                dispose_is_mistake,
                has_audit,
                audit_is_mistake,
                has_check,
                check_is_mistake,
                if(check_is_mistake=1 and  oneLevel =1, 0,oneLevel) oneLevel,
                if(check_is_mistake=1 and  twoLevel =1, 0,twoLevel) twoLevel,
                if(check_is_mistake=1 and  threeLevel =1, 0,threeLevel) threeLevel
             from 
                    (SELECT 
                    distinct
                    vehicle_no ,
                    vehicle_color,
                    id,
                    if(alarm_level=1,1,0) oneLevel,
                    if(alarm_level=2,1,0) twoLevel,
                    if(alarm_level=3,1,0) threeLevel
                    from gdispx_data.business_ai_alarm_info 
                    where toDate(alarm_time) = '{day}' 
                    and alarm_code in (10402,10401,10413,90001,90002,90007,10410,10405,10412)
                    and id != '') A
                left JOIN 
                    (SELECT 
                    alarm_id,
                    max(if(dispose_type=0 and process_unit !='监管系统', 1, 0)) `is_manual_dispose`,
                    max(if(process_status in (3,7) and dispose_type=0, 1, 0)) `dispose_is_mistake`,
                    max(if(process_user != '' and dispose_type=1, 1, 0)) `has_audit`,
                    max(if(process_status=3 and dispose_type=1, 1, 0)) `audit_is_mistake`,
                    max(if(dispose_type=7, 1, 0)) `has_check`,
                    max(if(process_status=2 and dispose_type=7, 1, 0)) `check_is_mistake`
                    from gdispx_data.business_alarm_audit_info final
                    where toDate(create_time) between '{day}' and '{risk_dispose_end_day}'
                    and alarm_id !=''
                    group by alarm_id) B on A.id = B.alarm_id
            ) C
        group by vehicle_no,vehicle_color) D
        on H.vehicle_no = D.vehicle_no and H.vehicle_color = D.vehicle_color
        where H.riskNum != D.riskNum or H.risk_disposeNum != D.risk_disposeNum
            or H.check_riskNum != D.check_riskNum or D.dispose_mistake_riskNum != H.dispose_mistake_riskNum
    '''
    acc_change_sql = f'''
        SELECT
            vehicle_no,vehicle_color
        from 
        (select vehicle_no,vehicle_color
        from gdispx_data.vehicle_wide_day_all final  where inc_day = '{day}'
        and has_accessory = 0
        ) A
        where A.vehicle_no in
        (
        select 
            distinct
            vehicle_no
            from gdispx_data.business_accessories_info
            where toDate(create_time) = '{day}'
        )
    '''

    acc_ontime_change_sql = f'''
        select
        A.vehicle_no AS vehicle_no, A.vehicle_color AS vehicle_color,
        B.accessoriesNum AS accessoriesNum,B.accessoriesonTimeNum AS accessoriesonTimeNum
        from 
        (select vehicle_no,vehicle_color,accessoriesNum , accessoriesonTimeNum
        from gdispx_data.vehicle_wide_day_all final  where inc_day = '{day}') A
        left join
        (
        select vehicle_no, vehicle_color, 
        alarmNum AS accessoriesNum, onTimeNum AS accessoriesonTimeNum
        from gdispx_data.statistics_on_time_accessories_day where toDate(alarm_time) = '{day}'
        ) B
        on A.vehicle_no = B.vehicle_no and A.vehicle_color = B.vehicle_color
        where A.accessoriesNum != B.accessoriesNum 
        or A.accessoriesonTimeNum != accessoriesonTimeNum 
    '''
    never_gps_change_sql = f'''
        SELECT
            A.vehicle_no as vehicle_no,vehicle_color
        from 
        (
        select vehicle_no,vehicle_color
        from gdispx_data.vehicle_wide_day_all final  where inc_day = '{day}'
        and never_gps = 1
        ) A 
        inner join 
        (
        select 
            vehicle_no
            from gdispx_data.vehicle_wide_day_all final 
            where toDate(create_time) = '{yesterday}'
            and never_gps = 0
        ) B on A.vehicle_no = B.vehicle_no 
    '''

    never_alarm_change_sql = f'''
            SELECT
                A.vehicle_no as vehicle_no,vehicle_color
            from 
            (
            select vehicle_no,vehicle_color
            from gdispx_data.vehicle_wide_day_all final  where inc_day = '{day}'
            and never_alarm = 1
            ) A 
            inner join
            (
            select 
                vehicle_no
                from gdispx_data.vehicle_wide_day_all final 
                where toDate(create_time) = '{yesterday}'
                and never_alarm = 0
            ) B on A.vehicle_no = B.vehicle_no 
        '''
    never_acc_change_sql = f'''
                SELECT
                    A.vehicle_no as vehicle_no,vehicle_color
                from 
                (
                select vehicle_no,vehicle_color
                from gdispx_data.vehicle_wide_day_all final  where inc_day = '{day}'
                and never_acc = 1
                ) A 
                inner join
                (
                select 
                    vehicle_no
                    from gdispx_data.vehicle_wide_day_all final 
                    where toDate(create_time) = '{yesterday}'
                    and never_acc = 0
                ) B on A.vehicle_no = B.vehicle_no 
            '''
    wide_sql = f'''
        select *
        from gdispx_data.vehicle_wide_day_all final  where inc_day = '{day}'
    '''

    middle_sql = f'''
                    select vehicle_no,vehicle_color,exception_type
                        from gdispx_data.vehicle_related_infos_middle 
                        where toDate(create_time) = '{day}'
                        and exception_type = 2
            '''

    alarm_change_list = db.query_to_dictList(alarm_change_sql) if not check_risk_only else []
    print('alarm_change_list done!')
    risk_change_list = db.query_to_dictList(risk_change_sql)
    print('risk_change_list done!')
    acc_change_list = db.query_to_dictList(acc_change_sql) if not check_risk_only else []
    print('acc_change_list done!')
    acc_ontime_change_list = db.query_to_dictList(acc_ontime_change_sql) if not check_risk_only else []
    print('acc_ontime_change_list done!')
    never_gps_change_list = db.query_to_dictList(never_gps_change_sql) if not check_risk_only else []
    print('never_gps_change_list done!')
    never_alarm_change_list = db.query_to_dictList(never_alarm_change_sql) if not check_risk_only else []
    print('never_alarm_change_list done!')
    never_acc_change_list = db.query_to_dictList(never_acc_change_sql) if not check_risk_only else []
    print('never_acc_change_list done!')
    middle_dlist = db.query_to_dictList(middle_sql)
    print('middle_dlist done!')
    Logger.instance().info(f'''
      {day}  alarm变更：{len(alarm_change_list)} ,
            risk变更:{len(risk_change_list)} ,
            acc变更:{len(acc_change_list)} ,
            ontime变更:{len(acc_ontime_change_list)} ,
            never_gps变更:{len(never_gps_change_list)} ,
            never_alarm变更:{len(never_alarm_change_list)} ,
            never_acc变更:{len(never_acc_change_list)} ,
            疑似屏蔽信号变更:{len(middle_dlist)}
    ''')
    vlist = db.query_to_dictList(wide_sql)

    db.close()
    return alarm_change_list, risk_change_list, \
           acc_change_list, acc_ontime_change_list, \
           never_gps_change_list, never_alarm_change_list, never_acc_change_list, middle_dlist, vlist


def do_inc_update_byDay(day, check_risk_only):
    if check_risk_only:
        Logger.instance().info(f"{day} 仅更新风险处置相关信息...")
    alarm_change_list, risk_change_list, \
    acc_change_list, acc_ontime_change_list, \
    never_gps_change_list, never_alarm_change_list, \
    never_acc_change_list, middle_dlist, vlist = collect_change(day, check_risk_only)
    vn_alarm = convert_vn_dict(alarm_change_list)
    vn_risk = convert_vn_dict(risk_change_list)
    vn_acc = convert_vn_dict(acc_change_list, True)
    vn_ontime = convert_vn_dict(acc_ontime_change_list)
    vn_never_gps = convert_vn_dict(never_gps_change_list, True)
    vn_never_alarm = convert_vn_dict(never_alarm_change_list, True)
    vn_never_acc = convert_vn_dict(never_acc_change_list, True)
    vn_middle = convert_vn_dict(middle_dlist, True)
    outlist = []
    for e in vlist:
        vn = (e['vehicle_no'], str(e['vehicle_color']))
        is_change = False
        if vn in vn_alarm:
            e['alarmNum'] = vn_alarm.get(vn)['alarmNum']
            is_change = True
        if vn in vn_risk:
            e['riskNum'] = vn_risk.get(vn)['riskNum']
            e['risk_oneLevel'] = vn_risk.get(vn)['risk_oneLevel']
            e['risk_twoLevel'] = vn_risk.get(vn)['risk_twoLevel']
            e['risk_threeLevel'] = vn_risk.get(vn)['risk_threeLevel']
            e['risk_disposeNum'] = vn_risk.get(vn)['risk_disposeNum']
            e['auto_riskNum'] = vn_risk.get(vn)['auto_riskNum']
            e['dispose_mistake_riskNum'] = vn_risk.get(vn)['dispose_mistake_riskNum']
            e['audit_riskNum'] = vn_risk.get(vn)['audit_riskNum']
            e['audit_reject_riskNum'] = vn_risk.get(vn)['audit_reject_riskNum']
            e['check_riskNum'] = vn_risk.get(vn)['check_riskNum']
            e['check_confirm_riskNum'] = vn_risk.get(vn)['check_confirm_riskNum']
            e['check_reject_riskNum'] = vn_risk.get(vn)['check_reject_riskNum']
            is_change = True
        if vn in vn_acc:
            e['has_accessory'] = 1
            e['never_acc'] = 0
            is_change = True
        if vn in vn_ontime:
            e['accessoriesNum'] = vn_ontime.get(vn)['accessoriesNum']
            e['accessoriesonTimeNum'] = vn_ontime.get(vn)['accessoriesonTimeNum']
            is_change = True
        if vn in vn_never_gps:
            e['never_gps'] = 0
            is_change = True
        if vn in vn_never_alarm:
            e['never_alarm'] = 0
            is_change = True
        if vn in vn_never_acc:
            e['never_acc'] = 0
            is_change = True
        if vn in vn_middle and e['exception_type'] != 2:
            e['exception_type'] = 2
            is_change = True
        if vn not in vn_middle and e['exception_type'] == 2:
            e['exception_type'] = 0
            is_change = True
        if is_change:
            outlist.append(e)
    Logger.instance().info(f"{day}需要变更的记录: {len(outlist)} ")
    if not outlist:
        return
    db = DBUtil(DBType.CLICKHOUSE.value)
    for g in read_by_trunk(outlist, 1000):
        save_to_db_by_inlist(db, g, False)
    db.close()


@countTime("增量更新")
def do_inc_update(report_day):
    t = TimeUtils.strptime(report_day, DateFormat.Y_m_d.value)
    # 解决风险处置工作日的问题  +3工作日（国庆 7+3）
    for i in range(11, 0, -1):
      try:  
        day = TimeUtils.DeltaDay(-i, cur_day=t)
        Logger.instance().info(f"{day} 开始增量更新...")
        check_risk_only = False
        if i > 4:
            check_risk_only = True
        do_inc_update_byDay(day, check_risk_only)
        Logger.instance().info(f"{day} 增量更新完毕...")
      except Exception as ex:
        print(ex)

def main(report_day):
    try:
      Logger.instance("宽表日结", join_path(bpath, 'log.log'))
      do_inc_update(report_day)
      do_export_cur_day_report(report_day)
    except Exception as ex:
      print(ex)

def do_update_from(start_day):
    '''
    指定从某一天开始更新宽表
    '''
    Logger.instance("宽表日结更新", join_path(bpath, 'log.log'))
    end_day = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    for day in from_A_to_B(start_day, end_day):
        Logger.instance().info(f"{day} 开始增量更新...")
        do_inc_update_byDay(day, False)
        Logger.instance().info(f"{day} 增量更新完毕...")


def re_create_inc_day_report_from(start_day, end_day):
    for report_day in from_A_to_B(start_day, end_day):
         do_export_cur_day_report(report_day)


if __name__ == '__main__':
    report_day = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    #main(report_day)
    do_export_cur_day_report('2024-05-29')
    do_inc_update_byDay('2024-05-29', False)
    #do_inc_update('2023-04-08')
    #do_update_from('2023-04-25')
    #for report_day in from_A_to_B('2023-02-05','2023-02-11'):
    #    main(report_day)
    #re_create_inc_day_report_from('2023-07-16', '2023-07-16')
    #start, end, days = TimeUtils.getLastMonthRange()
    #do_inc_update_byDay('2024-05-01',False)
    #do_inc_update_byDay('2024-04-07',False)
    #do_inc_update_byDay('2024-03-01',False)
    #do_inc_update_byDay('2024-03-24',False)
    #do_inc_update_byDay('2024-03-27',False)
    #do_inc_update_byDay('2024-03-26',True)
    #do_inc_update_byDay('2024-03-25',True)
    #print('0309')
    #for report_day in from_A_to_B('2024-05-20','2024-05-26'):
        #print(report_day)
        #do_inc_update_byDay(report_day,False)
        #do_export_cur_day_report(report_day)
