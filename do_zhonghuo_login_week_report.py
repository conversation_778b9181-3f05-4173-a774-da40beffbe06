from yyutils import *

'''
每周一统计
'''
bpath = r'/data/track_report'


weekDay_dict = {
    0:'周一',
    1:'周二',
    2:'周三',
    3:'周四',
    4:'周五',
    5:'周六',
    6:'周日',
}

def export_every_day_e(g2, ne):
    for e in g2:
        cur_day = TimeUtils.strptime(str(e['inc_day']))
        w = cur_day.weekday()
        ne[weekDay_dict.get(w)] = e['jg_day_n']


def export_total(outlist):
    ne = {
        '地市': '-',
        '需监管重型货车': sum([e['需监管重型货车'] for e in outlist]),
        '入网重型货车': sum([e['入网重型货车'] for e in outlist]),
        '周一': sum([e['周一'] for e in outlist]),
        '周二': sum([e['周二'] for e in outlist]),
        '周三': sum([e['周三'] for e in outlist]),
        '周四': sum([e['周四'] for e in outlist]),
        '周五': sum([e['周五'] for e in outlist]),
        '周六': sum([e['周六'] for e in outlist]),
        '周日': sum([e['周日'] for e in outlist]),
    }
    return ne


def getWeekStats(start, end):
    yz_sql = f'''
        select 
        A.city_id city_id,
        B.name city_name,
        A.n yz_week_n
        from
        (select
        city_id,count(distinct vehicle_no,vehicle_color) n
        from gdispx_data.vehicle_history_yunzheng
        where create_date between '{start}' and '{end}'
        and vehicle_type = '4'
        group by city_id) A
        left join gdispx_data.basics_area B on A.city_id = toString(B.id)
    '''

    jg_sql = f'''
        select
        city_id,
        any(city_name) city_name,
        count(distinct vehicle_no)  jg_week_n
        from  gdispx_data.vehicle_wide_day_all final
        where inc_day between '{start}' and '{end}'
        and vehicle_type = 4
        group by city_id
    '''
    everyDay_sql = f'''
        select 
        city_id,
        any(city_name) city_name,
        inc_day,
        count(1)  jg_day_n
        from gdispx_data.vehicle_wide_day_all final
        where inc_day between '{start}' and '{end}'
        and vehicle_type = 4
        and online = 1
        group by city_id,inc_day
    '''

    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist1 = db.query_to_dictList(yz_sql)
    datalist2 = db.query_to_dictList(jg_sql)
    datalist3 = db.query_to_dictList(everyDay_sql)
    city_e1 = list_2_dict(datalist2,'city_id')
    city_g2 = list_2_dictlist(datalist3,'city_id')
    outlist = []
    for e in datalist1:
        city_id = int(e['city_id'])
        ne = {'地市':e['city_name'],'需监管重型货车':e['yz_week_n']}
        e1 = city_e1.get(city_id)
        ne['入网重型货车'] = e1['jg_week_n']
        g2= city_g2.get(city_id)
        export_every_day_e(g2,ne)
        outlist.append(ne)
    ne = export_total(outlist)
    outlist.append(ne)
    h = ['地市','需监管重型货车','入网重型货车','周一','周二','周三','周四','周五','周六','周日']
    otfile = join_path(bpath,f'{start}-{end}全省重型火车上线情况.csv')
    write_csv(otfile,h,outlist)

def main(start, end):
    getWeekStats(start,end)


if __name__ == '__main__':
    start = TimeUtils.DeltaDay(-7)
    end = TimeUtils.DeltaDay(-1)
    main(start,end)
    # print(TimeUtils.strptime(TimeUtils.DeltaDay(-1)).weekday())

