#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

# detail_select = """
# select
#     vrvri.manger_short_name
#     , splitByChar(',', vrvri.manger_short_name)[2] as `城市`
#     , vrvri.vehicle_no
#     , vrvri.vehicle_type
#     , vrvri.access_time
#     , vrvri.vehicle_color
#     , vrvri.owner_name as `业户`
#     , vrvri.facilitator_name as `服务商`
#     , vrvri.facilitator_social_credit_code
#     , vrvri.master_sim_code
#     , vrvri.master_producter_name
#     , transform(vrvri.service_result, ['1','0','-1'], ['营运','待营运','报停、过户、过户转出'], '其他') `营运状态`
#     , case when notEmpty(vrvri.master_sim_code) and notEmpty(vrvri.AI_BOX_sim_code) then '分体机' else '一体机' end as `机型`
#     , case when vrvri.master_producter_name like '非粤标设备%' then '非粤标' when empty(vrvri.master_producter_name) then '' else '粤标' end as `设备类型`
# from gdispx_data.v_regulator_vehicle_related_infos vrvri
# """

detail_select = """
select
    vrvri.manger_short_name
    , splitByChar(',', vrvri.manger_short_name)[2] as `城市`
    , vrvri.master_sim_code as `sim卡号`
    , vrvri.vehicle_no as `车牌号`
    , transform(vrvri.vehicle_color, ['1','2','93','94'], ['蓝色','黄色','黄绿双拼色','渐变绿色']) as `车牌颜色`
    , transform(vrvri.vehicle_type, ['1','3','4'], ['客运车辆','危险货运','重型货车']) as `车辆类型`  
    , vrvri.owner_name as `业户`
    , vrvri.facilitator_name as `服务商`
    , vrvri.master_producter_name as `设备厂商`
    , transform(vrvri.service_result, ['1','0','-1'], ['营运','待营运','报停、过户、过户转出'], '其他') `营运状态`
    , case when notEmpty(vrvri.master_sim_code) and notEmpty(vrvri.AI_BOX_sim_code) then '分体机' else '一体机' end as `机型`
    , case when vrvri.master_producter_name like '非粤标设备%' then '非粤标' when empty(vrvri.master_producter_name) then '' else '粤标' end as `设备类型`
"""

not_innet_query_sql = detail_select + """
from gdispx_data.v_regulator_vehicle_related_infos vrvri
where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
 and trim(vrvri.master_sim_code) not in (
    select device_sim from gdispx_data.t_access_manage where `status` = 1 
 )
"""

not_online_query_sql = detail_select + """
from gdispx_data.v_regulator_vehicle_related_infos vrvri
left join gdispx_data.business_gpsdatas bg on trim(bg.device_id) = trim(vrvri.master_sim_code)
where bg.gps_time <= addDays(now(),-7)
 and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
"""

# 改成取全部的车辆
# piaoyi_query_sql = "WITH (select avg(tr.drift_point_count) from gdispx_data.track_report tr) as drift_point_count_avg"
piaoyi_query_sql = detail_select
piaoyi_query_sql += """
    , total_mileage_sum AS `轨迹总里程`
    , drift_point_count_sum as `统计周期内漂移次数`
from gdispx_data.v_regulator_vehicle_related_infos vrvri
join (select imei, sum(drift_point_count) drift_point_count_sum, sum(total_mileage) total_mileage_sum
     from gdispx_data.track_report where `date` >= '{track_begin_date}' and `date` < '{track_end_date}'
     group by imei) tr on tr.imei = trim(vrvri.master_sim_code)
-- where tr.drift_point_count_sum > drift_point_count_avg
where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
"""

mileage_incomplete_query_sql = detail_select + """
    , total_mileage_sum AS `轨迹总里程`
    , continuous_mileage_sum as `轨迹完整里程`
from gdispx_data.v_regulator_vehicle_related_infos vrvri
join (select imei, sum(total_mileage) total_mileage_sum, sum(continuous_mileage) continuous_mileage_sum
     from gdispx_data.track_report where `date` >= '{track_begin_date}' and `date` < '{track_end_date}'
     group by imei) tr on tr.imei = trim(vrvri.master_sim_code)
where tr.total_mileage_sum > tr.continuous_mileage_sum
and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
"""

extra_select = """
    , vrvri.master_type_num as `主设备`
    , vrvri.access_time as `接入时间`
"""

never_gps_query_sql = detail_select + extra_select + """
from gdispx_data.v_regulator_vehicle_related_infos vrvri
where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
and vehicle_id in (
    select vehicle_id from gdispx_data.business_vehicle_error_state bves 
    where noGps = 1
)
"""

never_alarm_query_sql = detail_select + extra_select + """
from gdispx_data.v_regulator_vehicle_related_infos vrvri
where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
and vehicle_id in (
    select vehicle_id from gdispx_data.business_vehicle_error_state bves 
    where noAlarm = 1
)
"""

never_acc_query_sql = detail_select + extra_select + """
from gdispx_data.v_regulator_vehicle_related_infos vrvri
where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
and vehicle_id in (
    select vehicle_id from gdispx_data.business_vehicle_error_state bves 
    where noAccessories = 1
)
"""


def query_then_write_csv(query_sql, client, output_file):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join(['"' + col[0] + '"' for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join(['"' + str(x) + '"' for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    # TODO: 这里的open都需要增加encoding = utf-8:
    try:
        client = ck.get_client()
        file_suffix = '_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open('未上线车辆明细' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(not_online_query_sql, client, output_file)
        with open('漂移车辆明细' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(
                piaoyi_query_sql.format(track_begin_date=begin_date.replace('-', '')[-6:],
                                        track_end_date=end_date.replace('-', '')[-6:]),
                client, output_file)
        with open('里程不完整车辆明细' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(
                mileage_incomplete_query_sql.format(track_begin_date=begin_date.replace('-', '')[-6:],
                                                    track_end_date=end_date.replace('-', '')[-6:]),
                client, output_file)

        with open('长期定位异常车辆明细' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(never_gps_query_sql, client, output_file)
        with open('长期未报警车辆明细' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(never_alarm_query_sql, client, output_file)
        with open('长期附件异常车辆明细' + file_suffix, 'w', encoding='utf-8') as output_file:
            query_then_write_csv(never_acc_query_sql, client, output_file)
    finally:
        ck.disconnect()


if __name__ == '__main__':
    export('2021-10-10', '2021-10-15')
