from yyutils import *
from utils.config import bpath
from utils.YYSQLTemplate import YYSQLTemplate
from utils.config import orderByCity, FLOORtoPercent
import month_for_city_report_sheet1 as s1
import month_for_city_report_sheet2 as s2
import month_for_city_report_sheet3 as s3
import month_for_city_report_sheet4 as s4

template_file = 'templates/template_month_{year}年{month}月运营月报表格附件（数值）终版.xlsx'


def fill_sheet1(elhander, inlist, se, year, month):
    sheet = elhander.activeWorksheet("表1广东省分辖区市月度考核表")
    row_styles = elhander.copyRowStyle(sheet, 5)
    row_styles1 = elhander.copyRowStyle(sheet, 26)
    last_row = elhander.copyRow(sheet, 28)
    elhander.clearData(sheet, 5)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)
    elhander.fillData(sheet, [se], row_styles1)
    sheet.append([])
    sheet.append(last_row)


def fill_sheet2(elhander, inlist, se, year, month):
    sheet = elhander.activeWorksheet("表2广东省监管车辆入网、上线、数据合格情况率分辖区市统计表")
    row_styles = elhander.copyRowStyle(sheet, 5)
    row_styles1 = elhander.copyRowStyle(sheet, 26)
    last_row = elhander.copyRow(sheet, 28)
    elhander.clearData(sheet, 5)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)
    elhander.fillData(sheet, [se], row_styles1)
    sheet.append([])
    sheet.append(last_row)


def fill_sheet3(elhander, inlist, se, year, month):
    sheet = elhander.activeWorksheet("表3广东省车均风险分辖区市统计表")
    row_styles = elhander.copyRowStyle(sheet, 4)
    row_styles1 = elhander.copyRowStyle(sheet, 25)
    last_row = elhander.copyRow(sheet, 26)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)
    elhander.fillData(sheet, [se], row_styles1)
    sheet.append([])
    sheet.append(last_row)


def fill_sheet4(elhander, inlist, year, month):
    sheet = elhander.activeWorksheet("表4广东省第三方监控机构登录使用情况")
    row_styles = elhander.copyRowStyle(sheet, 3)
    elhander.clearData(sheet, 3)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)


def writeByTemplate(s1_list, s1_se, s2_list, s2_se, s3_list, s3_se, s4_list, year, month):
    _, fn = get_filePath_and_fileName(template_file)
    fn = fn.format(year=year, month=month).strip('template_month_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, s1_list, s1_se, year, month)
    fill_sheet2(elhander, s2_list, s2_se, year, month)
    fill_sheet3(elhander, s3_list, s3_se, year, month)
    fill_sheet4(elhander, s4_list, year, month)
    elhander.save()


def main(start_day, end_day, days):
    s1_list, s1_se = s1.load_data(start_day, end_day, days)
    s2_list, s2_se = s2.load_data(start_day, end_day, days)
    s3_list, s3_se = s3.load_data(start_day, end_day, days)
    s4_list = s4.load_data(start_day, end_day, days)
    year = start_day.split('-')[0]
    month = start_day.split('-')[1]
    writeByTemplate(s1_list, s1_se, s2_list, s2_se, s3_list, s3_se, s4_list, year, month)


if __name__ == '__main__':
    start, end, days = TimeUtils.getLastMonthRange()
    main(start, end, days)
