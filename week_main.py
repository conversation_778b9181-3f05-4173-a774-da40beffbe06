from yyutils import *
from utils.config import bpath
import v_risk_week_report as s1
import login_week_report as s25
import productor_week_report as s3
import city_ok_week_report as s4

template_file = './templates/template_week_“两客一危一重”车辆智能监管数据周指标({start_day}-{end_day})【数据版】.xlsx'


def fill_sheet1(elhander, xjg_jg_list, se,start_day_format,end_day_format):
    sheet = elhander.activeWorksheet("1-各地市车辆智能监管数据")
    row_styles = elhander.copyRowStyle(sheet, 4)
    row_styles1 = elhander.copyRowStyle(sheet, 25)
    last_row = elhander.copyRow(sheet,26)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, xjg_jg_list, row_styles)
    elhander.fillData(sheet, [se], row_styles1)
    sheet.append(last_row)


def fill_sheet2(elhander, owner_user_login_list, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("2-广东省车辆智能监管系统企业用户登录情况")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, owner_user_login_list, row_styles)


def fill_sheet3(elhander, productor_list, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("3-厂家周报指标展示")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, productor_list, row_styles)


def fill_sheet4(elhander, city_ok_list, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("4-地市接入合格情况明细")
    row_styles = elhander.copyRowStyle(sheet, 3)
    elhander.clearData(sheet, 3)
    sheet[1][0].value = sheet[1][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, city_ok_list, row_styles)


def fill_sheet5(elhander, third_user_login_list, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("5-第三方监控机构登录情况")
    row_styles = elhander.copyRowStyle(sheet, 3)
    elhander.clearData(sheet, 3)
    sheet[1][0].value = sheet[1][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, third_user_login_list, row_styles)


def writeByTemplate(xjg_jg_list, se, owner_user_login_list,
                    productor_list, city_ok_list, third_user_login_list,
                    start_day, end_day):
    _, fn = get_filePath_and_fileName(template_file)
    start_day_format = start_day.replace('-', '.')
    end_day_format = end_day.replace('-', '.')[5:] if start_day[:4] == end_day[:4] else end_day.replace('-', '.')
    fn = fn.format(start_day=start_day_format, end_day=end_day_format).strip('template_week_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander,xjg_jg_list, se,start_day_format,end_day_format)
    fill_sheet2(elhander,owner_user_login_list,start_day_format,end_day_format)
    fill_sheet3(elhander,productor_list,start_day_format,end_day_format)
    fill_sheet4(elhander,city_ok_list,start_day_format,end_day_format)
    fill_sheet5(elhander,third_user_login_list,start_day_format,end_day_format)
    elhander.save()


def main(start_day, end_day):
    xjg_jg_list, se = s1.main(start_day, end_day)
    third_user_login_list,owner_user_login_list = s25.main(start_day, end_day)
    productor_list = s3.main(start_day, end_day)
    city_ok_list = s4.main(start_day, end_day)
    writeByTemplate(xjg_jg_list, se,owner_user_login_list,productor_list,city_ok_list,third_user_login_list, start_day, end_day)

if __name__ == '__main__':
    start, end = TimeUtils.getLastWeekRange()
    main(start, end)







