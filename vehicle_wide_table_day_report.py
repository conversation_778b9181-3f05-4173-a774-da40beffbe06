from yyutils import *
from datetime import datetime, timezone, timedelta

bpath = r'/data/cxh/wide_table/'
tz_utc_8 = timezone(timedelta(hours=8))


def downloadAll(otpath, report_day='', yy_report_day=''):
    db = DBUtil(DBType.CLICKHOUSE.value)
    wide_sql = f'''
        select * from
        gdispx_data.vehicle_wide_table vwt
        where inc_day = '{report_day.replace('-', '')[2:]}'
    '''

    vehicle_sql = f'''
        select
    	DISTINCT
    	vria.vehicle_id AS vehicle_id,
    	vria.vehicle_no AS vehicle_no,
    	vria.vehicle_color AS vehicle_color,
    	vria.vehicle_type AS vehicle_type,
    	vria.service_result AS service_result,
    	vria.access_time AS access_time,
    	vria.master_device_id AS master_device_id,
    	vria.master_sim_code AS master_sim_code,
    	vria.master_producter_id AS master_producter_id,
    	vria.master_producter_name AS master_producter_name,
    	vria.master_type_num AS master_type_num,
    	vria.master_device_brand AS master_device_brand,
    	vria.AI_BOX_device_id AS AI_BOX_device_id,
    	vria.AI_BOX_sim_code AS AI_BOX_sim_code,
    	vria.AI_BOX_producter_id AS AI_BOX_producter_id,
    	vria.AI_BOX_producter_name AS AI_BOX_producter_name,
    	vria.AI_BOX_type_num AS AI_BOX_type_num,
    	vria.AI_BOX_device_brand AS AI_BOX_device_brand,
    	ba.id AS area_id,
    	ba.name AS area_name,
    	ba.city_id AS city_id,
    	ba.city_short AS city_name
    from gdispx_data.vehicle_related_infos_all vria
    left join gdispx_data.basics_area ba
    on vria.area_id = ba.id
    '''
    track_sql = f'''
        SELECT 
            DISTINCT 
            imei AS sim_code,
            date AS report_date,
            total_point_count ,
            drift_point_count,
            continuous_mileage,
            total_mileage,
            error_point_count
        from gdispx_data.track_report tr 
        where tr.date = '{report_day.replace('-', '')[2:]}'
    '''
    last_gps_sql = f'''
        SELECT
            DISTINCT
            vehicle_id,
            vehicle_no ,
            vehicle_color,
            latitude as last_lat,
            longitude as last_lon,
            speed as last_speed,
            gps_time as last_gps_time
        from gdispx_data.business_gpsdatas_source bgs final
        where toDate(bgs.gps_time) = '{report_day}'
    '''
    original_alarm_sql = f'''
        SELECT
            vehicle_no,
            vehicle_color,
            '{report_day}' as report_date,
            sum(alarmNum) AS original_alarmNum
        FROM
            gdispx_data.statistics_original_alarm_day_all soada
        where
            alarm_time = '{report_day}'
        group by
            vehicle_no,
            vehicle_color
    '''
    risk_sql = f'''
        SELECT
            vehicle_no,
            vehicle_color,
            '{report_day}' as report_date,
            sum(alarmNum) AS riskNum,
            sum(disposeNum) AS risk_disposeNum,
            sum(oneLevel) AS risk_oneLevel,
            sum(twoLevel) AS risk_twoLevel,
            sum(threeLevel) AS risk_threeLevel,
            sum(monitorIntervene) AS alarm_monitorIntervene
        FROM
            gdispx_data.risk_statistics_day_all rsda
        where
            alarm_time = '{report_day}'
        group by
            vehicle_no,
            vehicle_color
    '''

    yy_accessories_sql = f'''
        SELECT 
        DISTINCT 
            alarm_time AS report_date,
            vehicle_no,
            vehicle_color,
            alarmNum AS accessoriesNum,
            onTimeNum AS accessoriesonTimeNum
        FROM gdispx_data.statistics_on_time_accessories_day sotad 
        where alarm_time = '{yy_report_day}';
    '''
    db.query_to_csv(wide_sql, join_path(otpath, f'{yy_report_day}_wide.csv'))
    db.query_to_csv(vehicle_sql, join_path(otpath, f'{report_day}_vehicle.csv'))
    db.query_to_csv(track_sql, join_path(otpath, f'{report_day}_track.csv'))
    db.query_to_csv(last_gps_sql, join_path(otpath, f'{report_day}_last_gps.csv'))
    db.query_to_csv(original_alarm_sql, join_path(otpath, f'{report_day}_alarm.csv'))
    db.query_to_csv(risk_sql, join_path(otpath, f'{report_day}_risk.csv'))
    db.query_to_csv(yy_accessories_sql, join_path(otpath, f'{yy_report_day}_accessories.csv'))
    sql1 = f'''
            SELECT * from gdispx_data.ETC_CKTS_NEW where toDate(g6) = '{report_day}';
        '''
    sql2 = f'''
            SELECT * from gdispx_data.ETC_MJTX_NEW where toDate(g8) = '{report_day}';
        '''
    sql3 = f'''
            SELECT * from gdispx_data.ETC_RKTX_NEW where toDate(g5) = '{report_day}';
        '''
    otfile1 = join_path(otpath, f'{report_day}_ETC_CKTS_NEW.csv')
    otfile2 = join_path(otpath, f'{report_day}_ETC_MJTX_NEW.csv')
    otfile3 = join_path(otpath, f'{report_day}_ETC_RKTX_NEW.csv')
    db.query_to_csv(sql1, otfile1)
    db.query_to_csv(sql2, otfile2)
    db.query_to_csv(sql3, otfile3)
    db.close()


def fill_default(ne):
    if 'accessoriesNum' not in ne:
        ne['accessoriesNum'] = 0
        ne['accessoriesonTimeNum'] = 0
    if 'total_point_count' not in ne:
        ne['total_point_count'] = 0
        ne['drift_point_count'] = 0
        ne['continuous_mileage'] = 0
        ne['total_mileage'] = 0
        ne['error_point_count'] = 0
        ne['is_online'] = 0
    else:
        ne['is_online'] = 1
    if 'original_alarmNum' not in ne:
        ne['original_alarmNum'] = 0
    if 'riskNum' not in ne:
        ne['riskNum'] = 0
        ne['risk_disposeNum'] = 0
        ne['risk_oneLevel'] = 0
        ne['risk_twoLevel'] = 0
        ne['risk_threeLevel'] = 0
        ne['alarm_monitorIntervene'] = 0


def export_cur_report(otpath, report_day):
    vehicle_file = join_path(otpath, f'{report_day}_vehicle.csv')
    vnc_add = {}
    sim_add = {}
    track_file = join_path(otpath, f'{report_day}_track.csv')
    for e in readCsv(track_file, print_count=100000):
        sim_code = e['sim_code']
        if not sim_code:
            continue
        e.pop('report_date')
        e.pop('sim_code')
        sim_add.setdefault(sim_code, e)
    lastGps_file = join_path(otpath, f'{report_day}_last_gps.csv')
    for e in readCsv(lastGps_file, print_count=100000):
        if not e['vehicle_no']:
            continue
        vnc = (e['vehicle_no'], e['vehicle_color'])
        e.pop('vehicle_id')
        e.pop('vehicle_no')
        e.pop('vehicle_color')
        vnc_add.setdefault(vnc, {}).update(e)
    alarm_file = join_path(otpath, f'{report_day}_alarm.csv')
    for e in readCsv(alarm_file, print_count=100000):
        if not e['vehicle_no']:
            continue
        vnc = (e['vehicle_no'], e['vehicle_color'])
        vnc_add.setdefault(vnc, {}).update({'original_alarmNum': e['original_alarmNum']})
    risk_file = join_path(otpath, f'{report_day}_risk.csv')
    for e in readCsv(risk_file, print_count=100000):
        if not e['vehicle_no']:
            continue
        vnc = (e['vehicle_no'], e['vehicle_color'])
        e.pop('vehicle_no')
        e.pop('vehicle_color')
        e.pop('report_date')
        vnc_add.setdefault(vnc, {}).update(e)
    middle_file = join_path(otpath,'2022-05-05_middle.csv')
    for e in readCsv(middle_file):
        vnc = (e['vehicle_no'], e['vehicle_color'])
        vnc_add.setdefault(vnc,{}).update({'exception_type':e['exception_type']})
    accessory_file = join_path(otpath,'2022-05-05_accessories.csv')
    for e in readCsv(accessory_file):
        vnc = (e['vehicle_no'], e['vehicle_color'])
        vnc_add.setdefault(vnc, {}).update({'accessoriesNum':e['accessoriesNum'],'accessoriesonTimeNum':e['accessoriesonTimeNum']})
    outlist = []
    for e in readCsv(vehicle_file, print_count=100000):
        ne = {}
        if not e['vehicle_no'] or not e['vehicle_id']:
            continue
        sim1 = e['master_sim_code']
        sim2 = e['AI_BOX_sim_code']
        if sim1 and sim_add.get(sim1):
            ne.update(sim_add.get(sim1))
        elif sim2 and sim_add.get(sim2):
            ne.update(sim_add.get(sim2))
        ne['inc_day'] = report_day.replace('-', '')[2:]
        ne.update(e)
        vnc = (e['vehicle_no'], e['vehicle_color'])
        if vnc_add.get(vnc):
            ne.update(vnc_add.get(vnc))
        fill_default(ne)
        outlist.append(ne)
    h = ['inc_day', 'vehicle_id', 'vehicle_no', 'vehicle_color', 'vehicle_type', 'service_result', 'access_time',
         'master_device_id', 'master_sim_code', 'master_producter_id', 'master_producter_name',
         'master_type_num', 'master_device_brand', 'AI_BOX_device_id', 'AI_BOX_sim_code',
         'AI_BOX_producter_id', 'AI_BOX_producter_name', 'AI_BOX_type_num', 'AI_BOX_device_brand',
         'area_id', 'area_name', 'city_id', 'city_name',
         'is_online','exception_type',
         'total_point_count', 'drift_point_count', 'continuous_mileage',
         'total_mileage', 'error_point_count', 'last_lat', 'last_lon', 'last_speed', 'last_gps_time',
         'original_alarmNum',
         'riskNum', 'risk_disposeNum', 'risk_oneLevel', 'risk_twoLevel', 'risk_threeLevel', 'alarm_monitorIntervene',
         'accessoriesNum', 'accessoriesonTimeNum']
    otfile = join_path(otpath, f'{report_day}_wide_to_insert.csv')
    write_csv(otfile, h, outlist)


def convert_g(g):
    for e in g:
        e['vehicle_id'] = int(e['vehicle_id'])
        e['vehicle_color'] = int(e['vehicle_color'])
        e['vehicle_type'] = int(e['vehicle_type'])
        e['master_producter_id'] = int(e['master_producter_id'])
        e['AI_BOX_producter_id'] = int(e['AI_BOX_producter_id'])
        e['area_id'] = int(e['area_id'])
        e['city_id'] = int(e['city_id'])
        e['total_point_count'] = int(e['total_point_count']) if e['total_point_count'] else 0
        e['drift_point_count'] = int(e['drift_point_count']) if e['drift_point_count'] else 0
        e['continuous_mileage'] = round(float(e['continuous_mileage']), 3) if e['continuous_mileage'] else 0.0
        e['total_mileage'] = round(float(e['total_mileage']), 3) if e['total_mileage'] else 0.0
        e['error_point_count'] = int(e['error_point_count']) if e['error_point_count'] else 0
        e['last_lat'] = round(float(e['last_lat']), 6) if e['last_lat'] else 0.0
        e['last_lon'] = round(float(e['last_lon']), 6) if e['last_lon'] else 0.0
        e['last_speed'] = round(float(e['last_speed']), 1) if e['last_speed'] else 0.0
        if e['access_time']:
            tm = TimeUtils.strptime(e['access_time'], TimeFormat.Y_m_d_H_M_S.value)
            e['access_time'] = datetime(tm.year, tm.month, tm.day, tm.hour, tzinfo=tz_utc_8)
        else:
            e['access_time'] = None
        if e['last_gps_time']:
            tm = TimeUtils.strptime(e['last_gps_time'], TimeFormat.Y_m_d_H_M_S.value)
            e['last_gps_time'] = datetime(tm.year, tm.month, tm.day, tm.hour, tzinfo=tz_utc_8)
        else:
            e['last_gps_time'] = None
        e['original_alarmNum'] = int(e['original_alarmNum']) if e['original_alarmNum'] else 0
        e['riskNum'] = int(e['riskNum']) if e['riskNum'] else 0
        e['risk_disposeNum'] = int(e['risk_disposeNum']) if e['risk_disposeNum'] else 0
        e['risk_oneLevel'] = int(e['risk_oneLevel']) if e['risk_oneLevel'] else 0
        e['risk_twoLevel'] = int(e['risk_twoLevel']) if e['risk_twoLevel'] else 0
        e['risk_threeLevel'] = int(e['risk_threeLevel']) if e['risk_threeLevel'] else 0
        e['alarm_monitorIntervene'] = int(e['alarm_monitorIntervene']) if e['alarm_monitorIntervene'] else 0
        e['accessoriesNum'] = int(e['accessoriesNum']) if e['accessoriesNum'] else 0
        e['accessoriesonTimeNum'] = int(e['accessoriesonTimeNum']) if e['accessoriesonTimeNum'] else 0


def batch_insert_report(otpath, report_day):
    infile = join_path(otpath, f'{report_day}_wide_to_insert.csv')
    db = DBUtil(DBType.CLICKHOUSE.value)
    for g in read_by_trunk(readCsv(infile), 10000):
        convert_g(g)
        insert_sql = f'''
            INSERT into gdispx_data.vehicle_wide_table 
            (`inc_day`,`vehicle_id`,`vehicle_no`,`vehicle_color`,`vehicle_type`,`service_result`,
            `access_time`,`master_device_id`,`master_sim_code`,`master_producter_id`,
            `master_producter_name`,`master_type_num`,`master_device_brand`,
            `AI_BOX_device_id`,`AI_BOX_sim_code`,`AI_BOX_producter_id`,
            `AI_BOX_producter_name`,`AI_BOX_type_num`,`AI_BOX_device_brand`,
            `area_id`,`area_name`,`city_id`,`city_name`,`total_point_count`,`drift_point_count`,
            `continuous_mileage`,`total_mileage`,`error_point_count`,`last_lat`,`last_lon`,
            `last_speed`,`last_gps_time`,`original_alarmNum`,`riskNum`,`risk_disposeNum`,
            `risk_oneLevel`,`risk_twoLevel`,`risk_threeLevel`,`alarm_monitorIntervene`,
            `accessoriesNum`,`accessoriesonTimeNum`) values 
        '''
        db.excute(insert_sql, g)
    db.close()


def main(report_day, yy_report_day):
    otpath = createOutpath(bpath, report_day)
    downloadAll(otpath, report_day, yy_report_day)
    export_cur_report(otpath, report_day)
    batch_insert_report(otpath, report_day)


if __name__ == '__main__':
    report_day = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    yy_report_day = TimeUtils.DeltaDay(-2, DateFormat.Y_m_d.value)
    # main(report_day, yy_report_day)
    # main('2022-05-05', '2022-05-04')
    # otpath = createOutpath(bpath, '2022-05-05')
    otpath = r'C:\工作目录\粤运\doc\工单处理\数据宽表\2022-05-05'
    # downloadAll(otpath, '2022-05-05', '2022-05-05')
    # export_cur_report(otpath, '2022-05-05')
    infile= join_path(otpath,'2022-05-05_wide_to_insert.csv')
    otfile = join_path(otpath,'gps异常.csv')
    outlist = []
    for e in readCsv(infile):
        if e['is_online'] == '1' and e['exception_type']:
            outlist.append(e)
    write_csv(otfile,getHeader(infile),outlist)