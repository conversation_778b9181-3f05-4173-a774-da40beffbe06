from yyutils import *
from utils.YYSQLTemplate import YYSQLTemplate
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    v_jg_sql = YYSQLTemplate.wide_stats(
        [],
        "city_id,vehicle_no,vehicle_color,vehicle_type",
        '''
        city_id,
        any(city_short_name) city_name,
        vehicle_no,
        vehicle_color,
        vehicle_type,
        sum(risk_oneLevel) oneLevelNum,
        sum(risk_twoLevel) twoLevelNum,
        sum(risk_threeLevel) threeLevelNum,
        oneLevelNum+twoLevelNum+threeLevelNum riskNum,
        sum(if(exception_type=2,1,0)) exNum,
        max(online) online
        ''',
        start_day=start_day, end_day=end_day)
    jg_sql = SQLHelper().table(v_jg_sql) \
        .groupBy('city_id,vehicle_type') \
        .select(f'''
                    city_id,
                    vehicle_type,
                    any(city_name) city_name,
                    sum(oneLevelNum) oneLevelNum,
                    sum(twoLevelNum) twoLevelNum,
                    sum(threeLevelNum) threeLevelNum,
                    sum(riskNum) riskNum,
                    sum(exNum) exNum,
                    sum(online) jg_count,
                    FLOOR(riskNum/{days}/jg_count,4) avg_riskNum
            ''')
    final_sql = SQLHelper().table(jg_sql).groupBy('city_id') \
        .select(
        f'''
        city_id,
        any(city_name) city_name,
        maxIf(riskNum,vehicle_type =1) ke_riskNum, 
        maxIf(jg_count,vehicle_type =1) ke_jg_count, 
        maxIf(avg_riskNum,vehicle_type =1) ke_avg_riskNum, 
        maxIf(riskNum,vehicle_type =3) w_riskNum, 
        maxIf(jg_count,vehicle_type =3) w_jg_count, 
        maxIf(avg_riskNum,vehicle_type =3) w_avg_riskNum, 
        maxIf(riskNum,vehicle_type =4) z_riskNum, 
        maxIf(jg_count,vehicle_type =4) z_jg_count, 
        maxIf(avg_riskNum,vehicle_type =4) z_avg_riskNum, 
        maxIf(jg_count,vehicle_type =1) + maxIf(jg_count,vehicle_type =3)
            + maxIf(jg_count,vehicle_type =4) t_jg_count,
        maxIf(riskNum,vehicle_type =1) + maxIf(riskNum,vehicle_type =3)
            + maxIf(riskNum,vehicle_type =4) t_riskNum,
        maxIf(oneLevelNum,vehicle_type =1) + maxIf(oneLevelNum,vehicle_type =3)
            + maxIf(oneLevelNum,vehicle_type =4) t_oneLevelNum,
        maxIf(twoLevelNum,vehicle_type =1) + maxIf(twoLevelNum,vehicle_type =3)
            + maxIf(twoLevelNum,vehicle_type =4) t_twoLevelNum,
        maxIf(threeLevelNum,vehicle_type =1) + maxIf(threeLevelNum,vehicle_type =3)
            + maxIf(threeLevelNum,vehicle_type =4) t_threeLevelNum,
        maxIf(exNum,vehicle_type =1) + maxIf(exNum,vehicle_type =3)
            + maxIf(exNum,vehicle_type =4) t_exNum,
        FLOOR(t_riskNum/{days}/t_jg_count,4) t_avg_riskNum,
        FLOOR(t_oneLevelNum/{days}/t_jg_count,4) t_avg_l1riskNum,
        FLOOR(t_twoLevelNum/{days}/t_jg_count,4) t_avg_l2riskNum,
        FLOOR(t_threeLevelNum/{days}/t_jg_count,4) t_avg_l3riskNum,
        FLOOR(t_exNum/{days}/t_jg_count,4) t_avg_exNum
        '''
    )
    return final_sql


def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        ne = OrderedDict(zip(
            ('pid', 'city_name',
             't_avg_riskNum', 't_avg_riskNum_rank',
             't_avg_l1riskNum', 't_avg_l1riskNum_rank',
             't_avg_l2riskNum', 't_avg_l2riskNum_rank',
             't_avg_l3riskNum', 't_avg_l3riskNum_rank',
             't_avg_exNum', 't_avg_exNum_rank',
             'ke_avg_riskNum', 'ke_avg_riskNum_rank',
             'w_avg_riskNum', 'w_avg_riskNum_rank',
             'z_avg_riskNum', 'z_avg_riskNum_rank'
             ),
            (e['pid'], e['city_name'],
             e['t_avg_riskNum'], e['t_avg_riskNum_rank'],
             e['t_avg_l1riskNum'], e['t_avg_l1riskNum_rank'],
             e['t_avg_l2riskNum'], e['t_avg_l2riskNum_rank'],
             e['t_avg_l3riskNum'], e['t_avg_l3riskNum_rank'],
             e['t_avg_exNum'], e['t_avg_exNum_rank'],
             e['ke_avg_riskNum'], e['ke_avg_riskNum_rank'],
             e['w_avg_riskNum'], e['w_avg_riskNum_rank'],
             e['z_avg_riskNum'], e['z_avg_riskNum_rank'],
             )
        ))
        outlist.append(ne)
    return outlist


def do_rank(inlist):
    do_rank_by_col(inlist, 't_avg_riskNum')
    do_rank_by_col(inlist, 't_avg_l1riskNum')
    do_rank_by_col(inlist, 't_avg_l2riskNum')
    do_rank_by_col(inlist, 't_avg_l3riskNum')
    do_rank_by_col(inlist, 't_avg_exNum')
    do_rank_by_col(inlist, 'ke_avg_riskNum')
    do_rank_by_col(inlist, 'w_avg_riskNum')
    do_rank_by_col(inlist, 'z_avg_riskNum')
    inlist.sort(key=lambda o: o['pid'])


def doSum(inlist,days):
    t_jg_count = sum([e['t_jg_count'] for e in inlist])
    ke_jg_count = sum([e['ke_jg_count'] for e in inlist])
    w_jg_count = sum([e['w_jg_count'] for e in inlist])
    z_jg_count = sum([e['z_jg_count'] for e in inlist])
    t_riskNum = sum([e['t_riskNum'] for e in inlist])
    t_oneLevelNum = sum([e['t_oneLevelNum'] for e in inlist])
    t_twoLevelNum = sum([e['t_twoLevelNum'] for e in inlist])
    t_threeLevelNum = sum([e['t_threeLevelNum'] for e in inlist])
    t_exNum = sum([e['t_exNum'] for e in inlist])
    ke_riskNum = sum([e['ke_riskNum'] for e in inlist])
    w_riskNum = sum([e['w_riskNum'] for e in inlist])
    z_riskNum = sum([e['z_riskNum'] for e in inlist])
    avg_t_riskNum = FLOORCUT(t_riskNum / t_jg_count/days)
    avg_t_oneLevelNum = FLOORCUT(t_oneLevelNum / t_jg_count/days)
    avg_t_twoLevelNum = FLOORCUT(t_twoLevelNum / t_jg_count/days)
    avg_t_threeLevelNum = FLOORCUT(t_threeLevelNum / t_jg_count/days)
    avg_t_exNum = FLOORCUT(t_exNum / t_jg_count/days)
    avg_ke_riskNum = FLOORCUT(ke_riskNum / ke_jg_count/days)
    avg_w_riskNum = FLOORCUT(w_riskNum / w_jg_count/days)
    avg_z_riskNum = FLOORCUT(z_riskNum / z_jg_count/days)
    ne = OrderedDict(zip(
        ('pid', 'city_name',
         't_avg_riskNum', 't_avg_riskNum_rank',
         't_avg_l1riskNum', 't_avg_l1riskNum_rank',
         't_avg_l2riskNum', 't_avg_l2riskNum_rank',
         't_avg_l3riskNum', 't_avg_l3riskNum_rank',
         't_avg_exNum', 't_avg_exNum_rank',
         'ke_avg_riskNum', 'ke_avg_riskNum_rank',
         'w_avg_riskNum', 'w_avg_riskNum_rank',
         'z_avg_riskNum', 'z_avg_riskNum_rank'
         ),
        ('全省平均值', '-',
         avg_t_riskNum, '',
         avg_t_oneLevelNum, '',
         avg_t_twoLevelNum, '',
         avg_t_threeLevelNum, '',
         avg_t_exNum, '',
         avg_ke_riskNum, '',
         avg_w_riskNum, '',
         avg_z_riskNum, '',
         )
    ))
    return ne


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    outlist = orderByCity(datalist)
    do_rank(outlist)
    outlist = export_outlist_orderDict(outlist)
    se = doSum(datalist,days)
    db.close()
    # h = list(outlist[0].keys())
    # otfile = join_path(bpath, 'test.csv')
    # outlist.append(se)
    # write_csv(otfile, h, outlist)
    return outlist,se

if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
