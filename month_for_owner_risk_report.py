from yyutils import *
from utils.config import bpath
import month_for_owner_risk_report_sheet1 as s1
import month_for_owner_risk_report_sheet2 as s2
import month_for_owner_risk_report_sheet3 as s3
import month_for_owner_risk_report_sheet4 as s4



template_file = 'templates/template_month_{year}年{month}月份各地市风险排名前10运输企业风险明细.xlsx'


def fill_sheet1(elhander, inlist, year, month):
    sheet = elhander.activeWorksheet("重货(10台以上)")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)


def fill_sheet2(elhander, inlist, year, month):
    sheet = elhander.activeWorksheet("客运")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)


def fill_sheet3(elhander, inlist, year, month):
    sheet = elhander.activeWorksheet("危运")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)


def fill_sheet4(elhander, inlist, year, month):
    sheet = elhander.activeWorksheet("重货")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)


def writeByTemplate(s1_list, s2_list, s3_list, s4_list, year, month):
    _, fn = get_filePath_and_fileName(template_file)
    fn = fn.format(year=year, month=month).strip('template_month_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, s1_list,  year, month)
    fill_sheet2(elhander, s2_list,  year, month)
    fill_sheet3(elhander, s3_list,  year, month)
    fill_sheet4(elhander, s4_list, year, month)
    elhander.save()


def main(start_day, end_day, days):
    s1_list = s1.load_data(start_day, end_day, days)
    s2_list = s2.load_data(start_day, end_day, days)
    s3_list = s3.load_data(start_day, end_day, days)
    s4_list = s4.load_data(start_day, end_day, days)
    year = start_day.split('-')[0]
    month = start_day.split('-')[1]
    writeByTemplate(s1_list,  s2_list, s3_list,  s4_list, year, month)


if __name__ == '__main__':
    start, end, days = TimeUtils.getLastMonthRange()
    main(start, end, days)


