from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from collections import OrderedDict


def loadSQL(start_day, end_day):
    avg_drift_sql = YYSQLTemplate.calTrackAvgDriftPoints(start_day, end_day)
    v_jg_select_columns = '''
                    vehicle_no ,
                    vehicle_color ,
                    any(master_producter_id) master_producter_id0,
                    any(master_producter_name) master_producter_name0,
                    max(online) online,
                    if( sum(drift_point_count) > avg_drift_point_count,1,0  ) is_drift,
                    sum(total_mileage) total_mileage,
                    sum(continuous_mileage) continuous_mileage,
                    if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, 0,1  ) is_ok,
                    min(never_gps) is_never_gps,
                    min(never_alarm) is_never_alarm,
                    min(never_acc) is_never_acc,
                    sum(accessoriesNum) accessoriesNum,
                    sum(accessoriesonTimeNum) accessoriesonTimeNum
                '''
    v_jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([], 'vehicle_no,vehicle_color',
                                                                    v_jg_select_columns,
                                                                    start_day=start_day,
                                                                    end_day=end_day
                                                                    )
    v_jg_sql = SQLHelper().table(v_jg_sql).select('''
                    vehicle_no ,
                    vehicle_color ,
                    if(master_producter_id0 is null,18,master_producter_id0) master_producter_id,
                    if(master_producter_id0 is null,'非粤标设备（1月18日前安装）',master_producter_name0) master_producter_name,
                    online,
                    is_drift,
                    total_mileage,
                    continuous_mileage,
                    is_ok,
                    is_never_gps,
                    is_never_alarm,
                    is_never_acc,
                    accessoriesNum,
                    accessoriesonTimeNum        
    ''')
    productor_sql = SQLHelper().table(v_jg_sql).groupBy('master_producter_id') \
        .select('''
            master_producter_id,
            any(master_producter_name) master_producter_name,
            count(1) jg_count,
            sum(online) online_count,
            FLOOR(sum(online) / count(1),4 ) online_rate,
            sum(is_drift) high_drift_count,
            FLOOR(if(online_count != 0,high_drift_count / online_count,0),4 ) drift_rate,
            FLOOR(sum(total_mileage),2) totalMileage,
            FLOOR(sum(continuous_mileage),2) continuousMileage,
            FLOOR(if(totalMileage != 0,continuousMileage / totalMileage,0),4 ) track_rate,
            sum(is_ok) ok_count,
            sum(is_never_gps) never_gps_count,
            sum(is_never_alarm) never_alarm_count,
            sum(is_never_acc) never_acc_count,
            FLOOR(ok_count / jg_count,4 ) ok_rate,
            sum(accessoriesNum) accessoriesNum_count,
            accessoriesNum_count - sum(accessoriesonTimeNum) accessories_miss_count,
            FLOOR(if(accessoriesNum_count!=0,accessories_miss_count / accessoriesNum_count,0),4 ) accessories_miss_rate
            ''')
    return avg_drift_sql, productor_sql


def do_rank(productor_list):
    productor_list.sort(key=lambda o: -o['online_rate'])
    for i in range(len(productor_list)):
        productor_list[i]['online_rank'] = i + 1
    productor_list.sort(key=lambda o: o['drift_rate'])
    for i in range(len(productor_list)):
        productor_list[i]['drift_rank'] = i + 1
    productor_list.sort(key=lambda o: -o['track_rate'])
    for i in range(len(productor_list)):
        productor_list[i]['track_rank'] = i + 1
    productor_list.sort(key=lambda o: -o['ok_rate'])
    for i in range(len(productor_list)):
        productor_list[i]['ok_rank'] = i + 1
    productor_list.sort(key=lambda o: o['accessories_miss_rate'])
    for i in range(len(productor_list)):
        productor_list[i]['accessories_miss_rank'] = i + 1
    productor_list.sort(key=lambda o: -o['jg_count'])


def do_productor_stats(start_day, end_day):
    avg_drift_sql, productor_sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    avg_drift_list = db.query_to_dictList(avg_drift_sql)
    avg_drift_point_count = avg_drift_list[0]['average_drift_num']
    productor_sql = productor_sql.replace('avg_drift_point_count',str(avg_drift_point_count))
    productor_list = db.query_to_dictList(productor_sql)
    db.close()
    do_rank(productor_list)
    return productor_list


def fill_last_week_data(cur_productor_list, last_productor_list):
    id_oe = list_2_dict(last_productor_list, 'master_producter_id')
    for e in cur_productor_list:
        oe = id_oe.get(e['master_producter_id'])
        if not oe:
            e['last_online_rate'] = '-'
            e['last_online_rate_dif'] = '-'
            e['last_drift_rate'] = '-'
            e['last_drift_rate_dif'] = '-'
            e['last_track_rate'] = '-'
            e['last_track_rate_dif'] = '-'
            e['last_ok_rate'] = '-'
            e['last_ok_rate_dif'] = '-'
            e['last_accessories_miss_rate'] = '-'
            e['last_accessories_miss_rate_dif'] = '-'
        else:
            e['last_online_rate'] = f"{round(oe['online_rate'] * 100,2)}%"
            e['last_online_rate_dif'] = f"{round((e['online_rate'] - oe['online_rate']) * 100,2)}%"
            e['last_drift_rate'] = f"{round(oe['drift_rate'] * 100,2)}%"
            e['last_drift_rate_dif'] = f"{round((e['drift_rate'] - oe['drift_rate']) * 100,2)}%"
            e['last_track_rate'] = f"{round(oe['track_rate'] * 100,2)}%"
            e['last_track_rate_dif'] = f"{round((e['track_rate'] - oe['track_rate']) * 100,2)}%"
            e['last_ok_rate'] = f"{round(oe['ok_rate'] * 100,2)}%"
            e['last_ok_rate_dif'] = f"{round((e['ok_rate'] - oe['ok_rate']) * 100,2)}%"
            e['last_accessories_miss_rate'] = f"{round((oe['accessories_miss_rate']) * 100,2)}%"
            e['last_accessories_miss_rate_dif'] = f"{round((e['accessories_miss_rate'] - oe['accessories_miss_rate']) * 100,2)}%"
        e['online_rate'] = f"{round(e['online_rate'] * 100,2)}%"
        e['drift_rate'] = f"{round(e['drift_rate'] * 100,2)}%"
        e['track_rate'] = f"{round(e['track_rate'] * 100,2)}%"
        e['ok_rate'] = f"{round(e['ok_rate'] * 100,2)}%"
        e['accessories_miss_rate'] = f"{round(e['accessories_miss_rate'] * 100,2)}%"
    outlist = []
    for e in cur_productor_list:
        ne = OrderedDict()
        ne["master_producter_name"] = e["master_producter_name"]
        ne["jg_count"] = e["jg_count"]
        ne["online_count"] = e["online_count"]
        ne["online_rate"] = e["online_rate"]
        ne["last_online_rate"] = e["last_online_rate"]
        ne["last_online_rate_dif"] = e["last_online_rate_dif"]
        ne["online_rank"] = e["online_rank"]
        ne["high_drift_count"] = e["high_drift_count"]
        ne["drift_rate"] = e["drift_rate"]
        ne["last_drift_rate"] = e["last_drift_rate"]
        ne["last_drift_rate_dif"] = e["last_drift_rate_dif"]
        ne["drift_rank"] = e["drift_rank"]
        ne["totalMileage"] = e["totalMileage"]
        ne["continuousMileage"] = e["continuousMileage"]
        ne["track_rate"] = e["track_rate"]
        ne["last_track_rate"] = e["last_track_rate"]
        ne["last_track_rate_dif"] = e["last_track_rate_dif"]
        ne["track_rank"] = e["track_rank"]
        ne["ok_count"] = e["ok_count"]
        ne["never_gps_count"] = e["never_gps_count"]
        ne["never_alarm_count"] = e["never_alarm_count"]
        ne["never_acc_count"] = e["never_acc_count"]
        ne["ok_rate"] = e["ok_rate"]
        ne["last_ok_rate"] = e["last_ok_rate"]
        ne["last_ok_rate_dif"] = e["last_ok_rate_dif"]
        ne["ok_rank"] = e["ok_rank"]
        ne["accessoriesNum_count"] = e["accessoriesNum_count"]
        ne["accessories_miss_count"] = e["accessories_miss_count"]
        ne["accessories_miss_rate"] = e["accessories_miss_rate"]
        ne["last_accessories_miss_rate"] = e["last_accessories_miss_rate"]
        ne["last_accessories_miss_rate_dif"] = e["last_accessories_miss_rate_dif"]
        ne["accessories_miss_rank"] = e["accessories_miss_rank"]
        outlist.append(ne)
    return outlist


def main(start_day, end_day):
    cur_productor_list = do_productor_stats(start_day, end_day)
    start_day_1 = TimeUtils.DeltaDay(-7, start_day)
    end_day_1 = TimeUtils.DeltaDay(-1, start_day)
    last_productor_list = do_productor_stats(start_day_1, end_day_1)
    productor_list = fill_last_week_data(cur_productor_list, last_productor_list)
    return productor_list


if __name__ == '__main__':
    start, end = TimeUtils.getLastWeekRange()
    main(start, end)
