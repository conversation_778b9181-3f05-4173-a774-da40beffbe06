#!/usr/bin/env python
"""
<AUTHOR> hemeng(01381307)
@file   : Logger.py
@time   : 2020/1/7 10:30
@desc   :
"""
import sys, os
import logging
import threading
from time import time, sleep


def format_str(*args):
    s = ' '.join([str(a) for a in args])
    return s


def countTime(*kwds):
    message = format_str(*kwds)

    def bar(f):
        def new_f(*args, **kwargs):
            logger = Logger.instance()
            logger.info('----------- begin', message, 'begin -----------')
            # logger.info('参数如下:')
            # for a in args:
            #     logger.info(a)
            for k, v in kwargs.items():
                logger.info(k, '=', v)
            s = time()
            ret = f(*args, **kwargs)
            logger.info('----------- end', message, 'end -----------')
            logger.info('执行函数', f.__name__, '花费了', round(time() - s, 3), '秒')
            return ret

        return new_f

    return bar


class Logger:
    LOG_PATH = os.path.join(os.path.dirname(os.path.split(os.path.realpath(__file__))[0]), 'logs')
    LOG_FILE = os.path.join(LOG_PATH, 'out.log')
    LOG_FORMAT = "%(asctime)s-%(name)s-%(levelname)s-%(message)s"
    DATE_FORMAT = "%Y/%m/%d %H:%M:%S"

    _instance_lock = threading.Lock()

    def __init__(self, loggername='', log_file=None):
        """ 不要直接调用构造函数创建对象 """
        self.logger = logging.getLogger(loggername)
        self.logger.setLevel(logging.DEBUG)

        formatter = logging.Formatter(Logger.LOG_FORMAT, datefmt=Logger.DATE_FORMAT)

        # FileHandler
        if not log_file:
            if not os.path.exists(Logger.LOG_PATH):
                os.makedirs(Logger.LOG_PATH)
            log_file = Logger.LOG_FILE
        fh = logging.FileHandler(log_file, encoding='utf-8')
        fh.setLevel(logging.INFO)
        fh.setFormatter(formatter)
        self.logger.addHandler(fh)

        # StreamHandler, use stdout
        ch = logging.StreamHandler(sys.stderr)
        ch.setLevel(logging.INFO)
        ch.setFormatter(formatter)
        self.logger.addHandler(ch)

    @staticmethod
    def instance(loggername='', log_file=None):
        if not hasattr(Logger, "_instance"):
            with Logger._instance_lock:
                if not hasattr(Logger, "_instance"):
                    Logger._instance = Logger(loggername, log_file)
        return Logger._instance

    def debug(self, *args):
        self.logger.debug(format_str(*args))

    def info(self, *args):
        self.logger.info(format_str(*args))

    def warning(self, *args):
        self.logger.warning(format_str(*args))

    def error(self, *args):
        self.logger.error(format_str(*args))

    def fatal(self, *args):
        self.logger.fatal(format_str(*args))

    def print_exc(self, *args):
        self.logger.error(format_str(*args), exc_info=True)


if __name__ == '__main__':
    '''
    日志文件默认创建于项目根目录的logs目录下面,out.log文件。
    创建日志工具应带上日志名，方便追溯，一般在入口函数所在的脚本初始化日志工具.
    
    '''

    obj = Logger.instance("融合测试")


    def task(arg):
        sleep(2)
        o = Logger.instance()
        o.info(arg, "id:", threading.current_thread().ident, id(o))


    @countTime('进行注解方法测试')
    def test():
        tasks = []
        for i in range(10):
            t = threading.Thread(target=task, args=[i, ])
            tasks.append(t)
        for t in tasks:
            t.setDaemon(True)
            t.start()
        for t in tasks:
            t.join()


    test()
    obj.error("id:", threading.current_thread().ident, id(obj))
