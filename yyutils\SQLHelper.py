class SQLHelper:
    def __init__(self):
        self.sql = ''

    def table(self, table, alias=None, **kwargs):
        if not alias:
            alias = ''
        tmp = ' '
        if 'select' not in table.lower():
            tmp += f' {table} {alias} \n'
        else:
            tmp += f' ({table}) {alias} \n'
        if kwargs:
            tmp = tmp.format(**kwargs)
        self.sql += tmp
        return self

    def leftJoin(self):
        self.sql += ' left join \n'
        return self

    def rightJoin(self):
        self.sql += ' right join \n'
        return self

    def innerJoin(self):
        self.sql += ' , \n'
        return self

    def on(self, conditions, **kwargs):
        if not conditions:
            return self
        tmp = ' on'
        if isinstance(conditions, str):
            tmp += f' {conditions} \n'
        else:
            i = 0
            for condition in conditions:
                i += 1
                if i == 1:
                    tmp += f' {condition} \n'
                else:
                    tmp += f' and {condition} \n'
        if kwargs:
            tmp = tmp.format(**kwargs)
        self.sql += tmp
        return self

    def where(self, conditions=None, **kwargs):
        if not conditions:
            return self
        tmp = ' where'
        if isinstance(conditions, str):
            tmp += f' {conditions} \n'
        else:
            i = 0
            for condition in conditions:
                i += 1
                if i == 1:
                    tmp += f' {condition} \n'
                else:
                    tmp += f' and {condition} \n'
        if kwargs:
            tmp = tmp.format(**kwargs)
        self.sql += tmp
        return self

    def groupBy(self, keys=None):
        if not keys:
            return self
        if isinstance(keys, str):
            self.sql += f' group by {keys} \n'
        else:
            self.sql += f' group by {",".join(keys)} \n'
        return self

    def having(self, conditions: list = None, **kwargs):
        if not conditions:
            return self
        tmp = ' having'
        i = 0
        for condition in conditions:
            i += 1
            if i == 1:
                tmp += f' {condition} \n'
            else:
                tmp += f' and {condition} \n'
        if kwargs:
            tmp = tmp.format(**kwargs)
        self.sql += tmp
        return self

    def select(self, columns=None, **kwargs):
        if not columns:
            return self
        tmp = 'select \n'
        if isinstance(columns, str):
            tmp += columns
        else:
            for column in columns:
                if isinstance(column, tuple):
                    tmp += f'{column[0]} {column[1]} ,'
                else:
                    tmp += f'{column} ,'
        tmp = tmp.strip(',')
        if kwargs:
            tmp = tmp.format(**kwargs)
        self.sql = f'{tmp} \n from {self.sql}'
        return self.sql

    def ckDelete(self):
        self.sql = f'alter table {self.sql}'
        idx = self.sql.index('where')
        self.sql = self.sql[:idx] + ' delete '+self.sql[idx:]
        return self.sql


if __name__ == '__main__':
    jg_sql = (SQLHelper().table('gdispx_data.v_vehicle_wide_day_all')
                         .where(["inc_day BETWEEN '{start_day}'  and '{end_day}'"],
                                start_day='2022-05-01', end_day='2022-05-31')
                         .groupBy('city_id, vehicle_no, vehicle_color, vehicle_type')
                         .select('''
                            city_id, 
                            vehicle_no, 
                            vehicle_color, 
                            vehicle_type, 
                            max(online) online
                         ''')
                         )
    city_group_sql = (
        SQLHelper().table(jg_sql)
            .groupBy(['city_id', 'vehicle_type'])
            .select(['city_id,vehicle_type,count(1) jg_count,sum(online) online_count'])
    )

    yz_city_group_sql = (
        SQLHelper().table('gdispx_data.vehicle_history_yunzheng')
            .where("create_date BETWEEN '{start_day}'  and '{end_day}'",
                   start_day='2022-05-01', end_day='2022-05-31')
            .groupBy(['city_id,vehicle_type '])
            .select(f'''
            city_id,
            count(DISTINCT vehicle_no) xjg_count,
            vehicle_type
    ''')
    )
    xjg_jg_sql = (
        SQLHelper().table(city_group_sql, 'B')
            .leftJoin()
            .table(yz_city_group_sql, 'C')
            .on(['B.city_id = toInt32(C.city_id)', 'B.vehicle_type = toUInt8(C.vehicle_type)'])
            .select(f'''
        B.city_id city_id,
        B.vehicle_type vehicle_type,
        jg_count,
        online_count,
        if(xjg_count<jg_count, jg_count,xjg_count) xjg_count
    ''')
    )
    print(xjg_jg_sql)
