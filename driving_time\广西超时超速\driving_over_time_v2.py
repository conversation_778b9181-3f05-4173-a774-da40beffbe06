import datetime
import pandas as pd

files = ['X:\\track_report\\driving_time\\广西超时超速\\桂K66936车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KC4469车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KH3499车辆20250101-0624.csv']

for file in files:
    df = pd.read_csv(file,engine='pyarrow')
    df['gps_time'] = pd.to_datetime(df['gps_time'])
    df = df.sort_values(by='gps_time')
    total_driving_time = 0
    total_stop_time = 0
    driver = {}
    startDrivingTime = None
    stopDrivingTime = None
    prev_speed = None

    for index, row in df.iterrows():
        current_speed = row['speed']
        current_time = row['gps_time']
        current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S.000")
        driverId = row['vehicle_no']

        if prev_speed is not None:
            # 计算时间间隔
            time_diff = current_time - prev_time
            time_diff_seconds = time_diff.total_seconds()

            # 根据新的逻辑判断累计驾驶时间还是休息时间
            if prev_speed > 0 and current_speed > 0:
                # 前后轨迹速度都大于0，累计驾驶时间
                if startDrivingTime is None:
                    startDrivingTime = prev_time_str
                stopDrivingTime = current_time_str

                if time_diff > datetime.timedelta(hours=4):
                    total_stop_time += time_diff_seconds
                else:
                    total_driving_time += time_diff_seconds

            elif prev_speed == 0 and current_speed == 0:
                # 前后都为0，累计休息时间
                total_stop_time += time_diff_seconds

            elif prev_speed == 0 and current_speed > 0:
                # 从0变为非0，累计休息时间
                total_stop_time += time_diff_seconds

            else:
                # 其余情况（从非0变为0），按原逻辑处理
                if prev_speed > 0:
                    # 之前在行驶，现在停止，累计驾驶时间
                    if startDrivingTime is None:
                        startDrivingTime = prev_time_str
                    stopDrivingTime = prev_time_str

                    if time_diff > datetime.timedelta(hours=4):
                        total_stop_time += time_diff_seconds
                    else:
                        total_driving_time += time_diff_seconds
                else:
                    # 停车时间
                    total_stop_time += time_diff_seconds

            # 检查是否需要重置计时器
            if total_stop_time > 1200:  # 20分钟
                if total_driving_time > 4*60*60:  # 4小时
                    print(f"{driverId} 驾驶超时 |开始时间 | {startDrivingTime} | 结束时间 | {stopDrivingTime} | 持续时长 | {(total_driving_time) / 3600:.2f}小时")

                # 重置计时器
                total_driving_time = 0
                total_stop_time = 0
                startDrivingTime = None
                stopDrivingTime = None

        # 更新前一个记录
        prev_speed = current_speed
        prev_time = current_time
        prev_time_str = current_time_str
            
