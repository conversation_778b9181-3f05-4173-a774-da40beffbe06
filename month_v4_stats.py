from yyutils import *
from utils.config import orderByCity,bpath
from utils.YYSQLTemplate import YYS<PERSON>Template
from collections import OrderedDict

template_file = 'templates/template_month_{year}年{month}月份广东省各地市重型货车风险情况统计（保险）【数据版】.xlsx'

def loadSQL(start_day, end_day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=end_day)
    jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats(
        ["vehicle_type = 4"],
        "vehicle_no,vehicle_color",
        '''
         vehicle_no,vehicle_color,
         any(area_manger_short_name) area_manger_short_name,
         any(owner_id) owner_id, any(owner_name) owner_name,
         any(third_party_id) third_party_id, any(third_party_name) third_party_name,
         sum(if(exception_type=2,1,0)) ex_days,
         if(max(online) =1,1,0 ) is_online,
         if(min(never_gps) +  min(never_alarm) + min(never_acc) > 0 , 0 , 1) is_ok,
         sum(run_time) run_time
        ''',
        start_day=start_day, end_day=end_day)
    vin_sql = YYSQLTemplate.vehicleRelatedInfosWide()
    risk_sql = f'''
        SELECT 
        vehicle_no,
        vehicle_color,
        sum(l1call) l1_call,
        sum(l1play) l1_play,
        sum(l1smoking) l1_smoking,
        sum(l1overspeed) l1_overspeed,
        sum(l1overtime) l1_overtime,
        sum(l1night_25) l1_night_25,
        sum(l2call) l2_call,
        sum(l2play) l2_play,
        sum(l2smoking) l2_smoking,
        sum(l2overspeed) l2_overspeed,
        sum(l2overtime) l2_overtime,
        sum(l2night_25) l2_night_25,
        sum(l3call) l3_call,
        sum(l3play) l3_play,
        sum(l3smoking) l3_smoking,
        sum(l3overspeed) l3_overspeed,
        sum(l3overtime) l3_overtime,
        sum(l3night_25) l3_night_25,
        l1_call + l1_play+l1_smoking+l1_overspeed+l1_overtime+l1_night_25 l1_riskNum,
        l2_call + l2_play+l2_smoking+l2_overspeed+l2_overtime+l2_night_25 l2_riskNum,
        l3_call + l3_play+l3_smoking+l3_overspeed+l3_overtime+l3_night_25 l3_riskNum,
        l1_riskNum+l2_riskNum+l3_riskNum riskNum
        from 
        (SELECT 
        vehicle_no,
        vehicle_color,
        if(alarm_level=1 and alarm_code=10401,1,0) l1call,
        if(alarm_level=1 and alarm_code= 10413,1,0) l1play,
        if(alarm_level=1 and alarm_code=10402,1,0) l1smoking,
        if(alarm_level=1 and alarm_code=90001,1,0) l1overspeed,
        if(alarm_level=1 and alarm_code=90002,1,0) l1overtime,
        if(alarm_level=1 and alarm_code=90007,1,0) l1night_25,
        if(alarm_level=2 and alarm_code=10401,1,0) l2call,
        if(alarm_level=2 and alarm_code= 10413,1,0) l2play,
        if(alarm_level=2 and alarm_code=10402,1,0) l2smoking,
        if(alarm_level=2 and alarm_code=90001,1,0) l2overspeed,
        if(alarm_level=2 and alarm_code=90002,1,0) l2overtime,
        if(alarm_level=2 and alarm_code=90007,1,0) l2night_25,
        if(alarm_level=3 and alarm_code=10401,1,0) l3call,
        if(alarm_level=3 and alarm_code= 10413,1,0) l3play,
        if(alarm_level=3 and alarm_code=10402,1,0) l3smoking,
        if(alarm_level=3 and alarm_code=90001,1,0) l3overspeed,
        if(alarm_level=3 and alarm_code=90002,1,0) l3overtime,
        if(alarm_level=3 and alarm_code=90007,1,0) l3night_25
        from gdispx_data.business_ai_alarm_info 
        where toDate(alarm_time) BETWEEN '{start_day}' and '{end_day}'
        and id not in 
        (SELECT 
        alarm_id 
        from gdispx_data.business_alarm_audit_info final
        where toDate(create_time) between '{start_day}' and '{risk_dispose_end_day}'
        and process_status=2 and dispose_type=7
        and alarm_id !=''
        )
        )
        group by vehicle_no,vehicle_color
    '''
    alarm_sql = f'''
        select 
            vehicle_no,
            vehicle_color,
            sum(type10400) tired_riskNum,
            sum(type10408) overtime_riskNum,
            sum(type10300) crash_riskNum,
            sum(type10403) notSeeAhead_riskNum,
            sum(type10302) vTooClose_riskNum,
            sum(type10301) notGoStraight_riskNum,
            sum(type10312) crossLine_riskNum,
            sum(type10410) unWearBelt_riskNum,
            sum(type10412) handsUp_riskNum
        from gdispx_data.statistics_original_alarm_day_all
        where alarm_time between '{start_day}' and '{end_day}'
        group by  vehicle_no,vehicle_color 
    '''
    final_sql = SQLHelper().table(jg_sql,'A')\
        .leftJoin().table(risk_sql,'B').on('A.vehicle_no = B.vehicle_no and A.vehicle_color = toUInt8(B.vehicle_color)')\
        .leftJoin().table(alarm_sql,'C').on('A.vehicle_no = C.vehicle_no and A.vehicle_color = C.vehicle_color') \
        .leftJoin().table(vin_sql, 'D').on('A.vehicle_no = D.vehicle_no and A.vehicle_color = toUInt8(D.vehicle_color) ')\
        .select('''
        A.vehicle_no vehicle_no,
        A.vehicle_color vehicle_color,
        owner_name,substring(vin,-7) vin,engine_number,
        splitByChar(',', area_manger_short_name)[2] AS city_name,
        splitByChar(',', area_manger_short_name)[3] AS area_name,
        compulsory_insurance_name,commercial_insurance_name,
        riskNum,l1_riskNum,l1_call,l1_play,l1_smoking,l1_overspeed,l1_overtime,
        l2_riskNum,l2_call,l2_play,l2_smoking,l2_overspeed,l2_overtime,
        l3_riskNum,l3_call,l3_play,l3_smoking,l3_overspeed,l3_overtime,
        tired_riskNum,overtime_riskNum,crash_riskNum,
        notSeeAhead_riskNum,vTooClose_riskNum,notGoStraight_riskNum,
        crossLine_riskNum,unWearBelt_riskNum,handsUp_riskNum,
        ex_days,third_party_name,is_online,is_ok,run_time
        ''')
    # print(final_sql)
    return final_sql

def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        ne = OrderedDict(zip(
            ('vehicle_no', 'vehicle_color', 'owner_name', 'vin', 'engine_number',
             'city_name', 'area_name', 'compulsory_insurance_name', 'commercial_insurance_name',
             'riskNum', 'l1_riskNum', 'l1_call', 'l1_play', 'l1_smoking','l1_overspeed','l1_overtime',
             'l2_riskNum', 'l2_call', 'l2_play', 'l2_smoking', 'l2_overspeed', 'l2_overtime',
             'l3_riskNum', 'l3_call', 'l3_play', 'l3_smoking', 'l3_overspeed', 'l3_overtime',
             'tired_riskNum', 'overtime_riskNum', 'crash_riskNum', 'notSeeAhead_riskNum',
             'vTooClose_riskNum','notGoStraight_riskNum','crossLine_riskNum',
             'unWearBelt_riskNum','handsUp_riskNum','ex_days','third_party_name','is_online','is_ok','run_time'),
            (e['vehicle_no'], e['vehicle_color'], e['owner_name'], e['vin'], e['engine_number'],
             e['city_name'], e['area_name'], e['compulsory_insurance_name'], e['commercial_insurance_name'],
             e['riskNum'],
             e['l1_riskNum'], e['l1_call'], e['l1_play'], e['l1_smoking'], e['l1_overspeed'],e['l1_overtime'],
             e['l2_riskNum'], e['l2_call'], e['l2_play'], e['l2_smoking'], e['l2_overspeed'],e['l2_overtime'],
             e['l3_riskNum'], e['l3_call'], e['l3_play'], e['l3_smoking'], e['l3_overspeed'],e['l3_overtime'],
             e['tired_riskNum'],e['overtime_riskNum'],e['crash_riskNum'],e['notSeeAhead_riskNum'],
             e['vTooClose_riskNum'], e['notGoStraight_riskNum'], e['crossLine_riskNum'],
             e['unWearBelt_riskNum'], e['handsUp_riskNum'],e['ex_days'],e['third_party_name'],
             e['is_online'],e['is_ok'],round(e['run_time']/3600,2)
             )
        ))
        outlist.append(ne)

    return outlist

def loadData(start_day, end_day):
    final_sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = orderByCity(datalist)
    outlist = export_outlist_orderDict(outlist)
    return outlist


def fill_sheet(elhander, inlist, year, month):
    sheet = elhander.activeWorksheet("Sheet1")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)


def writeByTemplate(datalist, year, month):
    _, fn = get_filePath_and_fileName(template_file)
    fn = fn.format(year=year, month=month).strip('template_month_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet(elhander, datalist, year, month)
    elhander.save()


def main(start_day, end_day):
    datalist = loadData(start_day, end_day)
    # h = list(datalist[0].keys())
    # write_csv(join_path(bpath,'vt4.csv'),h,datalist)
    year = start_day.split('-')[0]
    month = start_day.split('-')[1]
    writeByTemplate(datalist,year,month)


if __name__ == '__main__':
    start, end,days = TimeUtils.getLastMonthRange()
    main(start, end)
