#!/usr/bin/env python
# -*- coding: utf-8 -*-
from cassandra.cluster import Cluster
from cassandra.auth import PlainTextAuthProvider
# import psycopg2
from clickhouse_driver import Client
import pymysql


class CassandraDB:
    def __init__(self):
        self.cluster = Cluster(contact_points=['**************'],
                               auth_provider=PlainTextAuthProvider(username='cassandra', password='cassandra'))
        self.session = self.cluster.connect()

    def get_session(self):
        return self.session

    def shutdown(self):
        if self.cluster:
            self.cluster.shutdown()


# class PgDB:
#     def __init__(self, db, user, pw, host, port):
#         self.conn = psycopg2.connect(database=db, user=user, password=pw, host=host, port=port)
#
#     def get_cursor(self):
#         return self.conn.cursor()
#
#     def close(self):
#         if self.conn:
#             self.conn.colse()


class CkDB:
    # def __init__(self, host, port, user, password, database):
    #     self.client = Client(host=host, port=port, user=user, password=password, database=database,
    #                          send_receive_timeout=5)
    def __init__(self):
        self.client = Client(host='**************', port=9000, user='default', password='<EMAIL>',
                             database='gdispx_data', send_receive_timeout=5)

    def get_client(self):
        return self.client

    def disconnect(self):
        self.client.disconnect()


class MySqlDB:
    def __init__(self):
        self.conn = pymysql.connect(db='gdispx', user='admin', password='rpamy82', host='***********', port=15322)

    def get_cursor(self):
        return self.conn.cursor(pymysql.cursors.DictCursor)

    def close(self):
        if self.conn:
            self.conn.close()

