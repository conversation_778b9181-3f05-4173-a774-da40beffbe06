'''
1. 下载更新业户信息
'''
from yyutils import *
import os

bpath = r'/data/report_data/owner_info'


def getOwnerType(owner_type):
    t = []
    if not owner_type:
        return t
    if '1' in owner_type:
        t.append('客运')
    if '3' in owner_type:
        t.append('危运')
    if '4' in owner_type:
        t.append('重货')
    return t


def update_local_data(datalist,owner_alarmLevel, bpath):
    today = TimeUtils.Today()
    for e in datalist:
        t1 = getOwnerType(e['owner_type'])
        e['owner_type'] = '|'.join(t1)
        e['alarm_level'] = owner_alarmLevel.get(e['owner_id']) if owner_alarmLevel.get(e['owner_id']) else ''
    h = [
        'province_id', 'province_name', 'city_id', 'city_name', 'area_id', 'area_name',
        'area_manager_name', 'owner_id', 'owner_name', 'org_type', 'credit_code',
        'status', 'manager', 'manager_phone', 'contactor', 'contactor_phone', 'access_date', 'owner_type','alarm_level'
    ]
    otfile = join_path(bpath, f'{today}_owner_info.csv')
    write_csv(otfile, h, datalist)
    try:
        os.remove(r'/data/track_report/report/owner.zip')
    except Exception as ex:
        pass
    os.system(f'cd {bpath} && zip -q /data/track_report/report/owner.zip {today}_owner_info.csv')
    os.remove(otfile)


def cal_alarm_level(inlist):
    vt_count = {1: 0, 3: 0, 4: 0}
    ids = set()
    for e in inlist:
        if e['owner_id'] in ids:
            continue
        if int(e['vehicle_type']) in (1, 3, 4):
            vt_count[int(e['vehicle_type'])] += 1
    owner_alarmLevel = {}
    for e in inlist:
        rowsId = int(e['rowsId'])
        owner_id = e['owner_id']
        vt = int(e['vehicle_type'])
        if vt not in (1, 3, 4):
            continue
        c = vt_count.get(vt)
        f1 = round(float(rowsId)*100/c,2)
        b= (1 if f1>0 else 0) + (1 if f1>25 else 0) + (1 if f1>50 else 0) + (1 if f1>75 else 0)
        owner_alarmLevel.setdefault(owner_id,b)
    return owner_alarmLevel

def load_owner_info():
    if not os.path.exists(bpath):
        os.makedirs(bpath)
    sql = f'''
        select 
            ba.province_id province_id,
            ba3.name province_name,
            ba.city_id city_id,
            ba2.name city_name,
            ba.id area_id,
            if(ba.id=ba.city_id,'市辖区',ba.name) area_name,
            ba.manger_name area_manager_name,
            bo.owner_id owner_id ,
            bo.owner_name owner_name ,
            bo.economic_type org_type,
            bo.unify_social_code credit_code,
            bo.status status,
            bo.owner_principal manager,
            bo.principal_phone manager_phone,
            bo.linkman contactor,
            bo.linkman_phone contactor_phone,
            A.access_date access_date,
            A.owner_type owner_type
        from gdispx_basics.basics_owner bo 
        left join gdispx_basics.basics_owner_area boa 
        on bo.owner_id = boa.owner_id 
        left join gdispx_basics.basics_area ba 
        on boa.area_id = ba.id
        left join gdispx_basics.basics_area ba2 
        on ba.city_id = ba2.id 
        left join gdispx_basics.basics_area ba3 
        on ba.province_id = ba3.id
        left join 
        (
        select 
            owner_id , 
            min(if(YEAR(access_time)<2021,'2099-12-31',date(access_time))) access_date,
            GROUP_CONCAT(distinct vehicle_type) owner_type
        from gdispx_basics.basics_vehicle bv 
        group by owner_id 
        ) A on A.owner_id = bo.owner_id
    '''
    db = DBUtil(DBType.MYSQL.value)
    datalist = db.query_to_dictList(sql)
    db.close()
    db = DBUtil(DBType.CLICKHOUSE.value)
    sql = f'''
            SELECT
                *
            from
                gdispx_data.owers_report_month
            where
                alarm_time in 
                (select max(distinct alarm_time)
                from gdispx_data.owers_report_month )
        '''
    owner_report_list = db.query_to_dictList(sql)
    db.close()
    owner_alarmLevel = cal_alarm_level(owner_report_list)
    update_local_data(datalist,owner_alarmLevel, bpath)


if __name__ == '__main__':
    load_owner_info()
