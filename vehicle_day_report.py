#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB
import utils

GROUP_BY_WHOLE = 0
GROUP_BY_CITY = 1
GROUP_BY_FACILITATOR = 2
GROUP_BY_PRODUCTER = 3
# 锐明专项
GROUP_BY_WHOLE_RM = 10
GROUP_BY_CITY_RM = 11
GROUP_BY_FACILITATOR_RM = 12

GROUP_BY_WHOLE_TYPES = [GROUP_BY_WHOLE, GROUP_BY_WHOLE_RM]
GROUP_BY_CITY_TYPES = [GROUP_BY_CITY, GROUP_BY_CITY_RM]
GROUP_BY_FACILITATOR_TYPES = [GROUP_BY_FACILITATOR, GROUP_BY_FACILITATOR_RM]
GROUP_BY_PRODUCTER_TYPES = [GROUP_BY_PRODUCTER]

RM_TYPES = [GROUP_BY_WHOLE_RM, GROUP_BY_CITY_RM, GROUP_BY_FACILITATOR_RM]


def ck_results_to_dict(ck_results):
    """
    将ck的查询结果转换成字典,每行的第一个值是主键
    :param ck_results:
    :return:
    """
    cols = [f[0] for f in ck_results[1]]
    result_dict = {None: dict(zip(cols[1:], [''] * (len(cols) - 1)))}
    rows = ck_results[0]
    for row in rows:
        result_dict[row[0]] = dict(zip(cols, row))
    return result_dict


def merge_ck_results_list(ck_results_list):
    """
    将ck的多个查询结果合并,每个结果里的每行的第一个值是合并key
    :param ck_results_list:
    :return:
    """
    merge_results = ck_results_to_dict(ck_results_list[0])
    for results in ck_results_list[1:]:
        result_i = ck_results_to_dict(results)
        for k in merge_results:
            if k in result_i:
                merge_results[k].update(result_i[k])
            else:
                merge_results[k].update(result_i[None])
    return merge_results


def f4_stats_sql(group_type):
    query_sql = "SELECT "
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += "city as `name`"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += "facilitator_name as `name`"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += "master_producter_name as `name`"
    else:
        query_sql += "'ALL' as `name`"
    query_sql += ",toDate('{stats_date}') as `date`,{type} as `type`"
    query_sql += """
       , incon_num 
       , innet_num
       , round(innet_num/incon_num*100,2) as `innet_rate`
       , online_num
       , round(online_num/innet_num*100,2) as `online_rate`
       , drift_num as `freq_drift_num`
       , round(drift_num/online_num*100,2) as `drift_rate`
       , total_mileage
       , continuous_mileage
       , round(continuous_mileage/total_mileage*100,2) as `continuous_rate`
       from
     (
        WITH (select avg(tr.drift_point_count) from gdispx_data.track_report tr
              join gdispx_data.v_regulator_vehicle_related_infos vrvri on tr.imei = trim(vrvri.master_sim_code)
            ) as avg_drift_point_count
        select
    """
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += " splitByChar(',', vrvri.manger_short_name)[2] city,"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += " any(vrvri.facilitator_name) facilitator_name,"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += " any(vrvri.master_producter_name) master_producter_name,"
    query_sql += """
            count() incon_num
            , countIf(bg.device_id != '') innet_num
            , countIf(bg.gps_time > addDays(now(),-7)) online_num
            , countIf(tr.drift_point_count > avg_drift_point_count) drift_num
            , sum(tr.total_mileage) total_mileage
            , sum(tr.continuous_mileage) continuous_mileage
        from gdispx_data.v_regulator_vehicle_related_infos vrvri
        left join gdispx_data.business_gpsdatas bg on bg.device_id = trim(vrvri.master_sim_code)
        left join (select imei
                        , sum(drift_point_count) drift_point_count
                        , sum(total_mileage) total_mileage
                        , sum(continuous_mileage) continuous_mileage
                     from gdispx_data.track_report where `date` = '{track_date}'
                     group by imei) tr
                     on tr.imei = trim(vrvri.master_sim_code)
        where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
    """
    if group_type in RM_TYPES:
        query_sql += " and vrvri.master_producter_name = '深圳市锐明技术股份有限公司'"
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += " group by splitByChar(',', vrvri.manger_short_name)[2]"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += " group by vrvri.facilitator_id"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += " group by vrvri.master_producter_id"
    query_sql += ") t"
    return query_sql.format(stats_date=stats_date, type=group_type, track_date=stats_date.replace('-', '')[-6:])


def alarm_stats_sql(group_type):
    query_sql = "SELECT "
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += "city as `name`"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += "facilitator_name as `name`"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += "master_producter_name as `name`"
    else:
        query_sql += "'ALL' as `name`"
    query_sql += """
           , noalarm_num
           , round(noalarm_num/innet_num*100,2) as `noalarm_rate`
           , alarm_num
           , freq_alarm_num as `freq_alarm_num`
           , round(freq_alarm_num/alarm_num*100,2) as `freq_alarm_rate`
         from
         (
            WITH (
            select round(avg(alarm_cnt_100km))
            from (
                select toUInt64(round(device_alarm_cnt / mileage_100km)) alarm_cnt_100km
                from (
                    select imei, round(total_mileage/100,1) mileage_100km
                    from gdispx_data.track_report
                    where `date` = '{track_date}' and total_mileage > 1 AND total_mileage < 2400
                    ) a 
                join (select device_id, count() device_alarm_cnt from gdispx_data.business_alarm_info
                    where toDate(alarm_time) = toDate('{stats_date}')
                    group by device_id
                    ) b on a.imei = b.device_id
                )
           ) AS device_alarm_cnt_avg
            select
        """
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += " splitByChar(',', vrvri.manger_short_name)[2] city,"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += " any(vrvri.facilitator_name) facilitator_name,"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += " any(vrvri.master_producter_name) master_producter_name,"
    query_sql += """
                 countIf(bg.device_id != '') innet_num
                , countIf(bg.gps_time > addDays(now(),-7)) online_num
                , countIf(bg.device_id != '' and no_alarm.device_id = '') noalarm_num
                , countIf(alarm.device_id != '') alarm_num
                , countIf(alarm.device_alarm_cnt_100km > device_alarm_cnt_avg) freq_alarm_num
            from gdispx_data.v_regulator_vehicle_related_infos vrvri
            left join gdispx_data.business_gpsdatas bg on trim(bg.device_id) = trim(vrvri.master_sim_code)
            left join (select distinct device_id from gdispx_data.business_alarm_info
                        where alarm_time > toDate('2020-11-01')) no_alarm on no_alarm.device_id = vrvri.master_sim_code
            left join (select device_id, if(mileage_100km > 0, toUInt64(round(device_alarm_cnt / mileage_100km)), 0) device_alarm_cnt_100km
                       from (select device_id, count() device_alarm_cnt from gdispx_data.business_alarm_info
                			where toDate(alarm_time) = toDate('{stats_date}') group by device_id) bai
            		   left join (select imei, round(total_mileage/100,1) mileage_100km from gdispx_data.track_report 
                  				where `date` = '{track_date}' and total_mileage > 1 and total_mileage < 2400) tr on tr.imei = bai.device_id
                        ) alarm on alarm.device_id = vrvri.master_sim_code
            where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                    or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
        """
    if group_type in RM_TYPES:
        query_sql += " and vrvri.master_producter_name = '深圳市锐明技术股份有限公司'"
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += " group by splitByChar(',', vrvri.manger_short_name)[2]"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += " group by vrvri.facilitator_id"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += " group by vrvri.master_producter_id"
    query_sql += ") t"
    return query_sql.format(stats_date=stats_date, track_date=stats_date.replace('-', '')[-6:])


def acc_stats_sql(group_type):
    query_sql = "SELECT "
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += "city as `name`"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += "facilitator_name as `name`"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += "master_producter_name as `name`"
    else:
        query_sql += "'ALL' as `name`"
    query_sql += """
           , acc_num - acchave_num as `acc_miss_num`
           , acc_num as `acc_num`
           , round((acc_num-acchave_num)/acc_num*100,2) as `acc_miss_rate`
         from
         (select
        """
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += " splitByChar(',', vrvri.manger_short_name)[2] city,"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += " any(vrvri.facilitator_name) facilitator_name,"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += " any(vrvri.master_producter_name) master_producter_name,"
    query_sql += """sum(bai.device_acc_num) acc_num
                , sum(bai.device_acchave_num) acchave_num
            from gdispx_data.v_regulator_vehicle_related_infos vrvri
            left join (select 
                         device_id
                         , count() device_acc_num
                         , countIf(alarm_time >= addMinutes(create_time,-6) and b.alarm_tag != '') device_acchave_num
                        from (select device_id, alarm_time, alarm_tag from gdispx_data.business_alarm_info 
                                where toDate(alarm_time) = toDate('{stats_date}')
                                and alarm_code in (10400,10401,10402,10410,10413,10412)) a
                        left join (select alarm_tag,create_time from gdispx_data.business_accessories_info
                                    where toDate(create_time) = toDate('{stats_date}')) b 
                          on a.alarm_tag = b.alarm_tag
                        group by device_id) bai
                    on bai.device_id = trim(vrvri.master_sim_code)
            where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                    or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
        """
    if group_type in RM_TYPES:
        query_sql += " and vrvri.master_producter_name = '深圳市锐明技术股份有限公司'"
    if group_type in GROUP_BY_CITY_TYPES:
        query_sql += " group by splitByChar(',', vrvri.manger_short_name)[2]"
    elif group_type in GROUP_BY_FACILITATOR_TYPES:
        query_sql += " group by vrvri.facilitator_id"
    elif group_type in GROUP_BY_PRODUCTER_TYPES:
        query_sql += " group by vrvri.master_producter_id"
    query_sql += ") t"
    return query_sql.format(stats_date=stats_date)


def query_and_merge(group_type):
    query_sql_list = [f4_stats_sql(group_type), alarm_stats_sql(group_type), acc_stats_sql(group_type)]
    # for query_sql in query_sql_list:
    #    print(query_sql)
    merge_results = merge_ck_results_list(
        list(map(lambda query_sql: client.execute(query_sql, with_column_types=True), query_sql_list)))
    merge_results.pop(None)
    return list(merge_results.values())


def save_stats_results(stats_results):
    if len(stats_results) > 0:
        insert_sql = "INSERT into gdispx_data.vehicle_stats_day (`date`, `type`, `name`, " \
                     "`incon_num`, `innet_num`, `innet_rate`, `online_num`, `online_rate`, `freq_drift_num`, `drift_rate`, " \
                     "`total_mileage`, `continuous_mileage`, `continuous_rate`, " \
                     "`noalarm_num`, `noalarm_rate`, `alarm_num`, `freq_alarm_num`, `freq_alarm_rate`, " \
                     "`acc_miss_num`, `acc_num`, `acc_miss_rate`) values"
        client.execute(insert_sql, stats_results)


if __name__ == '__main__':
    ck = CkDB()
    try:
        client = ck.get_client()
        stats_date = '2021-08-26'
        utils.print_log('begin stats for ' + stats_date)
        stats_results = []
        stats_results += query_and_merge(GROUP_BY_WHOLE)
        stats_results += query_and_merge(GROUP_BY_CITY)
        stats_results += query_and_merge(GROUP_BY_FACILITATOR)
        stats_results += query_and_merge(GROUP_BY_PRODUCTER)

        # 锐明专项
        stats_results += query_and_merge(GROUP_BY_WHOLE_RM)
        stats_results += query_and_merge(GROUP_BY_CITY_RM)
        stats_results += query_and_merge(GROUP_BY_FACILITATOR_RM)
        utils.print_log('finish stats for {} with {} result'.format(stats_date, len(stats_results)))
        save_stats_results(stats_results)
    finally:
        ck.disconnect()

