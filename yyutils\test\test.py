from yyutils import *


def main(bpath):
    infile = join_path(bpath, '疑似屏蔽信号-业户top.csv')
    inlist = readCsv(infile)
    city_g = list_2_dictlist(inlist, 'city_name')
    outlist = []
    for city, g in city_g.items():
        type_sg = list_2_dictlist(g, 'vehicle_type')
        for t, sg in type_sg.items():
            tlist = list(filter(lambda o: o['exception_count'] != '0', sg))
            tlist.sort(key=lambda o: -int(o['exception_count']))
            if len(tlist) <= 10:
                outlist.extend(tlist)
                continue
            top10 = int(tlist[9]['exception_count'])
            tlist1 = list(filter(lambda o: int(o['exception_count']) >= int(top10), tlist))
            outlist.extend(tlist1)
    h = ['地市', '辖区县', '业户名称', '经营范围', '应监管车辆数', '入网车辆数',
         '存在疑似屏蔽问题的车辆数', '占比']
    outlist1 = []
    for e in outlist:
        if e['vehicle_type'] == '1':
            t = '客运'
        elif e['vehicle_type'] == '3':
            t = '危运'
        else:
            t = '重货'
        ne = {
            '地市': e["city_name"],
            '辖区县': e["area_name"],
            '业户名称': e["owner_name"],
            '经营范围': t,
            '应监管车辆数': e["xjg_count"],
            '入网车辆数': e["jg_count"],
            '存在疑似屏蔽问题的车辆数': e["exception_count"],
            '占比': round(int(e["exception_count"]) / int(e["jg_count"]), 4)
        }
        outlist1.append(ne)
    otfile = join_path(bpath, '疑似屏蔽信号-业户top10.csv')

    write_csv(otfile, h, outlist1)


if __name__ == '__main__':
    bpath = r'C:\工作目录\粤运\doc\工单处理\月报'
    main(bpath)
