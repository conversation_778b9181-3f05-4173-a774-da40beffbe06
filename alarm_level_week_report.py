#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

alarm_level_query = """
SELECT 
dictGet('gdispx_data.basics_area', 'city_short', (toInt32(any(sad.area_id)),toInt32(440000))) as `所属地市`
,SUM(sad.oneLevel) AS `一级风险数`
,SUM(sad.twoLevel) AS `二级风险数`
,SUM(sad.threeLevel) AS `三级风险数`
,SUM(sad.alarmNum) AS `合计风险数`
FROM gdispx_data.statistics_alarm_day AS sad
where toDate(sad.alarm_time) >= toDate('{begin_date}') and toDate(sad.alarm_time) < toDate('{end_date}')
and sad.vehicle_type in (1,2,3,4)
GROUP BY sad.city_id
"""


def query_then_write_csv(query_sql, client, output_file):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join([col[0] for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join([str(x) for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        output_file_name = '风险统计_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            query_then_write_csv(alarm_level_query.format(begin_date=begin_date, end_date=end_date),
                                 client, output_file)
    finally:
        ck.disconnect()


if __name__ == '__main__':
    begin_date = '2021-09-01'
    end_date = '2021-09-05'
    export(begin_date, end_date)
