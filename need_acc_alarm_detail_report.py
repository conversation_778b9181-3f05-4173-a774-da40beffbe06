#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

need_acc_alarm_detail_select = """
    select
        vrvri.manger_short_name
        , splitByChar(',', vrvri.manger_short_name)[2] as `城市`
        , vrvri.vehicle_no as `车牌号`
        , transform(vrvri.vehicle_color, ['1','2','93','94'], ['蓝色','黄色','黄绿双拼色','渐变绿色']) as `车牌颜色`
        , transform(vrvri.vehicle_type, ['1','3','4'], ['客运车辆','危险货运','重型货车']) as `车辆类型`  
        , vrvri.owner_name as `业户`
        , vrvri.facilitator_name as `服务商`
        , vrvri.master_producter_name  as `设备厂商` 
        , transform(vrvri.service_result, ['1','0','-1'], ['营运','待营运','报停、过户、过户转出'], '其他') `营运状态`
        , case when vrvri.master_producter_name like '非粤标设备%' then '非粤标' when empty(vrvri.master_producter_name) then '' else '粤标' end as `设备类型`
        , bai.alarm_code `报警类型编码`
        , bai.alarm_code_name `报警类型`
        , bai.alarm_time `报警时间`
        , has_acc_in_6m `6分钟是否有附件`
        , has_acc_in_24h `24小时是否有附件`
    from gdispx_data.v_regulator_vehicle_related_infos vrvri
    join gdispx_data.business_gpsdatas bg on bg.device_id = trim(vrvri.master_sim_code)
    join (select 
             a.device_id
             , alarm_time
             , alarm_code
             , alarm_code_name
             , if(b.alarm_tag != '' and create_time < addMinutes(alarm_time,6),1,0) has_acc_in_6m
             , if(b.alarm_tag != '' and create_time < addDays(alarm_time,1),1,0) has_acc_in_24h
          from (select device_id, alarm_time, alarm_code, alarm_code_name, alarm_tag from gdispx_data.business_alarm_info 
                where alarm_time >= toDate('{begin_date}') and alarm_time < toDate('{end_date}')
                and alarm_code in (10400,10401,10402,10410,10413,10412)) a
          left join (select alarm_tag,create_time from gdispx_data.business_accessories_info
            where create_time >= toDate('{begin_date}') and create_time < toDate('{end_date}') + 1) b on a.alarm_tag = b.alarm_tag
         ) bai on bai.device_id = trim(vrvri.master_sim_code) 
    where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
          or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
"""


def query_then_write_csv(query_sql, client, output_file):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join(['"' + col[0] + '"' for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join(['"' + str(x) + '"' for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        file_suffix = '_{}.csv'.format(begin_date.replace('-', '')[-4:])

        with open('需上传附件报警明细' + file_suffix, 'w') as output_file:
            query_then_write_csv(need_acc_alarm_detail_select.format(begin_date=begin_date,
                                                                     end_date=end_date)
                                 , client, output_file)

    finally:
        ck.disconnect()


if __name__ == '__main__':
    from datetime import datetime, timedelta
    # 获取今天(现在时间)
    today = datetime.today()
    # 2天之前
    begin_date = (today - timedelta(days=2)).strftime('%Y-%m-%d')
    end_date = (today - timedelta(days=1)).strftime('%Y-%m-%d')
    export(begin_date, end_date)

