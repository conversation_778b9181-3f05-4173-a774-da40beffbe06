#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

GROUP_BY_WHOLE = 0
GROUP_BY_CITY = 1
GROUP_BY_FACILITATOR = 2
GROUP_BY_PRODUCTER = 3


def export_L5(group_type, begin_date, end_date, ruiming=False):
    """
    24小时版本
    使用中间表查询
    """
    query_sql = "SELECT "
    if group_type == GROUP_BY_CITY:
        query_sql += "city as `城市`,"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += "if(facilitator_name = '', toString(facilitator_id), facilitator_name) as `运营商`,"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += "if(master_producter_name = '', toString(master_producter_id), master_producter_name) as `设备厂家`,"
    query_sql += """
            alarm_num as `报警车辆数`
           , freq_alarm_num as `频繁报警车辆数`
           , round(freq_alarm_num/alarm_num*100,2) as `频繁报警率`
           , acc_miss_num_sum as `附件丢失报警数`
           , need_acc_num_sum as `需上传附件报警总数`
           , round(acc_miss_num_sum/need_acc_num_sum*100,2) as `附件丢失率`
         from
         (
            WITH (
            select round(avg(alarm_cnt_100km))
            from (
                select toUInt64(round(alarm_num_sum / mileage_100km)) alarm_cnt_100km
                from gdispx_data.v_regulator_vehicle_related_infos vrvri
                join (
                    select imei, round(sum(total_mileage)/100,1) mileage_100km
                    from gdispx_data.track_report
                    where `date` >= '{track_begin_date}' and `date` < '{track_end_date}'
                    group by imei
                    ) a on trim(vrvri.master_sim_code) = a.imei
                join (select device_id, sum(alarm_num) alarm_num_sum from gdispx_data.vehicle_alarm_stats_day
                    where `date` >= toDate('{begin_date}') and `date` < toDate('{end_date}')
                    group by device_id
                    ) b on a.imei = b.device_id
                where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                    or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
                )
           ) AS device_alarm_cnt_avg
            select
        """
    if group_type == GROUP_BY_CITY:
        query_sql += " splitByChar(',', vrvri.manger_short_name)[2] city,"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += " vrvri.facilitator_id as facilitator_id, any(vrvri.facilitator_name) facilitator_name,"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += " vrvri.master_producter_id as master_producter_id, any(vrvri.master_producter_name) master_producter_name,"
    query_sql += """
                 count() alarm_num
                , countIf(if(mileage_100km > 0, toUInt64(round(alarm_num_sum / mileage_100km)), 0) > device_alarm_cnt_avg) freq_alarm_num
                , sum(need_acc_num_sum) need_acc_num_sum
                , sum(acc_miss_num_sum) acc_miss_num_sum
            from gdispx_data.v_regulator_vehicle_related_infos vrvri
            join (
                select device_id, sum(alarm_num) alarm_num_sum, sum(need_acc_num) need_acc_num_sum, sum(acc_miss_num) acc_miss_num_sum 
                from gdispx_data.vehicle_alarm_stats_day
                where `date` >= toDate('{begin_date}') and `date` < toDate('{end_date}')
                group by device_id
            ) vasd on vasd.device_id = trim(vrvri.master_sim_code)
            left join (
                select imei, round(sum(total_mileage)/100,1) mileage_100km from gdispx_data.track_report 
                where `date` >= '{track_begin_date}' and `date` < '{track_end_date}'
                group by imei
            ) tr on tr.imei = vasd.device_id
            where vrvri.master_sim_code != ''
             and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                    or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
        """
    if ruiming:
        query_sql += " and vrvri.master_producter_name = '深圳市锐明技术股份有限公司'"
    if group_type == GROUP_BY_CITY:
        query_sql += " group by splitByChar(',', vrvri.manger_short_name)[2]"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += " group by vrvri.facilitator_id"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += " group by vrvri.master_producter_id"
    query_sql += ") t"
    # query_sql
    return query_sql.format(begin_date=begin_date,
                            end_date=end_date,
                            track_begin_date=begin_date.replace('-', '')[-6:],
                            track_end_date=end_date.replace('-', '')[-6:])


def query_then_write_csv(query_sql, client, output_file):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join([col[0] for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join([str(x) for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        output_file_name = '合格率指标统计_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            output_file.write('整体\n')
            query_then_write_csv(export_L5(GROUP_BY_WHOLE, begin_date, end_date), client, output_file)
            output_file.write('\n\n按城市\n')
            query_then_write_csv(export_L5(GROUP_BY_CITY, begin_date, end_date), client, output_file)
            output_file.write('\n\n按运营商\n')
            query_then_write_csv(export_L5(GROUP_BY_FACILITATOR, begin_date, end_date), client, output_file)
            output_file.write('\n\n按设备厂商\n')
            query_then_write_csv(export_L5(GROUP_BY_PRODUCTER, begin_date, end_date), client, output_file)
    finally:
        ck.disconnect()


if __name__ == '__main__':
    begin_date = '2021-10-23'
    end_date = '2021-10-25'
    export(begin_date, end_date)

