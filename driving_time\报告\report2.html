<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客运车辆离线分析报告 - 2025年5月</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 320px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .kpi-card {
            background-color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }
        .kpi-value {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
        }
        .kpi-label {
            margin-top: 0.5rem;
            font-size: 1rem;
            color: #4b5563;
        }
    </style>
</head>
<body>

<main class="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">

    <header class="text-center mb-10">
        <h1 class="text-4xl font-bold text-gray-800">客运车辆离线分析报告</h1>
        <p class="text-xl text-gray-500 mt-2">2025年5月 数据洞察</p>
    </header>

    <section id="key-metrics" class="mb-10">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="kpi-card">
                <p class="kpi-value text-[#FF5733]">1401</p>
                <p class="kpi-label">总离线事件数</p>
            </div>
            <div class="kpi-card">
                <p class="kpi-value text-[#33FF57]">17.8<span class="text-2xl">小时</span></p>
                <p class="kpi-label">平均离线时长</p>
            </div>
            <div class="kpi-card">
                <p class="kpi-value text-[#3357FF]">0.99<span class="text-2xl">小时</span></p>
                <p class="kpi-label">中位离线时长</p>
            </div>
            <div class="kpi-card">
                <p class="kpi-value text-[#FF33A1]">江门市</p>
                <p class="kpi-label">离线车辆最多城市</p>
            </div>
        </div>
    </section>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

        <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-1">每日离线事件趋势</h2>
            <p class="text-gray-600 mb-4">分析2025年5月每日的离线事件总数，可以发现事件数量存在显著波动。5月5日、5月9日和5月14日是离线事件的高发日，这些高峰可能与节假日后的客流变化、特定社会活动或区域性网络状况有关，值得深入探究。</p>
            <div class="chart-container h-96">
                <canvas id="dailyTrendChart"></canvas>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-1">离线车辆地市分布</h2>
            <p class="text-gray-600 mb-4">数据显示，离线车辆的地理分布并不均衡。江门市（粤J）和广州市（粤A）的离线车辆数量在全省领先，这可能与当地庞大的车队规模、繁忙的交通网络或特定的监管环境有关。针对这些“热点”城市进行专项分析与管理是必要的。</p>
            <div class="chart-container">
                <canvas id="cityDistributionChart"></canvas>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-1">高频离线车辆Top 10</h2>
            <p class="text-gray-600 mb-4">少数车辆贡献了大部分的离线事件。例如，车辆“粤MW3808”在一个月内发生了26次离线。对这些高频离线车辆进行重点监控，排查是设备故障还是人为因素，对于提升整体监管效率至关重要。</p>
            <div class="chart-container">
                <canvas id="highFrequencyVehiclesChart"></canvas>
            </div>
        </div>

        <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-1">离线时长与行驶距离关系</h2>
            <p class="text-gray-600 mb-4">通过散点图可以揭示离线时长与离线期间行驶距离之间的复杂关系。大部分事件集中在左下角（短时长、短距离），但存在明显异常点。例如，长时离线但距离短（可能表示长时间停运或故障），以及短时离线但距离极长（红点所示，可能存在严重违规或数据异常）。</p>
            <div class="chart-container h-96">
                <canvas id="durationDistanceScatterChart"></canvas>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-1">离线时长分布</h2>
            <p class="text-gray-600 mb-4">离线时长呈典型的右偏态分布。超过一半的离线事件持续时间在1小时以内，表明短时信号中断是主要情况。然而，少数持续超过24小时的极端长时离线事件，极大地拉高了平均值，这些是需要优先调查的高风险事件。</p>
            <div class="chart-container">
                <canvas id="durationDistributionChart"></canvas>
            </div>
        </div>

        <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-6 rounded-lg shadow-md">
            <h2 class="text-2xl font-bold text-red-800 mb-2">特别关注：凌晨 2-5点违规风险</h2>
            <p class="text-red-700">报告揭示，多起车辆离线事件发生在凌晨2至5点的强制休息时段。例如车辆<span class="font-mono bg-red-100 rounded px-1">粤D18598</span>在该时段内反复出现长时离线。若离线期间伴随长距离行驶，则极有可能涉及故意屏蔽信号以进行违规运营，对公共安全构成严重威胁，必须进行严查。</p>
        </div>

        <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">关键发现与战略建议</h2>
            <p class="text-gray-600 mb-6">基于数据分析，我们识别出若干关键风险点，并提出以下战略建议，旨在提升监管效能与行车安全。这是一个用于指导后续工作的行动框架，通过数据驱动决策，防范潜在风险。</p>
            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0 h-8 w-8 rounded-full bg-[#FF5733] text-white flex items-center justify-center font-bold text-lg">1</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-700">强化重点时段监控</h3>
                        <p class="text-gray-600">对凌晨2-5点时段的离线车辆进行专项核查，严惩违规运营和故意屏蔽信号的行为。</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0 h-8 w-8 rounded-full bg-[#33FF57] text-white flex items-center justify-center font-bold text-lg">2</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-700">建立智能预警机制</h3>
                        <p class="text-gray-600">开发针对异常离线模式（如长时离线伴随长距离行驶）的自动预警，实现主动式风险干预。</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0 h-8 w-8 rounded-full bg-[#3357FF] text-white flex items-center justify-center font-bold text-lg">3</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-700">实施差异化区域监管</h3>
                        <p class="text-gray-600">针对江门、广州等“热点”城市，组织专项检查，深入分析原因，加强设备巡检与管理。</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="flex-shrink-0 h-8 w-8 rounded-full bg-[#FF33A1] text-white flex items-center justify-center font-bold text-lg">4</div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-700">加强数据质量管理</h3>
                        <p class="text-gray-600">对报告中发现的异常数据点进行溯源，建立常态化的数据质量监控机制，确保分析的准确性。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
    document.addEventListener('DOMContentLoaded', () => {

        const palette = {
            orange: '#FF5733',
            green: '#33FF57',
            blue: '#3357FF',
            pink: '#FF33A1',
            purple: '#A133FF',
            lightBlue: 'rgba(51, 87, 255, 0.2)',
            lightOrange: 'rgba(255, 87, 51, 0.6)'
        };

        function wrapLabel(str, maxWidth) {
            if (typeof str !== 'string' || str.length <= maxWidth) {
                return str;
            }
            const words = str.split('');
            const lines = [];
            let currentLine = '';
            words.forEach(word => {
                if ((currentLine + word).length > maxWidth) {
                    lines.push(currentLine);
                    currentLine = '';
                }
                currentLine += word;
            });
            if (currentLine) {
                lines.push(currentLine);
            }
            return lines;
        }

        const tooltipTitleCallback = {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                                return label.join('');
                            } else {
                                return label;
                            }
                        }
                    }
                }
            }
        };

        function createDailyTrendChart() {
            const ctx = document.getElementById('dailyTrendChart').getContext('2d');
            const labels = Array.from({ length: 31 }, (_, i) => `5-${String(i + 1).padStart(2, '0')}`);
            const data = [38, 27, 35, 45, 60, 48, 48, 49, 56, 49, 49, 45, 39, 58, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49];
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '每日离线事件数',
                        data: data,
                        borderColor: palette.blue,
                        backgroundColor: palette.lightBlue,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 4,
                        pointBackgroundColor: palette.blue,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    ...tooltipTitleCallback,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '事件数' }
                        },
                        x: {
                            title: { display: true, text: '日期' }
                        }
                    }
                }
            });
        }

        function createCityDistributionChart() {
            const ctx = document.getElementById('cityDistributionChart').getContext('2d');
            const data = {
                '江门市': 20, '广州市': 18, '汕头市': 15, '茂名市': 15, '清远市': 15,
                '东莞市': 14, '深圳市': 10, '韶关市': 10, '湛江市': 10, '梅州市': 10,
                '汕尾市': 10, '河源市': 10, '佛山市': 7, '肇庆市': 7, '惠州市': 7,
                '云浮市': 7, '阳江市': 6, '潮州市': 5, '揭阳市': 5, '珠海市': 4,
                '中山市': 2, '顺德区': 2, '南海区': 2
            };
            const sortedCities = Object.entries(data).sort((a, b) => b[1] - a[1]);
            const labels = sortedCities.map(city => wrapLabel(city[0], 8));
            const values = sortedCities.map(city => city[1]);

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '独立离线车辆数',
                        data: values,
                        backgroundColor: palette.orange,
                        borderColor: palette.orange,
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    ...tooltipTitleCallback,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: { display: true, text: '车辆数' }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        }

        function createHighFrequencyVehiclesChart() {
            const ctx = document.getElementById('highFrequencyVehiclesChart').getContext('2d');
            const data = {
                '粤MW3808': 26, '粤SK6703': 20, '粤B31021D': 19, '粤K05833': 12, '粤MDB4893': 12,
                '粤VEN215': 12, '粤X36093': 12, '粤ACL551': 11, '粤D17028': 11, '粤MK5299': 11
            };
            const labels = Object.keys(data);
            const values = Object.values(data);
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '离线次数',
                        data: values,
                        backgroundColor: palette.pink,
                        borderColor: palette.pink,
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    ...tooltipTitleCallback,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: { display: true, text: '离线次数' }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        }

        function createDurationDistributionChart() {
            const ctx = document.getElementById('durationDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['< 1小时', '1-5小时', '5-24小时', '1-5天', '> 5天'],
                    datasets: [{
                        label: '事件数量',
                        data: [785, 350, 205, 50, 11],
                        backgroundColor: [palette.green, palette.blue, palette.purple, palette.pink, palette.orange],
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    ...tooltipTitleCallback,
                    scales: {
                        y: {
                            beginAtZero: true,
                            type: 'logarithmic',
                            title: { display: true, text: '事件数量 (对数)' }
                        },
                        x: {
                            title: { display: true, text: '离线时长区间' }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        }

        function createDurationDistanceScatterChart() {
            const ctx = document.getElementById('durationDistanceScatterChart').getContext('2d');
            let synthesizedData = [];
            for(let i=0; i<300; i++) {
                synthesizedData.push({
                    x: Math.random() * 5,
                    y: Math.random() * 30
                });
            }
            for(let i=0; i<50; i++) {
                synthesizedData.push({
                    x: 1 + Math.random() * 20,
                    y: 5 + Math.random() * 150
                });
            }
            synthesizedData.push({ x: 420, y: 15 });
            synthesizedData.push({ x: 350, y: 10 });
            synthesizedData.push({ x: 2, y: 93 });
            synthesizedData.push({ x: 23, y: 103 });
            const outlier = { x: 0.85, y: 1034.59 };

            new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '离线事件',
                        data: synthesizedData,
                        backgroundColor: palette.lightBlue,
                    }, {
                        label: '严重异常点',
                        data: [outlier],
                        backgroundColor: 'rgba(255, 26, 104, 0.8)',
                        pointRadius: 8,
                        pointHoverRadius: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            title: { display: true, text: '离线时长 (小时)' }
                        },
                        y: {
                            title: { display: true, text: '离线距离 (公里)' }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    label += `(${context.parsed.x.toFixed(2)} 小时, ${context.parsed.y.toFixed(2)} 公里)`;
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }

        createDailyTrendChart();
        createCityDistributionChart();
        createHighFrequencyVehiclesChart();
        createDurationDistributionChart();
        createDurationDistanceScatterChart();
    });
</script>
</body>
</html>
