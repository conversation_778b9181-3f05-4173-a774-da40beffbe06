from yyutils import *
from utils.config import bpath
import month_for_exception_report_sheet1 as s1
import month_for_exception_report_sheet2 as s2
import month_for_exception_report_sheet3 as s3

template_file = 'templates/template_month_{year}年{month}月份疑似屏蔽信号需求.xlsx'


def fill_sheet1(elhander, s1_list, s1_se, year, month):
    sheet = elhander.activeWorksheet("各地市疑似屏蔽信号运行车辆总体情况")
    row_styles = elhander.copyRowStyle(sheet, 5)
    row_styles1 = elhander.copyRowStyle(sheet, 26)
    elhander.clearData(sheet, 5)
    sheet[2][0].value = sheet[2][0].value.format(year=year, month=month)
    elhander.fillData(sheet, s1_list, row_styles)
    elhander.fillData(sheet, [s1_se], row_styles1)


def fill_sheet2(elhander, s2_list, year, month):
    sheet = elhander.activeWorksheet("各地疑似屏蔽信号运行车辆占比最高的企业情况")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[2][0].value = sheet[2][0].value.format(year=year, month=month)
    elhander.fillData(sheet, s2_list, row_styles)


def fill_sheet3(elhander, s3_list, year, month):
    sheet = elhander.activeWorksheet("各地市第三方监控机构疑似屏蔽信号运行车辆占比情况")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[2][0].value = sheet[2][0].value.format(year=year, month=month)
    elhander.fillData(sheet, s3_list, row_styles)


def writeByTemplate(s1_list, s1_se, s2_list, s3_list,year, month):
    _, fn = get_filePath_and_fileName(template_file)
    fn = fn.format(year=year, month=month).strip('template_month_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, s1_list, s1_se,year, month)
    fill_sheet2(elhander, s2_list, year, month)
    fill_sheet3(elhander,s3_list, year, month)
    elhander.save()


def main(start_day, end_day, days):
    s1_list,s1_se = s1.load_data(start_day, end_day, days)
    s2_list = s2.load_data(start_day, end_day, days)
    s3_list = s3.load_data(start_day, end_day, days)
    year = start_day.split('-')[0]
    month = start_day.split('-')[1]
    writeByTemplate(s1_list,s1_se, s2_list,s3_list, year, month)



if __name__ == '__main__':
    start, end, days = TimeUtils.getLastMonthRange()
    main(start, end, days)