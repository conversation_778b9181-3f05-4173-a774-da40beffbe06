SELECT vri.vehicle_no ,vri.vehicle_color ,
       bin(brgia.alarm_status) as alarm_status ,
       bin(brgia.gnss_status) as gnss_status,
       brgia.latitude ,brgia.longitude ,brgia.height ,brgia.speed ,brgia.direction ,brgia.gps_time ,brgia.create_time ,brgia.source_protocol
from business_raw_gps_info_all brgia
         join vehicle_related_infos vri on brgia.sim_code = vri.master_sim_code
  where sim_code in(
      select master_sim_code from vehicle_related_infos vri2
                             where vri2.vehicle_type = '1'
                             and toString(area_id) like '4401%'
      )
  and toYYYYMM(gps_time) = '202505'
into outfile '4401-2025-05-01.csv.gz' format CSVWithNames;
