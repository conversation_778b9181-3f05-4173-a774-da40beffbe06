import argparse
from do_vehicle_wide_day_all_report import re_create_inc_day_report_from

parser = argparse.ArgumentParser(description='recreate vehicle wide table')
parser.add_argument('--date_from', help='beginning of time, example 2024-07-05')
parser.add_argument('--date_to', help='end of  time, example 2024-07-05')

args = parser.parse_args()

re_create_inc_day_report_from(args.date_from, args.date_to)
