from yyutils import read_csv, join_path,list_2_dictlist
import os

cityName_code = {}
adcode_cityInfo = {}
cityCode_cityInfo = {}


def load_city_config():
    jspath = os.path.split(os.path.realpath(__file__))[0]
    infile = join_path(jspath, 'city_codes.csv')
    inlist = read_csv(infile)
    for e in inlist:
        cityName_code.setdefault(e['city'], (e['adcode'], e['city_code']))
        adcode_cityInfo.setdefault(e['adcode'], (e['city'], e['city_code']))
        cityCode_cityInfo.setdefault(e['city_code'], []).append((e['city'], e['adcode']))

load_city_config()


def getCityByAdcode(adcode):
    return adcode_cityInfo.get(adcode)[0]


def getCityAdcodeByCityCode(city_code,city_piny):
    cityInfos = cityCode_cityInfo.get(city_code)
    if len(cityInfos) == 1:
        return cityInfos[0][0], cityInfos[0][1]
    return None, None


def getAdcodeCityCodeByCity(city):
    codeInfo = cityName_code.get(city)
    if codeInfo:
        return codeInfo[0], codeInfo[1]
    return None, None


if __name__ == '__main__':
    # print(getAdcodeCityCodeByCity('武汉市'))
    print(adcode_cityInfo.get('441200'))
