#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
from datetime import datetime, timedelta
from apscheduler.schedulers.blocking import BlockingScheduler
from main import main
import vehicle_week_report as vehicle_week_report
import vehicle_detail_report as vehicle_detail_report
import alarm_level_week_report as alarm_level_week_report
import alarm_week_report_v2 as alarm_week_report
import alarm_detail_report as alarm_detail_report
import system_owner_login_week_report as system_owner_login_week_report
import system_week_report as system_week_report
import need_acc_alarm_detail_report as need_acc_alarm_detail_report
import vehicle_alarm_day_stats_v1 as vehicle_alarm_day_stats


def cal_track_job():
    """
    计算轨迹数据job
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today()
    # 昨天
    yesterday = today - timedelta(days=1)
    day = yesterday.strftime("%y%m%d")
    main({'day': day, 'imei_file': None, 'thread_num': 8})
    print('ok! wait next...')


def report_job_sunday():
    """
    每周日报表
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today()
    # 一周之前
    begin_date = (today - timedelta(days=7)).strftime('%Y-%m-%d')
    begin_date_1day = (today - timedelta(days=1)).strftime('%Y-%m-%d')
    end_date = today.strftime('%Y-%m-%d')
    # 5大指标
    vehicle_week_report.export(begin_date, end_date)
    vehicle_detail_report.export(begin_date, end_date)
    # 风险等级
    alarm_level_week_report.export(begin_date, end_date)
    # 合格率(明细只能查一天)
    alarm_week_report.export(begin_date, end_date)
    alarm_detail_report.export(begin_date_1day, end_date)
    # 系统监控
    system_owner_login_week_report.export(begin_date, end_date)
    # # 压缩打包
    # os.system(
    #     'zip -q report/report_{}_{}.zip *.csv'.format(begin_date.replace('-', '')[-4:],
    #                                                   end_date.replace('-', '')[-4:]))
    # os.system('rm -f *.csv')
    print('ok! wait next...')


def report_job_thursday():
    """
    每周四报表
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today()
    # 一周之前
    begin_date = (today - timedelta(days=7)).strftime('%Y-%m-%d')
    end_date = today.strftime('%Y-%m-%d')
    system_week_report.export(begin_date, end_date)
    # # 压缩打包
    # os.system(
    #     'zip -q report/system_report_{}_{}.zip *.csv'.format(begin_date.replace('-', '')[-4:],
    #                                                          end_date.replace('-', '')[-4:]))
    # os.system('rm -f *.csv')
    print('ok! wait next...')


def report_job_everyday():
    """
    每天报表
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today()
    # 2天之前
    begin_date = (today - timedelta(days=2)).strftime('%Y-%m-%d')
    end_date = (today - timedelta(days=1)).strftime('%Y-%m-%d')
    need_acc_alarm_detail_report.export(begin_date, end_date)
    vehicle_alarm_day_stats.stats(begin_date)
    # # 压缩打包
    # os.system(
    #     'zip -q report/nedd_acc_alarm_detail_{}_{}.zip *.csv'.format(begin_date.replace('-', '')[-4:],
    #                                                                  end_date.replace('-', '')[-4:]))
    # os.system('rm -f *.csv')
    print('ok! wait next...')


def zip_today_all_csv():
    """
    压缩打包当天所有报表
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today().strftime('%Y-%m-%d')
    # 压缩打包
    os.system(
        'zip -q report/{}.zip *.csv'.format(today.replace('-', '')))
    os.system('rm -f *.csv')
    print('ok! wait next...')


if __name__ == '__main__':
    """
    定点输出的csv全部放到当天的文件夹
    所有任务最好都在6点前要执行完
    每日凌晨6点将当天文件夹打包成zip
    """
    scheduler = BlockingScheduler()
    # 每天0点5分执行
    scheduler.add_job(cal_track_job, 'cron', hour=0, minute=5)
    # 每周日3点执行
    scheduler.add_job(report_job_sunday, 'cron', day_of_week=6, hour=3)
    # 每周四3点执行
    scheduler.add_job(report_job_thursday, 'cron', day_of_week=3, hour=3)
    # 每天3点30执行
    scheduler.add_job(report_job_everyday, 'cron', hour=3, minute=30)
    # 每天6点执行打包压缩
    scheduler.add_job(zip_today_all_csv, 'cron', hour=6)
    # 开始
    scheduler.start()

