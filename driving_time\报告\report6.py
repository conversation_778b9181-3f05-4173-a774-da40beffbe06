import pandas as pd
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
import matplotlib.pyplot as plt
import os
from datetime import datetime

# --- 1. CONFIGURATION ---
CSV_FILENAME = 'X:\\track_report\driving_time\报告\\5月客运车辆离线漂移明细(已过滤).csv'
OUTPUT_FILENAME = 'final_word_report.docx'
CHARTS_DIR = 'charts' # Directory to save chart images

# --- Matplotlib Configuration for Chinese characters ---
plt.rcParams['font.sans-serif'] = ['SimHei']  # Use a font that supports Chinese
plt.rcParams['axes.unicode_minus'] = False  # Display the minus sign correctly

# --- 2. DATA PROCESSING & ANALYSIS ---

def parse_duration_to_hours(duration_str):
    if pd.isna(duration_str) or not isinstance(duration_str, str):
        return 0
    total_hours = 0
    duration_str = duration_str.strip()
    try:
        if '天' in duration_str:
            parts = duration_str.split('天')
            total_hours += int(parts[0]) * 24
            time_str = parts[1].strip()
            if '小时' in time_str:
                h_parts = time_str.split('小时')
                total_hours += int(h_parts[0])
                time_str = h_parts[1]
            if '分' in time_str:
                m_parts = time_str.split('分')
                total_hours += int(m_parts[0]) / 60
        elif ':' in duration_str:
            parts = [int(p) for p in duration_str.split(':')]
            if len(parts) == 3:
                total_hours = parts[0] + parts[1]/60 + parts[2]/3600
    except (ValueError, IndexError):
        return 0
    return total_hours

def analyze_data(df):
    analysis_results = {}
    df['离线距离_km'] = pd.to_numeric(df['离线距离'], errors='coerce').fillna(0)
    df['离线小时_hours'] = df['离线小时'].apply(parse_duration_to_hours)

    analysis_results['total_events'] = len(df)

    # Extreme Values
    analysis_results['sorted_by_distance'] = df.sort_values(by='离线距离_km', ascending=False).head(5)
    analysis_results['sorted_by_duration'] = df.sort_values(by='离线小时_hours', ascending=False).head(5)

    # Top 5s
    analysis_results['top_vehicles'] = df['车牌号'].value_counts().nlargest(5)
    analysis_results['top_owners'] = df['业户'].value_counts().nlargest(5)
    analysis_results['top_platforms'] = df['第三方'].value_counts().nlargest(5)

    # Categories for charts
    analysis_results['duration_df'] = df
    analysis_results['distance_df'] = df

    return analysis_results

# --- 3. CHART GENERATION ---

def create_charts(data):
    """Create and save all charts as images."""
    if not os.path.exists(CHARTS_DIR):
        os.makedirs(CHARTS_DIR)

    palette = plt.cm.get_cmap('viridis', 7)

    # Duration Chart (Pie)
    duration_labels = ['<1小时', '1-6小时', '6-12小时', '12-24小时', '1-3天', '3-7天', '>7天']
    duration_bins = [-1, 1, 6, 12, 24, 72, 168, float('inf')]
    duration_counts = pd.cut(data['duration_df']['离线小时_hours'], bins=duration_bins, labels=duration_labels).value_counts().reindex(duration_labels)
    fig1, ax1 = plt.subplots(figsize=(8, 6))
    ax1.pie(duration_counts, labels=duration_counts.index, autopct='%1.1f%%', startangle=90, colors=palette.colors)
    ax1.axis('equal')
    plt.title('离线小时分布', fontsize=16)
    fig1.savefig(os.path.join(CHARTS_DIR, 'duration_chart.png'), bbox_inches='tight')
    plt.close(fig1)

    # Distance Chart (Bar)
    distance_labels = ['0-10公里', '10-50公里', '50-200公里', '>200公里']
    distance_bins = [-1, 10, 50, 200, float('inf')]
    distance_counts = pd.cut(data['distance_df']['离线距离_km'], bins=distance_bins, labels=distance_labels).value_counts().reindex(distance_labels)
    fig2, ax2 = plt.subplots(figsize=(8, 6))
    distance_counts.plot(kind='bar', ax=ax2, color=palette.colors)
    ax2.set_ylabel('事件数量 (条)')
    ax2.tick_params(axis='x', rotation=0)
    plt.title('离线位移距离分布', fontsize=16)
    fig2.savefig(os.path.join(CHARTS_DIR, 'distance_chart.png'), bbox_inches='tight')
    plt.close(fig2)

    # Top N charts (Horizontal Bar)
    def create_top_chart(series, title, filename):
        fig, ax = plt.subplots(figsize=(8, 5))
        series.sort_values().plot(kind='barh', ax=ax, color=palette.colors)
        ax.set_xlabel('事件数量 (次)')
        plt.title(title, fontsize=16)
        plt.tight_layout()
        fig.savefig(os.path.join(CHARTS_DIR, filename), bbox_inches='tight')
        plt.close(fig)

    create_top_chart(data['top_vehicles'], '高频离线位移车辆 TOP 5', 'frequency_chart.png')
    create_top_chart(data['top_owners'], '高发离线位移业户 TOP 5', 'owner_chart.png')
    create_top_chart(data['top_platforms'], '事件集中的第三方平台 TOP 5', 'platform_chart.png')

# --- 4. WORD DOCUMENT CREATION ---

def set_font_style(run, size=11, bold=False, color_rgb=None):
    """Helper to set font style."""
    font = run.font
    font.name = '等线'
    font.size = Pt(size)
    font.bold = bold
    if color_rgb:
        font.color.rgb = RGBColor(*color_rgb)

def add_section_header(doc, text, color_rgb=(255, 118, 74)):
    """Adds a formatted section header."""
    p = doc.add_paragraph()
    p.paragraph_format.space_before = Pt(18)
    p.paragraph_format.space_after = Pt(6)
    run = p.add_run(text)
    set_font_style(run, size=16, bold=True, color_rgb=RGBColor.from_string('4A90E2')) # Blueish color

def add_table_from_df(doc, df, columns):
    """Adds a formatted table from a pandas DataFrame."""
    table = doc.add_table(rows=1, cols=len(columns))
    table.style = 'Table Grid'
    hdr_cells = table.rows[0].cells
    for i, col_name in enumerate(columns):
        hdr_cells[i].text = col_name
        set_font_style(hdr_cells[i].paragraphs[0].runs[0], bold=True)
    for _, row in df.iterrows():
        row_cells = table.add_row().cells
        row_cells[0].text = str(row['车牌号'])
        row_cells[1].text = str(row['离线小时'])
        row_cells[2].text = f"{row['离线距离_km']:.2f}"
    return table

def create_word_report(data):
    """Generates the final Word document."""
    doc = Document()

    # Title
    title = doc.add_paragraph('广东省重点监管车辆离线位移情况', style='Title')
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Subtitle
    subtitle = doc.add_paragraph('可视化分析报告 (由Python生成)', style='Subtitle')
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph(f"本次共计分析了 {data['total_events']} 条有效离线位移事件记录。").alignment = WD_ALIGN_PARAGRAPH.CENTER

    # --- Sections ---
    add_section_header(doc, '一、总体态势分析')
    doc.add_picture(os.path.join(CHARTS_DIR, 'duration_chart.png'), width=Inches(3.0))
    doc.add_picture(os.path.join(CHARTS_DIR, 'distance_chart.png'), width=Inches(3.0))

    add_section_header(doc, '二、核心关注点：超长距离离线位移')
    doc.add_paragraph(f"单次离线最大位移记录: {data['sorted_by_distance'].iloc[0]['离线距离_km']:.2f} 公里")
    add_table_from_df(doc, data['sorted_by_distance'], ['车牌号', '离线小时', '位移距离(公里)'])

    add_section_header(doc, '三、核心关注点：长时间离线伴随位移')
    max_duration_days = data['sorted_by_duration'].iloc[0]['离线小时_hours'] / 24
    doc.add_paragraph(f"单次离线最长持续时间: {max_duration_days:.2f} 天")
    add_table_from_df(doc, data['sorted_by_duration'], ['车牌号', '离线小时', '位移距离(公里)'])

    add_section_header(doc, '四、核心关注点：高频与高发单位')
    doc.add_paragraph('离线位移事件高频车辆 TOP 5:')
    doc.add_picture(os.path.join(CHARTS_DIR, 'frequency_chart.png'), width=Inches(6.0))
    doc.add_paragraph('离线位移事件高发业户 TOP 5:')
    doc.add_picture(os.path.join(CHARTS_DIR, 'owner_chart.png'), width=Inches(6.0))
    doc.add_paragraph('事件集中的第三方平台 TOP 5:')
    doc.add_picture(os.path.join(CHARTS_DIR, 'platform_chart.png'), width=Inches(6.0))

    add_section_header(doc, '五、结论与系统性管理建议')
    doc.add_paragraph("基于数据洞察，建议构建包含技术升级、管理规程、监管执法和长效机制的“四位一体”综合治理体系，从根本上解决问题。")

    # Footer
    footer = doc.sections[0].footer
    p = footer.paragraphs[0]
    p.text = f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\t\t数据来源: {CSV_FILENAME}"
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER

    doc.save(OUTPUT_FILENAME)

# --- 5. MAIN EXECUTION ---
if __name__ == '__main__':
    print("开始生成Word版可视化分析报告...")
    if not os.path.exists(CSV_FILENAME):
        print(f"错误: 数据文件 '{CSV_FILENAME}' 未找到。")
    else:
        try:
            print("1. 正在加载数据...")
            try:
                main_df = pd.read_csv(CSV_FILENAME, encoding='utf-8')
            except UnicodeDecodeError:
                main_df = pd.read_csv(CSV_FILENAME, encoding='gbk')

            print("2. 正在分析数据...")
            analysis_data = analyze_data(main_df)

            print("3. 正在生成图表...")
            create_charts(analysis_data)

            print("4. 正在生成Word报告文档...")
            create_word_report(analysis_data)

            print(f"✓ 报告生成成功！请在当前目录下查找文件: '{os.path.abspath(OUTPUT_FILENAME)}'")
        except Exception as e:
            print(f"报告生成过程中发生严重错误: {e}")

