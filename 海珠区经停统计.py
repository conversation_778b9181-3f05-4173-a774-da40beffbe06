from yyutils import *
from utils.config import orderByCity
from collections import OrderedDict

bpath = r'/data/track_report'
template_file = 'templates/template_daily_{start_time}6点至{end_time}6点各地市途经海珠区客运车辆风险跟踪统计表.xlsx'

def getDataList(start_day,end_day):
    sql = f'''
select aaa.vehicle_no                                            as `车牌号`,
       aaa.vehicle_color_name                                    as `车牌颜色`,
       aaa.owner_name                                            as `业户名称`,
       dictGetString('gdispx_data.sys_dict_item','label',(toString(ccc.vehicle_type),'vehicle_type')) vehicle_type_name,
       aaa.vehicle_city_name                                     as `city_name`,
       concat(aaa.vehicle_city_name, ',', aaa.vehicle_area_name) as `车辆辖区`,
       aaa.access_time                                           as `第一次进入时间`,
       aaa.stay_time                                             as `总停留时间(分钟)`,
       aaa.third_party_name                                      as `第三方机构名称`,
       bbb.`当天风险总数(宗)`                                    as `当天风险总数(宗)`,
       bbb.`接打手机报警(宗)`                                    as `接打手机报警(宗)`,
       bbb.`玩手机报警(宗)`                                      as `玩手机报警(宗)`,
       bbb.`抽烟报警(宗)`                                        as `抽烟报警(宗)`,
       bbb.`超速报警(宗)`                                        as `超速报警(宗)`,
       bbb.`驾驶超时报警(宗)`                                    as `驾驶超时报警(宗)`,
       bbb.`凌晨2点到5点运行报警(宗)`                            as `凌晨2点到5点运行报警(宗)`,
       bbb.`双手脱把报警(宗)`                                    as `双手脱把报警(宗)`,
       bbb.`设备遮挡失效报警(宗)`                                as `设备遮挡失效报警(宗)`,
       bbb.`未系安全带报警(宗)`                                  as `未系安全带报警(宗)`

from (SELECT vehicle_id,
             vehicle_no,
             vehicle_color_name,
             min(gps_time_min)        as access_time,
             COUNT(gps_time_min) * 10 as stay_time,
             city_name,
             area_name,
             vehicle_city_name,
             vehicle_area_name,
             any(owner_name)          as owner_name,
             any(third_party_name)    as third_party_name
      from remote('172.16.167.58', 'gdispx_fence', business_last_gps_info_10_min, 'default', '<EMAIL>')
      where gps_time BETWEEN '{start_day} 06:00:00' and '{end_day} 06:00:00'
        and (vehicle_id) in (SELECT DISTINCT vehicle_id
                             from remote('172.16.167.58', 'gdispx_fence', 'business_last_gps_info_10_min', 'default',
                                         '<EMAIL>')
                             where gps_time BETWEEN '{start_day} 06:00:00' and '{end_day} 06:00:00'
                               and area_name = '海珠区'
                               and vehicle_type = '1')
        and area_name = '海珠区'
      group by vehicle_id, vehicle_no, vehicle_color_name, city_name, area_name, vehicle_city_name, vehicle_area_name
      order by vehicle_no, vehicle_color_name
         ) as aaa
         left join
     (
         SELECT vehicle_id,
                sum(alarmNum)  AS `当天风险总数(宗)`,
                sum(type10401) AS `接打手机报警(宗)`,
                sum(type10413) AS `玩手机报警(宗)`,
                sum(type10402) AS `抽烟报警(宗)`,
                sum(type90001) AS `超速报警(宗)`,
                sum(type90002) AS `驾驶超时报警(宗)`,
                sum(type90007) AS `凌晨2点到5点运行报警(宗)`,
                sum(type10412) AS `双手脱把报警(宗)`,
                sum(type10405) AS `设备遮挡失效报警(宗)`,
                sum(type10410) AS `未系安全带报警(宗)`
         FROM gdispx_data.risk_statistics_hour_all
         where alarm_time BETWEEN '{start_day} 06:00:00' and '{end_day} 06:00:00'
           and toInt32(vehicle_id) in (SELECT DISTINCT vehicle_id
                                       from remote('172.16.167.58', 'gdispx_fence', 'business_last_gps_info_10_min',
                                                   'default', '<EMAIL>')
                                       where gps_time BETWEEN '{start_day} 06:00:00' and '{end_day} 06:00:00'
                                         and area_name = '海珠区'
                                         and vehicle_type = '1')
         GROUP by vehicle_id
         ) as bbb
     on aaa.vehicle_id = toInt32(bbb.vehicle_id)
     left join gdispx_data.vehicle_related_infos_all ccc on aaa.vehicle_id = toInt32(ccc.vehicle_id)
    '''
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(sql)
    return datalist


def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        ne = OrderedDict(zip(
            ('no','车牌号','车牌颜色','业户名称','vehicle_type_name','车辆辖区',
             '第一次进入时间','总停留时间(分钟)','第三方机构名称',
             '当天风险总数(宗)','接打手机报警(宗)','玩手机报警(宗)',
             '抽烟报警(宗)','超速报警(宗)','驾驶超时报警(宗)',
             '凌晨2点到5点运行报警(宗)','双手脱把报警(宗)',
             '设备遮挡失效报警(宗)','未系安全带报警(宗)','remark'),
            (
                e['pid'],e['车牌号'],e['车牌颜色'],e['业户名称'],e['vehicle_type_name'],e['车辆辖区'],
                e['第一次进入时间'],e['总停留时间(分钟)'],e['第三方机构名称'],
                e['当天风险总数(宗)'],e['接打手机报警(宗)'],e['玩手机报警(宗)'],
                e['抽烟报警(宗)'], e['超速报警(宗)'], e['驾驶超时报警(宗)'],
                e['凌晨2点到5点运行报警(宗)'], e['双手脱把报警(宗)'],
                e['设备遮挡失效报警(宗)'], e['未系安全带报警(宗)'], ''
            )
        ))
        outlist.append(ne)
    return outlist


def fill_sheet1(elhander, inlist, s1, s2):
    sheet = elhander.activeWorksheet("区域查车")
    row_styles = elhander.copyRowStyle(sheet, 3)
    elhander.clearData(sheet, 3)
    sheet[1][0].value = sheet[1][0].value.format(start_day=s1, end_day=s2)
    elhander.fillData(sheet, inlist, row_styles)


def writeByTemplate(start_day, end_day, outlist):
    s1 = start_day[:4]+'年' + start_day[5:7]+'月'+start_day[8:]+'日'
    s2 = end_day[:4]+'年' + end_day[5:7]+'月'+end_day[8:]+'日'
    _, fn = get_filePath_and_fileName(template_file)
    fn = fn.format(start_time=s1, end_time=s2).strip('template_daily_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, outlist, s1, s2)
    elhander.save()
    
    

def main(end_day):
    start_day = TimeUtils.DeltaDay(-1,cur_day=end_day)
    datalist = getDataList(start_day,end_day)
    outlist = orderByCity(datalist)
    outlist = export_outlist_orderDict(outlist)
    writeByTemplate(start_day,end_day,outlist)



if __name__ == '__main__':
    inc_day = TimeUtils.Today()
    main(inc_day)