from yyutils import *
from datetime import datetime, timedelta

bpath = r'/data/report_data/inquire'

def from_A_to_B(start, end):
    start_day = datetime.strptime(start, '%Y-%m-%d')
    if not end:
        end = start
    end_day = datetime.strptime(end, '%Y-%m-%d')
    n = 0
    while True:
        one = start_day + timedelta(days=n)
        if one <= end_day:
            n += 1
            yield str(one.strftime('%Y-%m-%d'))
        else:
            break

def loadData(inc_day):
    inquire_station_sql = f'''
        select 
            id, area_id, owner_id, third_party_id, station_type, status, 
            inquire_user, answer_user, inquire_question_id, answer_content, 
            inquire_time, answer_time, serial_num, inquire_type, answer_span, 
            inquire_batch
        from gdispx_basics.business_inquire_station bis 
        where inquire_time between '{inc_day} 00:00:00' and '{inc_day} 23:59:59'
    '''
    inquire_batch_sql = f'''
        select 
            id, owner_id, third_party_id, third_party_id_owner_id, 
            vehicle_type, status, inquire_batch, create_time
        from gdispx_basics.business_inquire_batch bis 
        where create_time between '{inc_day} 00:00:00' and '{inc_day} 23:59:59'
    '''
    third_sql = f'''
        select 
        btp.id,btp.name cname,btp.social_credit_code ,
        if(ba.id=ba.city_id,'',ba.id) area_id,
        if(ba.id=ba.city_id,'',ba.short_name) area_name ,
        ba.city_id ,ba.city_short city_name
        from gdispx_basics.basics_third_party btp  
        left join gdispx_basics.basics_area ba 
        on btp.area_id = ba.id 
        where btp.is_delete = 0
    '''
    owner_sql = f'''
        select 
            bo.owner_id id,
            owner_name cname,
            unify_social_code social_credit_code,
            if(ba.id=ba.city_id,ba.id + 1,ba.id) area_id,
            if(ba.id=ba.city_id,concat(ba.name,'本级'),ba.short_name) area_name,
            ba.city_id ,ba.city_short city_name
        from gdispx_basics.basics_owner bo   
        left join gdispx_basics.basics_owner_area boa 
        on boa.owner_id = bo.owner_id 
        left join gdispx_basics.basics_area ba 
        on boa.area_id = ba.id 
        where bo.is_delete = 0
    '''
    db = DBUtil(DBType.MYSQL.value)
    stationList = db.query_to_dictList(inquire_station_sql)
    batchList = db.query_to_dictList(inquire_batch_sql)
    thirdList = db.query_to_dictList(third_sql)
    ownerList = db.query_to_dictList(owner_sql)
    db.close()
    return stationList, batchList, thirdList, ownerList


def do_stats_third_inquire_by_batch(station_g, batch_g):
    third_se = {}
    third_batch_g = list_2_dictlist(batch_g, 'third_party_id')
    station_g_third = list(filter(lambda o: o['third_party_id'], station_g))
    for e in station_g_third:
        id = e['third_party_id']
        is_answer = 1 if str(e['status']) == '1' else 0
        se = {
            'cid': id,
            'inquire_count': 1,
            'answer_inquire_count': is_answer,
            'vt1_inquire_count': 0,
            'vt1_answer_inquire_count': 0,
            'vt3_inquire_count': 0,
            'vt3_answer_inquire_count': 0,
            'vt4_inquire_count': 0,
            'vt4_answer_inquire_count': 0,

        }
        third_se.setdefault(id, se)
        cur_batch_g = third_batch_g.get(id)
        if cur_batch_g:
            vt1List = list(filter(lambda o: str(o['vehicle_type']) == '1', cur_batch_g))
            vt3List = list(filter(lambda o: str(o['vehicle_type']) == '3', cur_batch_g))
            vt4List = list(filter(lambda o: str(o['vehicle_type']) == '4', cur_batch_g))
            if vt1List:
                se['vt1_inquire_count'] = 1
                se['vt1_answer_inquire_count'] = is_answer
            if vt3List:
                se['vt3_inquire_count'] = 1
                se['vt3_answer_inquire_count'] = is_answer
            if vt4List:
                se['vt4_inquire_count'] = 1
                se['vt4_answer_inquire_count'] = is_answer
    return third_se


def do_stats_owner_inquire_by_batch(station_g, batch_g):
    owner_g = list_2_dictlist(batch_g, 'owner_id')
    thirdOwner_g = list_2_dictlist(batch_g, 'third_party_id_owner_id')
    owner_se1 = {}
    for owner_id, g in owner_g.items():
        is_answer = 1 if list(filter(lambda o: str(o['status']) == '1', g)) else 0
        se = {
            'cid': owner_id,
            'inquire_count': 1,
            'answer_inquire_count': is_answer,
            'vt1_inquire_count': 0,
            'vt1_answer_inquire_count': 0,
            'vt3_inquire_count': 0,
            'vt3_answer_inquire_count': 0,
            'vt4_inquire_count': 0,
            'vt4_answer_inquire_count': 0,
        }
        vt1List = list(filter(lambda o: str(o['vehicle_type']) == '1', g))
        vt3List = list(filter(lambda o: str(o['vehicle_type']) == '3', g))
        vt4List = list(filter(lambda o: str(o['vehicle_type']) == '4', g))
        if vt1List:
            se['vt1_inquire_count'] = 1
            se['vt1_answer_inquire_count'] = is_answer
        if vt3List:
            se['vt3_inquire_count'] = 1
            se['vt3_answer_inquire_count'] = is_answer
        if vt4List:
            se['vt4_inquire_count'] = 1
            se['vt4_answer_inquire_count'] = is_answer
        owner_se1.setdefault(owner_id, se)
    owner_se2 = {}
    for owner_id, g in thirdOwner_g.items():
        if owner_se2.get(owner_id):
            continue
        is_answer = 1 if list(filter(lambda o: str(o['status']) == '1', g)) else 0
        se = {
            'cid': owner_id,
            'inquire_count': 1,
            'answer_inquire_count': is_answer,
            'vt1_inquire_count': 0,
            'vt1_answer_inquire_count': 0,
            'vt3_inquire_count': 0,
            'vt3_answer_inquire_count': 0,
            'vt4_inquire_count': 0,
            'vt4_answer_inquire_count': 0,
        }
        vt1List = list(filter(lambda o: str(o['vehicle_type']) == '1', g))
        vt3List = list(filter(lambda o: str(o['vehicle_type']) == '3', g))
        vt4List = list(filter(lambda o: str(o['vehicle_type']) == '4', g))
        if vt1List:
            se['vt1_inquire_count'] = 1
            se['vt1_answer_inquire_count'] = is_answer
        if vt3List:
            se['vt3_inquire_count'] = 1
            se['vt3_answer_inquire_count'] = is_answer
        if vt4List:
            se['vt4_inquire_count'] = 1
            se['vt4_answer_inquire_count'] = is_answer
        owner_se2.setdefault(owner_id, se)
    owner_se = {}
    for owner_id, se1 in owner_se1.items():
        if not owner_se2.get(owner_id):
            owner_se.setdefault(owner_id, se1)
            continue
        se2 = owner_se2.get(owner_id)
        if se2['vt1_inquire_count'] == 1:
            if se1['vt1_inquire_count'] == 0 and se2['vt1_answer_inquire_count'] == 1:
                se1['vt1_answer_inquire_count'] = 1
            se1['vt1_inquire_count'] = 1

        if se2['vt3_inquire_count'] == 1:
            if se1['vt3_inquire_count'] == 0 and se2['vt3_answer_inquire_count'] == 1:
                se1['vt3_answer_inquire_count'] = 1
            se1['vt3_inquire_count'] = 1
        if se2['vt4_inquire_count'] == 1:
            if se1['vt4_inquire_count'] == 0 and se2['vt4_answer_inquire_count'] == 1:
                se1['vt4_answer_inquire_count'] = 1
            se1['vt4_inquire_count'] = 1
        if se1['answer_inquire_count'] == 1:
            if se2['vt1_inquire_count'] == 1:
                se1['vt1_inquire_count'] = 1
                se1['vt1_answer_inquire_count'] = 1
            if se2['vt3_inquire_count'] == 1:
                se1['vt3_inquire_count'] = 1
                se1['vt3_answer_inquire_count'] = 1
            if se2['vt4_inquire_count'] == 1:
                se1['vt4_inquire_count'] = 1
                se1['vt4_answer_inquire_count'] = 1
        owner_se.setdefault(owner_id, se1)
    for owner_id, se2 in owner_se2.items():
        if not owner_se1.get(owner_id):
            owner_se.setdefault(owner_id, se2)
    return owner_se


def do_stats_inquire_by_batch(station_g, batch_g):
    third_se = do_stats_third_inquire_by_batch(station_g, batch_g)
    owner_se = do_stats_owner_inquire_by_batch(station_g, batch_g)
    return third_se, owner_se


def doSum(third_se, owner_se, cur_third_se, cur_owner_se):
    for id, cur_se in cur_third_se.items():
        if not third_se.get(id):
            third_se.setdefault(id, cur_se)
            continue
        se = third_se.get(id)
        se['inquire_count'] += cur_se['inquire_count']
        se['answer_inquire_count'] += cur_se['answer_inquire_count']
        se['vt1_inquire_count'] += cur_se['vt1_inquire_count']
        se['vt1_answer_inquire_count'] += cur_se['vt1_answer_inquire_count']
        se['vt3_inquire_count'] += cur_se['vt3_inquire_count']
        se['vt3_answer_inquire_count'] += cur_se['vt3_answer_inquire_count']
        se['vt4_inquire_count'] += cur_se['vt4_inquire_count']
        se['vt4_answer_inquire_count'] += cur_se['vt4_answer_inquire_count']
    for id, cur_se in cur_owner_se.items():
        if not owner_se.get(id):
            owner_se.setdefault(id, cur_se)
            continue
        se = owner_se.get(id)
        se['inquire_count'] += cur_se['inquire_count']
        se['answer_inquire_count'] += cur_se['answer_inquire_count']
        se['vt1_inquire_count'] += cur_se['vt1_inquire_count']
        se['vt1_answer_inquire_count'] += cur_se['vt1_answer_inquire_count']
        se['vt3_inquire_count'] += cur_se['vt3_inquire_count']
        se['vt3_answer_inquire_count'] += cur_se['vt3_answer_inquire_count']
        se['vt4_inquire_count'] += cur_se['vt4_inquire_count']
        se['vt4_answer_inquire_count'] += cur_se['vt4_answer_inquire_count']


def packageData(third_se, owner_se, thirdList, ownerList, inc_day):
    outlist = []
    for e in thirdList:
        se = third_se.get(e['id'])
        if not se:
            ne = {
                'inc_day': inc_day,
                'cid': e['id'],
                'tenant_id': 6,
                'cname': e['cname'],
                'social_credit_code': e['social_credit_code'],
                'area_id': e['area_id'],
                'area_name': e['area_name'],
                'city_id': e['city_id'],
                'city_name': e['city_name'],
                'inquire_count': 0,
                'answer_inquire_count': 0,
                'vt1_inquire_count': 0,
                'vt1_answer_inquire_count': 0,
                'vt3_inquire_count': 0,
                'vt3_answer_inquire_count': 0,
                'vt4_inquire_count': 0,
                'vt4_answer_inquire_count': 0,
            }
            # outlist.append(ne)
            continue
        se['cname'] = e['cname']
        se['tenant_id'] = 6
        se['social_credit_code'] = e['social_credit_code']
        se['area_id'] = e['area_id']
        se['area_name'] = e['area_name']
        se['city_id'] = e['city_id']
        se['city_name'] = e['city_name']
        se['inc_day'] = inc_day
        outlist.append(se)
    for e in ownerList:
        se = owner_se.get(e['id'])
        if not se:
            ne = {
                'inc_day': inc_day,
                'cid': e['id'],
                'tenant_id': 1,
                'cname': e['cname'],
                'social_credit_code': e['social_credit_code'],
                'area_id': e['area_id'],
                'area_name': e['area_name'],
                'city_id': e['city_id'],
                'city_name': e['city_name'],
                'inquire_count': 0,
                'answer_inquire_count': 0,
                'vt1_inquire_count': 0,
                'vt1_answer_inquire_count': 0,
                'vt3_inquire_count': 0,
                'vt3_answer_inquire_count': 0,
                'vt4_inquire_count': 0,
                'vt4_answer_inquire_count': 0,
            }
            # outlist.append(ne)
            continue
        se['cname'] = e['cname']
        se['tenant_id'] = 1
        se['social_credit_code'] = e['social_credit_code']
        se['area_id'] = e['area_id']
        se['area_name'] = e['area_name']
        se['city_id'] = e['city_id']
        se['city_name'] = e['city_name']
        se['inc_day'] = inc_day
        outlist.append(se)
    return outlist


def do_cal_report(stationList, batchList, thirdList, ownerList, inc_day):
    station_batch_g = list_2_dictlist(stationList, 'inquire_batch')
    batch_g = list_2_dictlist(batchList, 'inquire_batch')
    third_se, owner_se = {}, {}
    for bid, g1 in station_batch_g.items():
        if not batch_g.get(bid):
            continue
        g2 = batch_g.get(bid)
        cur_third_se, cur_owner_se = do_stats_inquire_by_batch(g1, g2)
        doSum(third_se, owner_se, cur_third_se, cur_owner_se)
    return packageData(third_se, owner_se, thirdList, ownerList, inc_day)


def save_to_db_by_inlist(db, g):
    insert_sql = f'''
                        INSERT into gdispx_data.inquire_report_day_all 
                        (`inc_day`,`cid`,`cname`,`social_credit_code`,`tenant_id`,
                        `city_id`,`city_name`, `area_id`, `area_name`, `inquire_count`,
                         `answer_inquire_count`,`vt1_inquire_count`, `vt1_answer_inquire_count`,
                         `vt3_inquire_count`,`vt3_answer_inquire_count`,
                         `vt4_inquire_count`,`vt4_answer_inquire_count`
                        ) values 
                    '''
    for e in g:
        e['inc_day'] = TimeUtils.strptime(e['inc_day'], DateFormat.Y_m_d.value)
        e['area_id'] = int(e['area_id']) if e['area_id'] else None
        e['city_id'] = int(e['city_id']) if e['city_id'] else None
        e['cname'] = e['cname'] if e['cname'] else ''
        e['social_credit_code'] = e['social_credit_code'] if e['social_credit_code'] else ''
        e['city_name'] = e['city_name'] if e['city_name'] else ''
        e['area_name'] = e['area_name'] if e['area_name'] else ''
    db.excute(insert_sql, g)


def save_to_db(inlist):
    db = DBUtil(DBType.CLICKHOUSE.value)
    for g in read_by_trunk(inlist, 1000):
        save_to_db_by_inlist(db, g)
    db.close()


def main(inc_day):
    Logger.instance("查岗日结", join_path(bpath, 'log.log'))
    Logger.instance().info(f"{inc_day} 开始执行...")
    stationList, batchList, thirdList, ownerList = loadData(inc_day)
    outlist = do_cal_report(stationList, batchList, thirdList, ownerList, inc_day)
    h = 'inc_day,cid,tenant_id,cname,social_credit_code,area_id,area_name,' \
        'city_id,city_name,inquire_count,answer_inquire_count,vt1_inquire_count,' \
        'vt1_answer_inquire_count,vt3_inquire_count,vt3_answer_inquire_count,' \
        'vt4_inquire_count,vt4_answer_inquire_count'.split(',')
    otfile = join_path(bpath, f'inquire_report_{inc_day}.csv')
    write_csv(otfile, h, outlist)
    save_to_db(outlist)
    Logger.instance().info(f"{inc_day} 执行完毕!")


if __name__ == '__main__':
    # inc_day = TimeUtils.DeltaDay(-1)
    for day in from_A_to_B('2023-10-18','2023-10-23'):
        main(day)
