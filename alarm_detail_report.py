#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

freq_alarm_detail_select = """
select
	vrvri.manger_short_name
    , splitByChar(',', vrvri.manger_short_name)[2] as `城市`
    , vrvri.vehicle_no
    , vrvri.vehicle_type
    , vrvri.access_time
    , vrvri.vehicle_color
    , vrvri.owner_name as `业户`
    , vrvri.facilitator_name as `服务商`
    , vrvri.facilitator_social_credit_code
    , vrvri.master_sim_code
    , vrvri.master_producter_name
    , transform(vrvri.service_result, ['1','0','-1'], ['营运','待营运','报停、过户、过户转出'], '其他') `营运状态`
    , case when notEmpty(vrvri.master_sim_code) and notEmpty(vrvri.AI_BOX_sim_code) then '分体机' else '一体机' end as `机型`
    , case when vrvri.master_producter_name like '非粤标设备%' then '非粤标' when empty(vrvri.master_producter_name) then '' else '粤标' end as `设备类型`
    , alarm.device_alarm_cnt as `设备报警数`
    , tr.total_mileage_sum AS `总里程`
from gdispx_data.v_regulator_vehicle_related_infos vrvri
"""

freq_alarm_query_sql = """
WITH (
   	select round(avg(alarm_cnt_100km))
	from (
		select toUInt64(round(device_alarm_cnt / mileage_100km)) alarm_cnt_100km
		from (
			select imei, sum(total_mileage) total_mileage_sum, sum(round(total_mileage/100,1)) mileage_100km
		 	from gdispx_data.track_report where `date` >= '{track_begin_date}' and `date` < '{track_end_date}'
		 	group by imei	
		    ) a 
		join (select device_id, count() device_alarm_cnt from gdispx_data.business_alarm_info
			where alarm_time >= toDate('{begin_date}') and alarm_time < toDate('{end_date}')
			group by device_id
			) b on a.imei = b.device_id
		where a.total_mileage_sum > 1 AND a.total_mileage_sum < 2400
   )) AS device_alarm_cnt_avg
"""
freq_alarm_query_sql += freq_alarm_detail_select + """
join gdispx_data.business_gpsdatas bg on bg.device_id = trim(vrvri.master_sim_code)
join (select device_id, count() device_alarm_cnt from gdispx_data.business_alarm_info
			where alarm_time >= toDate('{begin_date}') and alarm_time < toDate('{end_date}')
			group by device_id) alarm 
			on alarm.device_id = trim(vrvri.master_sim_code)
JOIN (SELECT imei, sum(total_mileage) total_mileage_sum, sum(round(total_mileage/100,1)) mileage_100km FROM gdispx_data.track_report 
           WHERE `date` >= '{track_begin_date}' and `date` < '{track_end_date}'
           group by imei
           having total_mileage_sum > 1 AND total_mileage_sum < 2400) tr on tr.imei = trim(vrvri.master_sim_code)
where vrvri.master_sim_code != ''
 and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
  and toUInt64(round(device_alarm_cnt / mileage_100km)) > device_alarm_cnt_avg
"""

attach_miss_select = """
select
    vrvri.manger_short_name
    , splitByChar(',', vrvri.manger_short_name)[2] as `城市`
    , vrvri.vehicle_no
    , vrvri.vehicle_type
    , vrvri.access_time
    , vrvri.vehicle_color
    , vrvri.owner_name as `业户`
    , vrvri.facilitator_name as `服务商`
    , vrvri.facilitator_social_credit_code
    , vrvri.master_sim_code
    , vrvri.master_producter_name
    , transform(vrvri.service_result, ['1','0','-1'], ['营运','待营运','报停、过户、过户转出'], '其他') `营运状态`
    , case when notEmpty(vrvri.master_sim_code) and notEmpty(vrvri.AI_BOX_sim_code) then '分体机' else '一体机' end as `机型`
    , case when vrvri.master_producter_name like '非粤标设备%' then '非粤标' when empty(vrvri.master_producter_name) then '' else '粤标' end as `设备类型`
from gdispx_data.v_regulator_vehicle_related_infos vrvri
"""

attach_miss_query_sql = attach_miss_select + """
join gdispx_data.business_gpsdatas bg on bg.device_id = trim(vrvri.master_sim_code)
join (select 
	     distinct a.device_id
	  from (select device_id, alarm_time, alarm_tag from gdispx_data.business_alarm_info 
			where alarm_time >= toDate('{begin_date}') and alarm_time < toDate('{end_date}')
	      	and alarm_code in (10400,10401,10402,10410,10413,10412)) a
	        left join (select alarm_tag,create_time from gdispx_data.business_accessories_info
				where create_time >= toDate('{begin_date}') and create_time < toDate('{end_date}')) b on a.alarm_tag = b.alarm_tag
	 		where b.alarm_tag = '' or (b.alarm_tag != '' and alarm_time < addDays(create_time,-1))
	 ) bai on bai.device_id = trim(vrvri.master_sim_code) 
where vrvri.master_sim_code != ''
 and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
"""


def query_then_write_csv(query_sql, client, output_file):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join(['"' + col[0] + '"' for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join(['"' + str(x) + '"' for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        file_suffix = '_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])

        with open('频繁报警车辆明细' + file_suffix, 'w') as output_file:
            query_then_write_csv(freq_alarm_query_sql.format(track_begin_date=begin_date.replace('-', '')[-6:],
                                                             track_end_date=end_date.replace('-', '')[-6:],
                                                             begin_date=begin_date,
                                                             end_date=end_date)
                                 , client, output_file)
       # with open('附件丢失车辆明细' + file_suffix, 'w') as output_file:
       #     query_then_write_csv(attach_miss_query_sql.format(begin_date=begin_date, end_date=end_date)
       #                          , client, output_file)
    finally:
        ck.disconnect()


if __name__ == '__main__':
    begin_date = '2021-09-08'
    end_date = '2021-09-09'
    export(begin_date, end_date)
