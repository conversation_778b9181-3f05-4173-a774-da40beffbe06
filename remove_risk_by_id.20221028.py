from yyutils import *


def loadSQL(where_conditions):
    target_risk_detail_sql = SQLHelper().table("gdispx_data.business_ai_alarm_info ") \
        .where(where_conditions).select("*")
    delete_risk_detail_sql = SQLHelper().table("gdispx_data.business_ai_alarm_info") \
        .where(where_conditions).ckDelete()
    risk_sql1 = SQLHelper().table("gdispx_data.business_ai_alarm_info").innerJoin() \
        .table("gdispx_data.basics_area") \
        .where(["business_ai_alarm_info.area_id = toUInt32(basics_area.id)"] + where_conditions) \
        .select('''
        vehicle_no,
		vehicle_color,
		vehicle_color_name,
		vehicle_id,
		vehicle_type,
		toStartOfHour(alarm_time)                        AS alarm_time,
		owner_id,
		owner_name,
		area_id,
		area_name,
		city_id,
		facilitator_id,
		facilitator_name,
		third_party_id,
		third_party_name,
		if(alarm_code = 10300, 1, 0)                     AS type10300,
		if(alarm_code = 10301, 1, 0)                     AS type10301,
		if(alarm_code = 10302, 1, 0)                     AS type10302,
		if(alarm_code = 10303, 1, 0)                     AS type10303,
		if(alarm_code = 10304, 1, 0)                     AS type10304,
		if(alarm_code = 10305, 1, 0)                     AS type10305,
		if(alarm_code = 10306, 1, 0)                     AS type10306,
		if(alarm_code = 10316, 1, 0)                     AS type10316,
		if(alarm_code = 10400, 1, 0)                     AS type10400,
		if(alarm_code = 10401, 1, 0)                     AS type10401,
		if(alarm_code = 10402, 1, 0)                     AS type10402,
		if(alarm_code = 10403, 1, 0)                     AS type10403,
		if(alarm_code = 10404, 1, 0)                     AS type10404,
		if(alarm_code = 10405, 1, 0)                     AS type10405,
		if(alarm_code = 10415, 1, 0)                     AS type10415,
		if(alarm_code = 10416, 1, 0)                     AS type10416,
		if(alarm_code = 10315, 1, 0)                     AS type10315,
		if(alarm_code = 10312, 1, 0)                     AS type10312,
		if(alarm_code = 10313, 1, 0)                     AS type10313,
		if(alarm_code = 10408, 1, 0)                     AS type10408,
		if(alarm_code = 10410, 1, 0)                     AS type10410,
		if(alarm_code = 10411, 1, 0)                     AS type10411,
		if(alarm_code = 10412, 1, 0)                     AS type10412,
		if(alarm_code = 10413, 1, 0)                     AS type10413,
		if(alarm_code = 10200, 1, 0)                     AS type10200,
		if(alarm_code = 10202, 1, 0)                     AS type10202,
		if(alarm_code = 10203, 1, 0)                     AS type10203,
		if(alarm_code = 20200, 1, 0)                     AS type20200,
		if(alarm_code = 10300 and alarm_level = 1, 1, 0) AS type0110300,
		if(alarm_code = 10301 and alarm_level = 1, 1, 0) AS type0110301,
		if(alarm_code = 10302 and alarm_level = 1, 1, 0) AS type0110302,
		if(alarm_code = 10303 and alarm_level = 1, 1, 0) AS type0110303,
		if(alarm_code = 10304 and alarm_level = 1, 1, 0) AS type0110304,
		if(alarm_code = 10305 and alarm_level = 1, 1, 0) AS type0110305,
		if(alarm_code = 10306 and alarm_level = 1, 1, 0) AS type0110306,
		if(alarm_code = 10316 and alarm_level = 1, 1, 0) AS type0110316,
		if(alarm_code = 10400 and alarm_level = 1, 1, 0) AS type0110400,
		if(alarm_code = 10401 and alarm_level = 1, 1, 0) AS type0110401,
		if(alarm_code = 10402 and alarm_level = 1, 1, 0) AS type0110402,
		if(alarm_code = 10403 and alarm_level = 1, 1, 0) AS type0110403,
		if(alarm_code = 10404 and alarm_level = 1, 1, 0) AS type0110404,
		if(alarm_code = 10405 and alarm_level = 1, 1, 0) AS type0110405,
		if(alarm_code = 10415 and alarm_level = 1, 1, 0) AS type0110415,
		if(alarm_code = 10416 and alarm_level = 1, 1, 0) AS type0110416,
		if(alarm_code = 10315 and alarm_level = 1, 1, 0) AS type0110315,
		if(alarm_code = 10312 and alarm_level = 1, 1, 0) AS type0110312,
		if(alarm_code = 10313 and alarm_level = 1, 1, 0) AS type0110313,
		if(alarm_code = 10408 and alarm_level = 1, 1, 0) AS type0110408,
		if(alarm_code = 10410 and alarm_level = 1, 1, 0) AS type0110410,
		if(alarm_code = 10411 and alarm_level = 1, 1, 0) AS type0110411,
		if(alarm_code = 10412 and alarm_level = 1, 1, 0) AS type0110412,
		if(alarm_code = 10413 and alarm_level = 1, 1, 0) AS type0110413,
		if(alarm_code = 10200 and alarm_level = 1, 1, 0) AS type0110200,
		if(alarm_code = 10202 and alarm_level = 1, 1, 0) AS type0110202,
		if(alarm_code = 10203 and alarm_level = 1, 1, 0) AS type0110203,
		if(alarm_code = 20200 and alarm_level = 1, 1, 0) AS type0120200,
		if(alarm_code = 10300 and alarm_level = 2, 1, 0) AS type0210300,
		if(alarm_code = 10301 and alarm_level = 2, 1, 0) AS type0210301,
		if(alarm_code = 10302 and alarm_level = 2, 1, 0) AS type0210302,
		if(alarm_code = 10303 and alarm_level = 2, 1, 0) AS type0210303,
		if(alarm_code = 10304 and alarm_level = 2, 1, 0) AS type0210304,
		if(alarm_code = 10305 and alarm_level = 2, 1, 0) AS type0210305,
		if(alarm_code = 10306 and alarm_level = 2, 1, 0) AS type0210306,
		if(alarm_code = 10316 and alarm_level = 2, 1, 0) AS type0210316,
		if(alarm_code = 10400 and alarm_level = 2, 1, 0) AS type0210400,
		if(alarm_code = 10401 and alarm_level = 2, 1, 0) AS type0210401,
		if(alarm_code = 10402 and alarm_level = 2, 1, 0) AS type0210402,
		if(alarm_code = 10403 and alarm_level = 2, 1, 0) AS type0210403,
		if(alarm_code = 10404 and alarm_level = 2, 1, 0) AS type0210404,
		if(alarm_code = 10405 and alarm_level = 2, 1, 0) AS type0210405,
		if(alarm_code = 10415 and alarm_level = 2, 1, 0) AS type0210415,
		if(alarm_code = 10416 and alarm_level = 2, 1, 0) AS type0210416,
		if(alarm_code = 10315 and alarm_level = 2, 1, 0) AS type0210315,
		if(alarm_code = 10312 and alarm_level = 2, 1, 0) AS type0210312,
		if(alarm_code = 10313 and alarm_level = 2, 1, 0) AS type0210313,
		if(alarm_code = 10408 and alarm_level = 2, 1, 0) AS type0210408,
		if(alarm_code = 10410 and alarm_level = 2, 1, 0) AS type0210410,
		if(alarm_code = 10411 and alarm_level = 2, 1, 0) AS type0210411,
		if(alarm_code = 10412 and alarm_level = 2, 1, 0) AS type0210412,
		if(alarm_code = 10413 and alarm_level = 2, 1, 0) AS type0210413,
		if(alarm_code = 10200 and alarm_level = 2, 1, 0) AS type0210200,
		if(alarm_code = 10202 and alarm_level = 2, 1, 0) AS type0210202,
		if(alarm_code = 10203 and alarm_level = 2, 1, 0) AS type0210203,
		if(alarm_code = 20200 and alarm_level = 2, 1, 0) AS type0220200,
		if(alarm_code = 10300 and alarm_level = 3, 1, 0) AS type0310300,
		if(alarm_code = 10301 and alarm_level = 3, 1, 0) AS type0310301,
		if(alarm_code = 10302 and alarm_level = 3, 1, 0) AS type0310302,
		if(alarm_code = 10303 and alarm_level = 3, 1, 0) AS type0310303,
		if(alarm_code = 10304 and alarm_level = 3, 1, 0) AS type0310304,
		if(alarm_code = 10305 and alarm_level = 3, 1, 0) AS type0310305,
		if(alarm_code = 10306 and alarm_level = 3, 1, 0) AS type0310306,
		if(alarm_code = 10316 and alarm_level = 3, 1, 0) AS type0310316,
		if(alarm_code = 10400 and alarm_level = 3, 1, 0) AS type0310400,
		if(alarm_code = 10401 and alarm_level = 3, 1, 0) AS type0310401,
		if(alarm_code = 10402 and alarm_level = 3, 1, 0) AS type0310402,
		if(alarm_code = 10403 and alarm_level = 3, 1, 0) AS type0310403,
		if(alarm_code = 10404 and alarm_level = 3, 1, 0) AS type0310404,
		if(alarm_code = 10405 and alarm_level = 3, 1, 0) AS type0310405,
		if(alarm_code = 10415 and alarm_level = 3, 1, 0) AS type0310415,
		if(alarm_code = 10416 and alarm_level = 3, 1, 0) AS type0310416,
		if(alarm_code = 10315 and alarm_level = 3, 1, 0) AS type0310315,
		if(alarm_code = 10312 and alarm_level = 3, 1, 0) AS type0310312,
		if(alarm_code = 10313 and alarm_level = 3, 1, 0) AS type0310313,
		if(alarm_code = 10408 and alarm_level = 3, 1, 0) AS type0310408,
		if(alarm_code = 10410 and alarm_level = 3, 1, 0) AS type0310410,
		if(alarm_code = 10411 and alarm_level = 3, 1, 0) AS type0310411,
		if(alarm_code = 10412 and alarm_level = 3, 1, 0) AS type0310412,
		if(alarm_code = 10413 and alarm_level = 3, 1, 0) AS type0310413,
		if(alarm_code = 10200 and alarm_level = 3, 1, 0) AS type0310200,
		if(alarm_code = 10202 and alarm_level = 3, 1, 0) AS type0310202,
		if(alarm_code = 10203 and alarm_level = 3, 1, 0) AS type0310203,
		if(alarm_code = 20200 and alarm_level = 3, 1, 0) AS type0320200,
		if(alarm_code = 90001, 1, 0)                     as type90001,
		if(alarm_code = 90002, 1, 0)                     as type90002,
		if(alarm_code = 90003, 1, 0)                     as type90003,
		if(alarm_code = 90004, 1, 0)                     as type90004,
		if(alarm_code = 90005, 1, 0)                     as type90005,
		if(alarm_code = 90006, 1, 0)                     as type90006,
		if(alarm_code = 90007, 1, 0)                     as type90007,
		if(alarm_code = 90001 and alarm_level = 1, 1, 0) as type0190001,
		if(alarm_code = 90002 and alarm_level = 1, 1, 0) as type0190002,
		if(alarm_code = 90003 and alarm_level = 1, 1, 0) as type0190003,
		if(alarm_code = 90004 and alarm_level = 1, 1, 0) as type0190004,
		if(alarm_code = 90005 and alarm_level = 1, 1, 0) as type0190005,
		if(alarm_code = 90006 and alarm_level = 1, 1, 0) as type0190006,
		if(alarm_code = 90007 and alarm_level = 1, 1, 0) as type0190007,
		if(alarm_code = 90001 and alarm_level = 2, 1, 0) as type0290001,
		if(alarm_code = 90002 and alarm_level = 2, 1, 0) as type0290002,
		if(alarm_code = 90003 and alarm_level = 2, 1, 0) as type0290003,
		if(alarm_code = 90004 and alarm_level = 2, 1, 0) as type0290004,
		if(alarm_code = 90005 and alarm_level = 2, 1, 0) as type0290005,
		if(alarm_code = 90006 and alarm_level = 2, 1, 0) as type0290006,
		if(alarm_code = 90007 and alarm_level = 2, 1, 0) as type0290007,
		if(alarm_code = 90001 and alarm_level = 3, 1, 0) as type0390001,
		if(alarm_code = 90002 and alarm_level = 3, 1, 0) as type0390002,
		if(alarm_code = 90003 and alarm_level = 3, 1, 0) as type0390003,
		if(alarm_code = 90004 and alarm_level = 3, 1, 0) as type0390004,
		if(alarm_code = 90005 and alarm_level = 3, 1, 0) as type0390005,
		if(alarm_code = 90006 and alarm_level = 3, 1, 0) as type0390006,
		if(alarm_code = 90007 and alarm_level = 3, 1, 0) as type0390007,
		if(alarm_level = 1, 1, 0)                        AS oneLevel,
		if(alarm_level = 2, 1, 0)                        AS twoLevel,
		if(alarm_level = 3, 1, 0)                        AS threeLevel,
		id,
		0                                                AS disposeNum,
		monitor_intervene
        ''')
    risk_sql2 = SQLHelper().table(risk_sql1).groupBy('''
        alarm_time,
		vehicle_no,
		vehicle_color,
		vehicle_color_name,
		vehicle_id,
		vehicle_type,
		owner_id,
		owner_name,
		area_id,
		area_name,
		city_id,
		facilitator_id,
		facilitator_name,
		third_party_id,
		third_party_name
        ''') \
        .select('''
        alarm_time,
		vehicle_no,
		vehicle_color,
		vehicle_color_name,
		vehicle_id,
		vehicle_type,
		owner_id,
		owner_name,
		area_id,
		area_name,
		city_id,
		facilitator_id,
		facilitator_name,
		third_party_id,
		third_party_name,
		negate(sum(type10300))         AS type10300,
		negate(sum(type10301))         AS type10301,
		negate(sum(type10302))         AS type10302,
		negate(sum(type10303))         AS type10303,
		negate(sum(type10304))         AS type10304,
		negate(sum(type10305))         AS type10305,
		negate(sum(type10306))         AS type10306,
		negate(sum(type10316))         AS type10316,
		negate(sum(type10400))         AS type10400,
		negate(sum(type10401))         AS type10401,
		negate(sum(type10402))         AS type10402,
		negate(sum(type10403))         AS type10403,
		negate(sum(type10404))         AS type10404,
		negate(sum(type10405))         AS type10405,
		negate(sum(type10415))         AS type10415,
		negate(sum(type10416))         AS type10416,
		negate(sum(type10315))         AS type10315,
		negate(sum(type10312))         AS type10312,
		negate(sum(type10313))         AS type10313,
		negate(sum(type10408))         AS type10408,
		negate(sum(type10410))         AS type10410,
		negate(sum(type10411))         AS type10411,
		negate(sum(type10412))         AS type10412,
		negate(sum(type10413))         AS type10413,
		negate(sum(type10200))         AS type10200,
		negate(sum(type10202))         AS type10202,
		negate(sum(type10203))         AS type10203,
		negate(sum(type20200))         AS type20200,
		negate(sum(type0110300))       AS type0110300,
		negate(sum(type0110301))       AS type0110301,
		negate(sum(type0110302))       AS type0110302,
		negate(sum(type0110303))       AS type0110303,
		negate(sum(type0110304))       AS type0110304,
		negate(sum(type0110305))       AS type0110305,
		negate(sum(type0110306))       AS type0110306,
		negate(sum(type0110316))       AS type0110316,
		negate(sum(type0110400))       AS type0110400,
		negate(sum(type0110401))       AS type0110401,
		negate(sum(type0110402))       AS type0110402,
		negate(sum(type0110403))       AS type0110403,
		negate(sum(type0110404))       AS type0110404,
		negate(sum(type0110405))       AS type0110405,
		negate(sum(type0110415))       AS type0110415,
		negate(sum(type0110416))       AS type0110416,
		negate(sum(type0110315))       AS type0110315,
		negate(sum(type0110312))       AS type0110312,
		negate(sum(type0110313))       AS type0110313,
		negate(sum(type0110408))       AS type0110408,
		negate(sum(type0110410))       AS type0110410,
		negate(sum(type0110411))       AS type0110411,
		negate(sum(type0110412))       AS type0110412,
		negate(sum(type0110413))       AS type0110413,
		negate(sum(type0110200))       AS type0110200,
		negate(sum(type0110202))       AS type0110202,
		negate(sum(type0110203))       AS type0110203,
		negate(sum(type0120200))       AS type0120200,
		negate(sum(type0210300))       AS type0210300,
		negate(sum(type0210301))       AS type0210301,
		negate(sum(type0210302))       AS type0210302,
		negate(sum(type0210303))       AS type0210303,
		negate(sum(type0210304))       AS type0210304,
		negate(sum(type0210305))       AS type0210305,
		negate(sum(type0210306))       AS type0210306,
		negate(sum(type0210316))       AS type0210316,
		negate(sum(type0210400))       AS type0210400,
		negate(sum(type0210401))       AS type0210401,
		negate(sum(type0210402))       AS type0210402,
		negate(sum(type0210403))       AS type0210403,
		negate(sum(type0210404))       AS type0210404,
		negate(sum(type0210405))       AS type0210405,
		negate(sum(type0210415))       AS type0210415,
		negate(sum(type0210416))       AS type0210416,
		negate(sum(type0210315))       AS type0210315,
		negate(sum(type0210312))       AS type0210312,
		negate(sum(type0210313))       AS type0210313,
		negate(sum(type0210408))       AS type0210408,
		negate(sum(type0210410))       AS type0210410,
		negate(sum(type0210411))       AS type0210411,
		negate(sum(type0210412))       AS type0210412,
		negate(sum(type0210413))       AS type0210413,
		negate(sum(type0210200))       AS type0210200,
		negate(sum(type0210202))       AS type0210202,
		negate(sum(type0210203))       AS type0210203,
		negate(sum(type0220200))       AS type0220200,
		negate(sum(type0310300))       AS type0310300,
		negate(sum(type0310301))       AS type0310301,
		negate(sum(type0310302))       AS type0310302,
		negate(sum(type0310303))       AS type0310303,
		negate(sum(type0310304))       AS type0310304,
		negate(sum(type0310305))       AS type0310305,
		negate(sum(type0310306))       AS type0310306,
		negate(sum(type0310316))       AS type0310316,
		negate(sum(type0310400))       AS type0310400,
		negate( sum(type0310401))       AS type0310401,
		negate( sum(type0310402))       AS type0310402,
		negate( sum(type0310403))       AS type0310403,
		negate(sum(type0310404))       AS type0310404,
		negate(sum(type0310405))       AS type0310405,
		negate(sum(type0310415))       AS type0310415,
		negate(sum(type0310416))       AS type0310416,
		negate(sum(type0310315))       AS type0310315,
		negate( sum(type0310312))       AS type0310312,
		negate( sum(type0310313))       AS type0310313,
		negate( sum(type0310408))       AS type0310408,
		negate( sum(type0310410))       AS type0310410,
		negate( sum(type0310411))      AS type0310411,
		negate( sum(type0310412))       AS type0310412,
		negate( sum(type0310413))       AS type0310413,
		negate( sum(type0310200))       AS type0310200,
		negate( sum(type0310202))       AS type0310202,
		negate( sum(type0310203))       AS type0310203,
		negate( sum(type0320200))       AS type0320200,
		negate(sum(type90001))         as type90001,
		negate(sum(type90002))         as type90002,
		negate(sum(type90003))         as type90003,
		negate(sum(type90004))         as type90004,
		negate(sum(type90005))         as type90005,
		negate(sum(type90006))         as type90006,
		negate(sum(type90007))         as type90007,
		negate(sum(type0190001))       as type0190001,
		negate(sum(type0190002))       as type0190002,
		negate(sum(type0190003))       as type0190003,
		negate(sum(type0190004))       as type0190004,
		negate(sum(type0190005))       as type0190005,
		negate(sum(type0190006))       as type0190006,
		negate(sum(type0190007))       as type0190007,
		negate(sum(type0290001))       as type0290001,
		negate(sum(type0290002))       as type0290002,
		negate(sum(type0290003))       as type0290003,
		negate(sum(type0290004))       as type0290004,
		negate(sum(type0290005))       as type0290005,
		negate(sum(type0290006))       as type0290006,
		negate(sum(type0290007))       as type0290007,
		negate(sum(type0390001))       as type0390001,
		negate(sum(type0390002))       as type0390002,
		negate(sum(type0390003))       as type0390003,
		negate(sum(type0390004))       as type0390004,
		negate(sum(type0390005))       as type0390005,
		negate(sum(type0390006))       as type0390006,
		negate(sum(type0390007))       as type0390007,
		negate( sum(oneLevel)  )        AS oneLevel,
		negate(sum(twoLevel)  )        AS twoLevel,
		negate( sum(threeLevel))        AS threeLevel,
		negate( count(id)    )          AS alarmNum,
		negate(sum(disposeNum))        AS disposeNum,
		0 AS monitorIntervene,
		'' as alarmId
        ''')
    insert_hour_sql = '''
        insert into gdispx_data.risk_statistics_hour_all
    ''' + risk_sql2
    return target_risk_detail_sql, risk_sql2, delete_risk_detail_sql, insert_hour_sql


def main(where_conditions):
    otpath = createOutpath(bpath, 'cases', f'风险剔除_{TimeUtils.Today()}')
    target_risk_detail_sql, risk_sql2, delete_risk_detail_sql, insert_hour_sql = loadSQL(where_conditions)
    db = DBUtil(DBType.CLICKHOUSE.value)
    db.query_to_csv(target_risk_detail_sql, join_path(otpath, 'risk_detail-103-107.csv'))
    db.query_to_csv(risk_sql2, join_path(otpath, 'risk_hour_to_insert.csv'))
    db.excute(insert_hour_sql)
    print(delete_risk_detail_sql)
    db.excute(delete_risk_detail_sql)
    db.close()


class AlarmType:
    over_speed = 90001
    over_time = 90002
    _2_5_runnint = 90007
    smoking = 10402


if __name__ == '__main__':
    bpath = r'/data/report_data'
    where_conditions = ['''
    id in (
'014451130356-10410-1657968354000-127726035',
'013305233952-10410-1666875804000-127731725',
'014923873456-10410-1666876466000-127733985',
'014061303525-10410-1666875824000-127731813',
'014061303525-10410-1666876160000-127733002',
'013305246842-10410-1666876344000-127733597',
'013305092126-10410-1666876015000-127732526',
'013305124742-10410-1666876010000-127732514',
'013305233560-10410-1666875808000-127731741',
'013305268246-10410-1666875675000-127731288',
'013305268246-10410-1666876134000-127732916',
'013305103011-10410-1666875796000-127731697',
'014034000967-10410-1666875968000-127732374',
'013305189688-10410-1666876395000-127733739',
'013305153006-10410-1666875492000-127730512',
'013305153006-10410-1666876073000-127732740',
'013308843926-10410-1666876092000-127732808',
'013305033353-10410-1666875775000-127731619',
'013305267759-10410-1666875770000-127731595',
'013305267759-10410-1666876322000-127733520',
'013305132380-10410-1666875671000-127731269',
'013305305063-10410-1666875841000-127731881',
'013305305063-10410-1666876320000-127733517',
'014706882651-10410-1666875769000-127731598',
'013305686314-10410-1666876233000-127733248',
'013305233401-10410-1666875632000-127731101',
'013305233401-10410-1666876025000-127732573',
'013305233401-10410-1666876422000-127733836',
'010369139917-10410-1666875832000-127731846',
'013305019249-10410-1666875836000-127731859',
'013305268188-10410-1666875715000-127731460',
'013305131843-10410-1666875639000-127731130',
'013305122497-10410-1666876075000-127732748',
'013305204900-10410-1666875595000-127730954',
'013305204900-10410-1666875976000-127732392',
'013305204900-10410-1666876433000-127733860',
'013305189170-10410-1666875812000-127731754',
'013305205633-10410-1666876321000-127733523',
'013305232937-10410-1666875720000-127731429',
'013305232937-10410-1666876224000-127733222',
'013305187510-10410-1666875576000-127730877',
'013305187510-10410-1666875935000-127732262',
'013305289384-10410-1666875648000-127731163',
'013305246341-10410-1666875876000-127732015',
'013305246341-10410-1666876403000-127733775',
'013305252486-10410-1666875941000-127732277',
'013209945613-10410-1666875954000-127732336',
'013866106463-10410-1666876029000-127732600',
'014034031133-10410-1666875940000-127732300',
'018304834723-10410-1666876120000-127732886',
'014416117224-10410-1666876155000-127732984',
'013305133210-10410-1666875624000-127731067',
'013305133210-10410-1666876121000-127732876',
'013305148477-10410-1666876024000-127732569',
'013864201190-10410-1666875648000-127731165',
'014331095004-10410-1666875555000-127730784',
'014331095004-10410-1666876035000-127732622',
'014331095004-10410-1666876416000-127733815',
'014034000904-10410-1666875536000-127730703',
'014034000904-10410-1666875895000-127732104',
'013351141466-10410-1666875776000-127731636',
'013305349829-10410-1666876226000-127733223',
'013305306444-10410-1666876284000-127733404',
'013305306466-10410-1666875971000-127732373',
'013304700607-10410-1666875528000-127730672',
'013305360228-10410-1666875769000-127731597',
'013305360228-10410-1666876447000-127733909',
'013305360016-10410-1666876428000-127733846',
'013305348736-10410-1666875811000-127731744',
'013305348736-10410-1666876170000-127733027',
'013305359749-10410-1666875530000-127730670',
'013305359749-10410-1666876446000-127733901',
'013305314564-10410-1666875883000-127732036',
'013305187794-10410-1666875785000-127731648',
'013305187794-10410-1666876144000-127732933',
'013305336962-10410-1666875644000-127731135',
'013305336962-10410-1666876020000-127732549',
'013305336962-10410-1666876429000-127733849',
'013305337856-10410-1666876408000-127733790',
'013305382621-10410-1666876357000-127733621',
'013305383673-10410-1666875548000-127730749',
'013305349743-10410-1666876025000-127732589',
'014475827342-10410-1666875832000-127731863',
'013305337525-10410-1666875768000-127731596',
'013305337616-10410-1666876198000-127733130',
'013305367534-10410-1666876274000-127733363',
'040058085100-10410-1666875726000-127731455',
'040058085100-10410-1666876091000-127732794',
'013789815331-10410-1666875538000-127730717',
'013789815331-10410-1666876287000-127733420',
'013304890180-10410-1666875712000-127731411',
'013305390858-10410-1666876040000-127732620',
'013305389586-10410-1666875841000-127731877',
'013305389586-10410-1666876219000-127733202',
'014472351173-10410-1666875753000-127731550',
'018105105947-10410-1666875705000-127731387',
'018102971210-10410-1666875923000-127732219',
'018109517719-10410-1666876451000-127733938',
'013305056225-10410-1666875772000-127731615',
'013305056225-10410-1666876133000-127732912',
'013305337078-10410-1666875814000-127731756',
'013305337078-10410-1666876285000-127733408',
'013305289428-10410-1666875641000-127731128',
'013305289428-10410-1666876160000-127732998',
'013305383250-10410-1666875502000-127730547',
'013305383250-10410-1666875968000-127732362',
'013305383250-10410-1666876346000-127733599',
'014426685275-10410-1666875677000-127731313',
'018100990095-10410-1666875777000-127731633',
'013305269592-10410-1666875646000-127731155',
'013305269592-10410-1666876005000-127732499',
'013305269592-10410-1666876377000-127733686',
'013305360191-10410-1666875762000-127731574',
'018101336735-10410-1666876471000-127734007',
'013305349582-10410-1666875990000-127732452',
'014403427049-10410-1666875705000-127731379',
'014403427049-10410-1666876099000-127732813',
'013305316278-10410-1666875842000-127731879',
'013305360773-10410-1666875661000-127731217',
'013305368611-10410-1666876203000-127733146',
'013305018280-10410-1666876301000-127733455',
'013305018292-10410-1666875523000-127730714',
'013305018292-10410-1666876226000-127733282',
'013305338232-10410-1666875888000-127732051',
'013305338232-10410-1666876417000-127733816',
'013305382935-10410-1666875675000-127731277',
'013305382935-10410-1666876085000-127732764',
'018106048335-10410-1666876342000-127733598',
'013305780569-10410-1666876204000-127733150',
'013084148723-10410-1666875656000-127731200',
'013305361663-10410-1666875739000-127731488',
'013305361663-10410-1666876385000-127733706',
'013305206647-10410-1666876158000-127732991',
'013304792928-10410-1666876212000-127733188',
'013305361505-10410-1666875670000-127731262',
'013305328202-10410-1666875988000-127732435',
'013305718469-10410-1666875852000-127731928',
'013305338292-10410-1666876360000-127733630',
'013305789311-10410-1666875657000-127731197',
'013305361190-10410-1666876405000-127733776',
'013305382223-10410-1666875797000-127731696',
'014654335436-10410-1666875818000-127731801',
'014426677867-10410-1666875944000-127732296',
'013305316604-10410-1666875805000-127731728',
'014465021616-10410-1666875898000-127732097',
'014465021616-10410-1666876361000-127733635',
'014465021604-10410-1666875969000-127732364',
'014465021604-10410-1666876417000-127733820',
'016300000807-10410-1666875605000-127731007',
'013305911633-10410-1666875567000-127730837',
'013305382654-10410-1666875618000-127731050',
'013305382654-10410-1666875991000-127732465',
'013305382654-10410-1666876350000-127733610',
'013305367953-10410-1666876162000-127733006',
'013305383754-10410-1666876372000-127733666',
'013304964269-10410-1666875624000-127731064',
'040061329990-10410-1666876011000-127732520',
'014654984456-10410-1666875817000-127731803',
'040516673962-10410-1666876075000-127732738',
'013305382023-10410-1666875620000-127731053',
'013305911397-10410-1666876120000-127732870',
'013305382148-10410-1666876129000-127732898',
'013305208810-10410-1666875783000-127731645',
'013305208810-10410-1666876176000-127733051',
'013305208860-10410-1666875713000-127731416',
'013305208860-10410-1666876090000-127732791',
'013305208860-10410-1666876451000-127733921',
'013305336987-10410-1666876449000-127733915',
'013305316243-10410-1666876169000-127733029',
'013305382503-10410-1666875757000-127731555',
'013305382503-10410-1666876059000-127732696',
'013305289489-10410-1666876340000-127733579',
'013304912992-10410-1666876407000-127733785',
'013303230420-10410-1666875650000-127731174',
'013305218833-10410-1666876278000-127733382',
'013305218834-10410-1666875536000-127730711',
'013305234550-10410-1666875748000-127731523',
'013305234550-10410-1666876205000-127733153',
'013305268054-10410-1666875550000-127730760',
'013305250004-10410-1666875912000-127732163',
'013305160850-10410-1666875777000-127731632',
'014659579046-10410-1666875661000-127731249',
'014854382710-10410-1666875509000-127730609',
'014300294532-10410-1666875928000-127732235',
'019706838395-10410-1666875634000-127731107',
'019706838395-10410-1666875993000-127732461',
'018005014128-10410-1666876272000-127733364',
'013305233952-10410-1666875098000-127728699',
'013305233952-10410-1666875438000-127730279',
'014923873456-10410-1666874334000-127724846',
'014923873456-10410-1666874896000-127727812',
'014061303525-10410-1666874559000-127726043',
'014061303525-10410-1666875072000-127728587',
'014061303525-10410-1666875462000-127730394',
'013305270170-10410-1666874806000-127727374',
'013305270170-10410-1666875176000-127729073',
'040706823918-10410-1666874918000-127728054',
'013305092126-10410-1666875291000-127729649',
'013864216245-10410-1666874533000-127725926',
'013305057961-10410-1666875329000-127729826',
'013305268246-10410-1666874454000-127725501',
'013305268246-10410-1666874863000-127727671',
'013305268246-10410-1666875273000-127729553',
'014034000967-10410-1666874330000-127724811',
'014034000967-10410-1666875115000-127728767',
'014706860888-10410-1666874762000-127727137',
'014706860888-10410-1666875362000-127729972',
'014706996678-10410-1666875050000-127728521',
'013305267759-10410-1666875448000-127730329',
'013305132380-10410-1666874750000-127727069',
'013305132380-10410-1666875124000-127728837',
'013305305063-10410-1666875095000-127728689',
'064011015043-10410-1666874614000-127726350',
'014706872978-10410-1666874824000-127727481',
'013305252664-10410-1666874584000-127726201',
'013305233401-10410-1666874601000-127726281',
'013305233401-10410-1666874982000-127728218',
'013305233401-10410-1666875330000-127729822',
'013305019249-10410-1666874466000-127725562',
'013305019249-10410-1666874826000-127727482',
'013305019249-10410-1666875329000-127729819',
'013305268188-10410-1666875420000-127730193',
'013305224178-10410-1666874534000-127725928',
'013305133240-10410-1666874989000-127728273',
'013305133240-10410-1666875411000-127730164',
'013305204900-10410-1666874459000-127725520',
'013305204900-10410-1666874839000-127727561',
'013305217711-10410-1666875236000-127729372',
'013305189170-10410-1666874300000-127724660',
'013305189170-10410-1666874871000-127727702',
'013305189170-10410-1666875246000-127729420',
'013305788924-10410-1666875327000-127729810',
'013305205633-10410-1666875451000-127730351',
'013305218339-10410-1666874867000-127727678',
'013305232937-10410-1666874451000-127725482',
'013305232937-10410-1666874811000-127727398',
'013305232937-10410-1666875355000-127729946',
'013305205623-10410-1666874424000-127725337',
'013305205623-10410-1666874822000-127727474',
'013305205623-10410-1666875288000-127729647',
'013305187510-10410-1666875210000-127729251',
'040706835773-10410-1666874992000-127728271',
'041034000842-10410-1666875349000-127729924',
'013305232955-10410-1666874862000-127727654',
'013305246341-10410-1666875114000-127728773',
'013305246341-10410-1666875435000-127730266',
'013305252486-10410-1666874330000-127724814',
'013868201856-10410-1666874821000-127727461',
'013866106402-10410-1666875453000-127730361',
'013305133210-10410-1666874379000-127725080',
'013305133210-10410-1666875008000-127728335',
'013305148477-10410-1666874295000-127724638',
'013305148477-10410-1666875425000-127730214',
'013287262134-10410-1666874684000-127726764',
'013864200601-10410-1666874825000-127727480',
'013871004040-10410-1666875396000-127730106',
'013868204518-10410-1666874742000-127727024',
'013305103291-10410-1666874265000-127724452',
'013305103291-10410-1666874768000-127727187',
'013305103291-10410-1666875460000-127730386',
'041023413382-10410-1666874829000-127727721',
'014470643403-10410-1666875323000-127729797',
'013305289034-10410-1666874575000-127726135',
'013305289034-10410-1666875298000-127729686',
'013861002819-10410-1666874337000-127724867',
'013861002773-10410-1666875460000-127730383',
'013351141466-10410-1666874777000-127727235',
'064754810970-10410-1666875108000-127728768',
'013305349829-10410-1666874545000-127725977',
'013305328399-10410-1666874375000-127725057',
'013305306466-10410-1666874393000-127725154',
'013305306466-10410-1666874770000-127727174',
'013305247280-10410-1666876303000-127733458',
'041023969112-10410-1666875963000-127732352',
'041023969112-10410-1666876399000-127733753',
'013305224920-10410-1666875544000-127730743',
'013305224920-10410-1666875941000-127732285',
'013305207534-10410-1666875654000-127731205',
'013305233614-10410-1666876390000-127733728',
'042003606019-10410-1666875508000-127730596',
'013304595800-10410-1666875578000-127730875',
'013304595800-10410-1666875937000-127732261',
'013304595800-10410-1666876321000-127733527',
'013305162643-10410-1666875575000-127730867',
'013305162643-10410-1666876167000-127733022',
'013305162683-10410-1666875559000-127730803',
'013305162683-10410-1666875971000-127732381',
'013305162683-10410-1666876330000-127733563',
'013305132269-10410-1666875729000-127731468',
'013305134239-10410-1666875751000-127731546',
'013305190513-10410-1666876431000-127733857',
'013305103037-10410-1666875901000-127732117',
'013305103037-10410-1666876417000-127733829',
'013305130727-10410-1666875701000-127731372',
'013305151385-10410-1666876451000-127733923',
'013305600608-10410-1666875578000-127730868',
'013305600608-10410-1666876028000-127732590',
'013305600608-10410-1666876413000-127733807',
'013952572681-10410-1666875708000-127731405',
'014462529555-10410-1666875539000-127730744',
'013305206764-10410-1666876217000-127733200',
'013305191106-10410-1666876340000-127733586',
'018091305249-10410-1666875611000-127731049',
'013305148865-10410-1666875931000-127732234',
'040052929063-10410-1666875872000-127731996',
'013305131304-10410-1666875526000-127730643',
'013305191002-10410-1666876348000-127733604',
'014914936242-10410-1666875602000-127730986',
'013305442557-10410-1666875882000-127732034',
'019032516003-10410-1666876395000-127733752',
'017210228007-10410-1666876469000-127734006',
'013305348967-10410-1666875545000-127730737',
'013305090405-10410-1666875777000-127731630',
'013305090405-10410-1666876267000-127733345',
'014706181614-10410-1666875942000-127732284',
'014706181614-10410-1666876438000-127733879',
'013304912511-10410-1666875902000-127732121',
'013304912511-10410-1666876280000-127733394',
'013305132045-10410-1666875564000-127730815',
'013305339177-10410-1666875716000-127731424',
'013305339203-10410-1666875565000-127730819',
'013304970206-10410-1666875586000-127730913',
'013304970206-10410-1666876439000-127733884',
'017210307002-10410-1666875549000-127730795',
'017210307002-10410-1666876202000-127733171',
'013305152327-10410-1666875667000-127731259',
'013305182169-10410-1666875834000-127731854',
'013305182169-10410-1666876198000-127733139',
'014706183156-10410-1666875700000-127731364',
'014706183156-10410-1666876122000-127732880',
'013305251957-10410-1666876400000-127733771',
'013305251950-10410-1666875940000-127732273',
'013305251950-10410-1666876332000-127733565',
'013305271183-10410-1666875494000-127730510',
'013305271183-10410-1666876191000-127733106',
'013305271556-10410-1666875847000-127731898',
'014706181385-10410-1666875925000-127732220',
'013305225598-10410-1666875844000-127731888',
'014706182789-10410-1666875524000-127730633',
'014706182789-10410-1666876098000-127732812',
'013305591672-10410-1666876255000-127733315',
'014075810924-10410-1666875644000-127731168',
'014075725006-10410-1666875664000-127731253',
'013305360228-10410-1666874421000-127725312',
'013305360228-10410-1666874799000-127727343',
'013305360228-10410-1666875294000-127729671',
'013305360016-10410-1666874287000-127724571',
'013305360016-10410-1666874670000-127726620',
'013305360016-10410-1666875274000-127729557',
'013305367669-10410-1666874375000-127725059',
'013305348736-10410-1666874423000-127725333',
'013305348736-10410-1666875049000-127728483',
'013305348736-10410-1666875427000-127730224',
'013305361456-10410-1666874827000-127727493',
'013305187794-10410-1666874416000-127725287',
'013305187794-10410-1666875185000-127729113',
'013305187008-10410-1666874521000-127725858',
'013305187008-10410-1666875234000-127729360',
'013305367102-10410-1666874418000-127725292',
'013305305516-10410-1666874271000-127724486',
'013305305516-10410-1666874700000-127726797',
'013305305516-10410-1666875453000-127730360',
'013305336962-10410-1666875139000-127728892',
'013305337856-10410-1666874293000-127724620',
'013305337856-10410-1666874605000-127726308',
'013305337856-10410-1666875022000-127728381',
'013305337856-10410-1666875469000-127730415',
'013305384314-10410-1666874737000-127726979',
'013305383462-10410-1666874680000-127726698',
'013305337525-10410-1666874992000-127728267',
'014426666431-10410-1666875200000-127729211',
'014559761107-10410-1666874420000-127725324',
'013305368060-10410-1666874764000-127727138',
'013305832676-10410-1666875135000-127728889',
'040058085100-10410-1666874749000-127727057',
'040058085100-10410-1666875355000-127729931',
'013789815350-10410-1666874465000-127725548',
'013789815326-10410-1666874412000-127725262',
'013789815336-10410-1666874880000-127727740',
'013789815331-10410-1666874475000-127725614',
'013789815331-10410-1666874843000-127727584',
'013789815331-10410-1666875206000-127729228',
'013789812687-10410-1666874262000-127724443',
'013789812687-10410-1666874633000-127726463',
'013789812687-10410-1666875355000-127729936',
'013789813780-10410-1666874609000-127726337',
'013304890180-10410-1666875305000-127729720',
'013305368008-10410-1666874377000-127725076',
'018101213774-10410-1666874312000-127724756',
'013305390858-10410-1666875102000-127728711',
'013305389586-10410-1666874705000-127726817',
'013305389586-10410-1666875163000-127729004',
'013305655076-10410-1666874453000-127725480',
'013305655076-10410-1666874914000-127727896',
'013305382808-10410-1666874256000-127724394',
'013305382808-10410-1666874652000-127726547',
'013305382808-10410-1666875048000-127728468',
'014654956512-10410-1666875183000-127729864',
'018102734579-10410-1666875185000-127729152',
'018100961915-10410-1666875216000-127729263',
'018101662390-10410-1666875433000-127730261',
'013305043837-10410-1666875070000-127728582',
'013305337078-10410-1666874542000-127725964',
'013305337078-10410-1666874937000-127728011',
'013305337078-10410-1666875416000-127730180',
'013305383250-10410-1666874284000-127724557',
'013305383250-10410-1666874686000-127726717',
'013305383250-10410-1666875126000-127728820',
'013305269592-10410-1666875275000-127729577',
'013305316613-10410-1666875171000-127729050',
'018104712584-10410-1666875142000-127728928',
'013304730571-10410-1666874276000-127724541',
'013305361458-10410-1666874996000-127728289',
'014403427049-10410-1666875045000-127728455',
'013305316278-10410-1666875224000-127729303',
'013305360773-10410-1666874786000-127727265',
'013305018286-10410-1666874216000-127724194',
'013305018286-10410-1666874563000-127726074',
'013305018279-10410-1666875017000-127728373',
'013305443403-10410-1666874550000-127725999',
'013305124984-10410-1666875010000-127728344',
'013305778929-10410-1666874464000-127725540',
'013305778929-10410-1666874791000-127727298',
'013305778929-10410-1666875131000-127728850',
'013305338232-10410-1666874809000-127727379',
'013305338232-10410-1666875444000-127730307',
'013305348394-10410-1666874582000-127726184',
'013305348356-10410-1666874276000-127724525',
'013305348356-10410-1666874790000-127727284',
'013305348356-10410-1666875144000-127728911',
'014465021597-10410-1666875167000-127729025',
'013303191097-10410-1666874564000-127726082',
'013084148723-10410-1666874511000-127725785',
'013084148723-10410-1666875067000-127728570',
'013305328069-10410-1666875140000-127728897',
'013305778909-10410-1666875169000-127729037',
'013305361663-10410-1666874729000-127726939',
'013305327681-10410-1666874298000-127724640',
'013305327681-10410-1666875491000-127730494',
'014147064973-10410-1666874665000-127726725',
'013305288140-10410-1666875223000-127729302',
'013305328402-10410-1666875256000-127729467',
'013304912343-10410-1666874338000-127724862',
'013305718469-10410-1666875443000-127730303',
'013305382223-10410-1666874366000-127725008',
'013305382223-10410-1666875047000-127728485',
'013305382223-10410-1666875474000-127730445',
'014465021624-10410-1666874617000-127726364',
'013305834085-10410-1666874926000-127727949',
'014426670970-10410-1666875440000-127730298',
'013305305932-10410-1666875039000-127728433',
'013305416287-10410-1666875382000-127730043',
'013305281796-10410-1666874966000-127728138',
'014152043625-10410-1666874551000-127726099',
'014465021616-10410-1666874567000-127726088',
'014465021616-10410-1666875017000-127728368',
'016300000670-10410-1666875484000-127730472',
'013305382654-10410-1666874300000-127724650',
'013305382654-10410-1666874779000-127727225',
'013305382654-10410-1666875258000-127729488',
'013305293547-10410-1666874213000-127724162',
'013305367987-10410-1666874330000-127724813',
'013304964269-10410-1666874420000-127725309',
'013304964269-10410-1666874765000-127727148',
'013305382285-10410-1666874294000-127724624',
'013305382285-10410-1666874891000-127727787',
'014651827219-10410-1666874316000-127724760',
'013305384115-10410-1666875383000-127730060',
'013305367472-10410-1666875381000-127730040',
'013305328340-10410-1666874275000-127724521',
'013305269093-10410-1666874505000-127725743',
'013305269093-10410-1666874916000-127727904',
'013305269093-10410-1666875466000-127730409',
'013305208808-10410-1666874456000-127725504',
'013305208810-10410-1666874980000-127728220',
'013305208810-10410-1666875387000-127730074',
'013305208860-10410-1666874916000-127727912',
'013305208860-10410-1666875380000-127730041',
'014432622649-10410-1666874822000-127727485',
'013305315552-10410-1666874768000-127727163',
'013305672769-10410-1666874271000-127724493',
'013305218895-10410-1666874941000-127728034',
'013305390061-10410-1666875435000-127730251',
'013305327808-10410-1666874591000-127726241',
'013305327808-10410-1666875125000-127728808',
'013305305527-10410-1666874563000-127726062',
'013305305527-10410-1666875347000-127729909',
'013305251939-10410-1666875208000-127729219',
'013305250004-10410-1666875389000-127730079',
'013305160850-10410-1666874384000-127725115',
'013305160850-10410-1666874784000-127727258',
'013305160850-10410-1666875284000-127729616',
'014717825452-10410-1666875123000-127728835',
'014659716788-10410-1666874905000-127727882',
'014659604711-10410-1666472275000-127727692',
'013305250216-10410-1666874628000-127726438',
'013305161437-10410-1666874601000-127726293',
'014407743640-10410-1666874259000-127724475',
'016033578740-10410-1666874933000-127728013',
'019706838429-10410-1666874692000-127726755',
'013305091114-10410-1666874401000-127725203',
'013305091114-10410-1666875000000-127728304',
'013305091114-10410-1666875391000-127730092',
'019706838395-10410-1666874248000-127724355',
'019706838395-10410-1666875166000-127729029',
'064917517291-10410-1666874926000-127727984',
'013305131444-10410-1666875257000-127729471',
'014418119520-10410-1666874970000-127728195',
'013305162013-10410-1666875480000-127730459',
'014448234343-10410-1666874257000-127724434',
'014029774140-10410-1666875365000-127729983',
'014729528052-10410-1666874974000-127728180',
'041023969112-10410-1666874320000-127724771',
'041023969112-10410-1666874628000-127726490',
'041023969112-10410-1666875003000-127728313',
'041023969112-10410-1666875457000-127730372',
'040356573643-10410-1666874700000-127726838',
'013305130877-10410-1666874511000-127725779',
'041023961698-10410-1666874833000-127727503',
'013305233195-10410-1666874479000-127725618',
'018004849083-10410-1666875451000-127730376',
'013305188153-10410-1666874218000-127724214',
'013305188153-10410-1666874627000-127726435',
'013305188153-10410-1666875300000-127729703',
'018108619433-10410-1666874896000-127727838',
'013305233614-10410-1666874501000-127725725',
'013305186867-10410-1666874401000-127725209',
'013305186867-10410-1666874742000-127727021',
'018103758590-10410-1666874571000-127726149',
'018104050217-10410-1666874480000-127725643',
'013656546980-10410-1666874557000-127726057',
'018106029959-10410-1666875264000-127729524',
'019000468186-10410-1666875467000-127730427',
'013059699570-10410-1666874373000-127726370',
'013305283033-10410-1666874293000-127724617',
'013305283033-10410-1666874899000-127727823',
'013304595800-10410-1666874449000-127725468',
'013304595800-10410-1666874831000-127727511',
'013304595800-10410-1666875211000-127729245',
'013305130481-10410-1666874663000-127726627',
'013305234423-10410-1666874762000-127727135',
'013305175112-10410-1666874233000-127724272',
'013305571946-10410-1666875492000-127730499',
'013305162683-10410-1666874629000-127726443',
'013305162683-10410-1666874959000-127728109',
'013305206932-10410-1666874396000-127725185',
'013305132269-10410-1666874223000-127724231',
'013305132269-10410-1666874524000-127725889',
'013305132269-10410-1666875335000-127729848',
'013305134239-10410-1666874288000-127724588',
'013305134239-10410-1666875304000-127729716',
'040706100020-10410-1666874295000-127724628',
'013305130751-10410-1666874548000-127725993',
'013305133503-10410-1666874317000-127724745',
'013305189303-10410-1666874310000-127724703',
'013305222923-10410-1666874812000-127727400',
'013305288273-10410-1666874733000-127726957',
'013305071604-10410-1666874324000-127724823',
'013305252091-10410-1666874325000-127724781',
'013305151385-10410-1666874394000-127725173',
'013305223247-10410-1666875368000-127729989',
'013305043712-10410-1666874971000-127728170',
'013305043712-10410-1666875289000-127729643',
'044142101188-10410-1666874776000-127727233',
'013305600608-10410-1666875199000-127729177',
'013922020109-10410-1666875186000-127729134',
'014467933789-10410-1666874283000-127724583',
'018108434180-10410-1666875469000-127730448',
'014926661089-10410-1666874513000-127725791',
'014377258835-10410-1666875060000-127728566',
'013305191106-10410-1666874856000-127727617',
'014818971692-10410-1666874347000-127724933',
'014935807506-10410-1666874554000-127726054',
'040052929063-10410-1666875356000-127729937',
'013305131304-10410-1666874451000-127725474',
'013305131304-10410-1666875077000-127728616',
'014656574375-10410-1666874742000-127727011',
'014938690984-10410-1666874552000-127726046',
'013305217178-10410-1666875065000-127728569',
'013305217178-10410-1666875434000-127730270',
'018104376910-10410-1666874233000-127724282',
'017001700184-10410-1666874856000-127727755',
'018109620493-10410-1666874490000-127725693',
'013305246404-10410-1666874956000-127728090',
'018001574209-10410-1666875000000-127728310',
'014914936242-10410-1666874204000-127724117',
'014914936242-10410-1666874809000-127727381',
'013305225374-10410-1666874794000-127727314',
'013305265663-10410-1666874673000-127726645',
'013305265663-10410-1666875197000-127729161',
'013305281798-10410-1666874212000-127724150',
'013305281798-10410-1666874608000-127726329',
'013305281798-10410-1666874946000-127728053',
'019032516003-10410-1666874331000-127724831',
'019032516003-10410-1666874898000-127727832',
'017210228007-10410-1666875111000-127728789',
'013305090405-10410-1666874571000-127726117',
'013305090405-10410-1666874872000-127727705',
'014706182226-10410-1666874369000-127725021',
'013305152287-10410-1666874305000-127724681',
'013305152287-10410-1666875065000-127728567',
'013305033190-10410-1666874499000-127725744',
'013305033190-10410-1666874920000-127727935',
'013305033190-10410-1666875381000-127730053',
'013305033159-10410-1666874760000-127727136',
'013305019232-10410-1666874394000-127725166',
'013305019232-10410-1666874840000-127727549',
'013305019232-10410-1666875420000-127730201',
'013305132045-10410-1666875248000-127729437',
'013305489438-10410-1666874321000-127724777',
'017210310002-10410-1666875454000-127730398',
'017210307002-10410-1666874848000-127727635',
'017210425009-10410-1666875358000-127730229',
'017210228005-10410-1666874463000-127725581',
'013305152327-10410-1666874492000-127725685',
'013305152327-10410-1666875007000-127728334',
'013305182169-10410-1666874450000-127725476',
'013305182169-10410-1666874972000-127728193',
'013305182169-10410-1666875475000-127730442',
'013305217906-10410-1666874892000-127727796',
'013305217906-10410-1666875338000-127729863',
'013305217899-10410-1666874395000-127725172',
'013305217899-10410-1666874710000-127726853',
'013305217899-10410-1666875310000-127729741',
'013305251950-10410-1666875492000-127730502',
'013305271556-10410-1666874582000-127726173',
'013305271556-10410-1666875043000-127728445',
'013305271556-10410-1666875476000-127730439',
'013305246095-10410-1666875139000-127728901',
'013305225598-10410-1666874715000-127726872',
'014706181741-10410-1666874458000-127725529',
'013305756303-10410-1666874268000-127724468',
'014075725217-10410-1666874918000-127727920',
'014075725471-10410-1666875008000-127728353'
        
    )  
    ''',
                        ]
    main(where_conditions)
    # target_risk_detail_sql, risk_sql2, delete_risk_detail_sql, insert_hour_sql = loadSQL(where_conditions)
    # print(delete_risk_detail_sql)
