"""
轨迹速度平滑处理示例
演示如何使用不同的滤波方法处理GPS轨迹数据中的速度突变
"""

from speed_smoothing import TrajectorySpeedSmoother
import matplotlib.pyplot as plt
import numpy as np

def quick_smoothing_example():
    """快速平滑示例"""
    print("=== 轨迹速度平滑处理示例 ===\n")
    
    # 1. 创建平滑器并加载数据
    smoother = TrajectorySpeedSmoother('drivingtime.csv')
    
    if not smoother.load_data():
        print("数据加载失败，请检查文件路径")
        return
    
    # 2. 检测速度异常值
    print("\n--- 异常值检测 ---")
    outliers = smoother.detect_speed_outliers(threshold_factor=2.0)
    if len(outliers) > 0:
        print(f"异常速度值位置: {outliers[:10]}...")  # 只显示前10个
        print(f"异常速度值: {smoother.original_speed[outliers[:10]]}")
    
    # 3. 应用推荐的平滑方法（组合滤波）
    print("\n--- 应用组合平滑滤波 ---")
    smoother.apply_smoothing(
        method='combined', 
        methods=['gaussian', 'moving_average']
    )
    
    # 4. 计算平滑效果指标
    print("\n--- 平滑效果评估 ---")
    metrics = smoother.calculate_metrics()
    for key, value in metrics.items():
        print(f"{key}: {value:.2f}")
    
    # 5. 保存结果
    smoother.save_results('smoothed_drivingtime.csv')
    
    # 6. 绘制对比图
    print("\n--- 生成对比图 ---")
    smoother.plot_comparison('speed_smoothing_result.png')
    
    print("\n处理完成！")
    print("- 平滑后的数据已保存到: smoothed_drivingtime.csv")
    print("- 对比图已保存到: speed_smoothing_result.png")

def compare_all_methods():
    """比较所有平滑方法的效果"""
    print("\n=== 比较所有平滑方法 ===\n")
    
    smoother = TrajectorySpeedSmoother('drivingtime.csv')
    if not smoother.load_data():
        return
    
    # 定义所有方法及其参数
    methods = {
        '移动平均': ('moving_average', {'window_size': 5}),
        '高斯滤波': ('gaussian', {'sigma': 1.0}),
        '中值滤波': ('median', {'kernel_size': 5}),
        'Savitzky-Golay': ('savgol', {'window_length': 11, 'polyorder': 3}),
        '巴特沃斯': ('butterworth', {'cutoff_freq': 0.1, 'order': 4}),
        '组合滤波': ('combined', {'methods': ['gaussian', 'moving_average']})
    }
    
    results = {}
    
    for name, (method, params) in methods.items():
        print(f"--- 测试 {name} ---")
        try:
            smoother.apply_smoothing(method, **params)
            metrics = smoother.calculate_metrics()
            results[name] = metrics
            print(f"平滑度改善: {metrics['平滑度改善比例']:.1f}%")
        except Exception as e:
            print(f"方法 {name} 执行失败: {e}")
    
    # 找出最佳方法
    if results:
        best_method = max(results.keys(), 
                         key=lambda x: results[x]['平滑度改善比例'])
        print(f"\n最佳平滑方法: {best_method}")
        print(f"平滑度改善: {results[best_method]['平滑度改善比例']:.1f}%")

def custom_smoothing():
    """自定义平滑参数示例"""
    print("\n=== 自定义平滑参数 ===\n")
    
    smoother = TrajectorySpeedSmoother('drivingtime.csv')
    if not smoother.load_data():
        return
    
    # 根据数据特点调整参数
    data_length = len(smoother.original_speed)
    speed_std = np.std(smoother.original_speed)
    
    print(f"数据长度: {data_length}")
    print(f"速度标准差: {speed_std:.2f}")
    
    # 根据数据特点选择参数
    if speed_std > 20:  # 速度变化较大
        print("检测到速度变化较大，使用强平滑参数")
        smoother.apply_smoothing('gaussian', sigma=2.0)
    elif speed_std > 10:  # 中等变化
        print("检测到中等速度变化，使用中等平滑参数")
        smoother.apply_smoothing('combined', 
                                methods=['gaussian', 'moving_average'])
    else:  # 变化较小
        print("速度变化较小，使用轻度平滑")
        smoother.apply_smoothing('moving_average', window_size=3)
    
    metrics = smoother.calculate_metrics()
    print(f"平滑效果: {metrics['平滑度改善比例']:.1f}%")

if __name__ == "__main__":
    # 运行快速示例
    quick_smoothing_example()
    
    # 可选：比较所有方法（取消注释以运行）
    # compare_all_methods()
    
    # 可选：自定义参数示例（取消注释以运行）
    # custom_smoothing()
