from yyutils import *
from utils.config import bpath
from utils.YYSQLTemplate import YYSQLTemplate

template_file = 'templates/template_month_{year}年{month}月（给保险）月度车辆风险情况（数据版）【输出版】.xlsx'


def loadSQL(start_day, end_day):
    wide_sql = YYSQLTemplate.wide_stats(
        ['vehicle_type = 4'],
        'vehicle_no,vehicle_color',
        '''
        vehicle_no,vehicle_color,
        any(area_manger_short_name) area_manger_short_name,
         any(owner_id) owner_id, any(owner_name) owner_name,
         any(third_party_id) third_party_id, any(third_party_name) third_party_name,
         sum(risk_oneLevel) + sum(risk_twoLevel) + sum(risk_threeLevel) riskNum,
         sum(risk_oneLevel) l1_riskNum,
         sum(risk_twoLevel) l2_riskNum,
         sum(risk_threeLevel) l3_riskNum,
         if(max(online) =1,1,0 ) is_online,
         if(min(never_gps) +  min(never_alarm) + min(never_acc) > 0 , 0 , 1) is_ok
        ''',
        start_day=start_day, end_day=end_day
    )

    vin_sql = YYSQLTemplate.vehicleRelatedInfosWide()
    final_sql = SQLHelper().table(wide_sql, 'A').leftJoin().table(vin_sql, 'B') \
        .on('A.vehicle_no = B.vehicle_no and A.vehicle_color = B.vehicle_color')\
        .select(
        '''
        A.vehicle_no vehicle_no,
        dictGetString('gdispx_data.sys_dict_item','label',(toString(A.vehicle_color),'vehicle_color')) vehicle_color,
        owner_name,
        vin,
        engine_number,
        splitByChar(',', area_manger_short_name)[2] AS city_name,
        splitByChar(',', area_manger_short_name)[3] AS area_name,
        compulsory_insurance_name,
        commercial_insurance_name,
        riskNum,
        l1_riskNum,
        l2_riskNum,
        l3_riskNum,
        third_party_name,
        if(is_online = 1, '是' , '否') is_online,
        if(is_ok = 1, '是' , '否') is_ok
        '''
    )
    # print(final_sql)
    return final_sql


def load_data(start_day, end_day):
    sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(sql)
    db.close()
    return datalist


def fill_sheet1(elhander, datalist, year,month):
    sheet = elhander.activeWorksheet("Sheet1")
    row_styles = elhander.copyRowStyle(sheet, 3)
    elhander.clearData(sheet, 3)
    sheet[1][0].value = sheet[1][0].value.format(year=year, month=month)
    elhander.fillData(sheet, datalist, row_styles)


def writeByTemplate(datalist,  year,month):
    _, fn = get_filePath_and_fileName(template_file)
    fn = fn.format(year=year, month=month).strip('template_month_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, datalist, year,month)
    elhander.save()


def main(start_day, end_day, days):
    datalist = load_data(start_day, end_day)
    year = start_day.split('-')[0]
    month = start_day.split('-')[1]
    writeByTemplate(datalist, year,month)


if __name__ == '__main__':
    start, end, days = TimeUtils.getLastMonthRange()
    main(start, end, days)
