from yyutils import *
from utils.YYSQLTemplate import YYSQLTemplate
from utils.config import orderByCity, FLOORtoPercent, bpath
from collections import OrderedDict

template_file = 'templates/template_month_{year}年{month}月份各设备商数据情况(数据版).xlsx'


def loadSQL(start_day, end_day, days):
    avg_drift_sql = YYSQLTemplate.calTrackAvgDriftPoints(start_day, end_day)
    v_jg_select_columns = '''
                        vehicle_no ,
                        vehicle_color ,
                        any(master_producter_id) master_producter_id0,
                        any(master_producter_name) master_producter_name0,
                        max(online) online,
                        if( sum(drift_point_count) > avg_drift_point_count,1,0  ) is_drift,
                        sum(total_mileage) total_mileage,
                        sum(continuous_mileage) continuous_mileage,
                        if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, 0,1  ) is_ok,
                        min(never_gps) is_never_gps,
                        min(never_alarm) is_never_alarm,
                        min(never_acc) is_never_acc,
                        sum(accessoriesNum) accessoriesNum,
                        sum(accessoriesonTimeNum) accessoriesonTimeNum,
                        sum(risk_disposeNum) risk_disposeNum,
                        risk_disposeNum - sum(check_confirm_riskNum) risk_disposeNum_true
                        
                    '''
    v_jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats(
        [], 'vehicle_no,vehicle_color',
        v_jg_select_columns,
        start_day=start_day,
        end_day=end_day
    )
    v_jg_sql = SQLHelper().table(v_jg_sql).select('''
                        vehicle_no ,
                        vehicle_color ,
                        if(master_producter_id0 is null,18,master_producter_id0) master_producter_id,
                        if(master_producter_id0 is null,'非粤标设备（1月18日前安装）',master_producter_name0) master_producter_name,
                        online,
                        is_drift,
                        total_mileage,
                        continuous_mileage,
                        is_ok,
                        is_never_gps,
                        is_never_alarm,
                        is_never_acc,
                        accessoriesNum,
                        accessoriesonTimeNum,
                        risk_disposeNum,
                        risk_disposeNum_true
        ''')
    productor_sql = SQLHelper().table(v_jg_sql).groupBy('master_producter_id') \
        .select('''
                master_producter_id,
                any(master_producter_name) master_producter_name,
                count(1) jg_count,
                sum(online) online_count,
                FLOOR(sum(online) / count(1),4 ) online_rate,
                sum(is_drift) high_drift_count,
                FLOOR(if(online_count != 0,high_drift_count / online_count,0),4 ) drift_rate,
                FLOOR(sum(total_mileage),2) totalMileage,
                FLOOR(sum(continuous_mileage),2) continuousMileage,
                FLOOR(if(totalMileage != 0,continuousMileage / totalMileage,0),4 ) track_rate,
                sum(is_ok) ok_count,
                sum(is_never_gps) never_gps_count,
                sum(is_never_alarm) never_alarm_count,
                sum(is_never_acc) never_acc_count,
                FLOOR(ok_count / jg_count,4 ) ok_rate,
                sum(accessoriesNum) accessoriesNum_count,
                accessoriesNum_count - sum(accessoriesonTimeNum) accessories_miss_count,
                FLOOR(if(accessoriesNum_count!=0,accessories_miss_count / accessoriesNum_count,0),4 ) accessories_miss_rate,
                sum(risk_disposeNum) risk_disposeNum,
                sum(risk_disposeNum_true) risk_disposeNum_true,
                FLOOR(risk_disposeNum_true/risk_disposeNum,4) risk_disposeNum_true_rate
                ''')
    return avg_drift_sql, productor_sql


def export_outlist_orderDict(inlist):
    h = [
        'master_producter_name','jg_count','online_count','online_rate',
        'high_drift_count','drift_rate','totalMileage','continuousMileage','track_rate',
        'ok_count','never_gps_count','never_alarm_count','never_acc_count','ok_rate',
        'accessoriesNum_count','accessories_miss_count','accessories_miss_rate',
        'risk_disposeNum','risk_disposeNum_true','risk_disposeNum_true_rate'
    ]
    outlist = []
    for e in inlist:
        e['online_rate'] = FLOORtoPercent(e['online_rate'])
        e['drift_rate'] = FLOORtoPercent(e['drift_rate'])
        e['track_rate'] = FLOORtoPercent(e['track_rate'])
        e['ok_rate'] = FLOORtoPercent(e['ok_rate'])
        e['accessories_miss_rate'] = FLOORtoPercent(e['accessories_miss_rate'])
        e['risk_disposeNum_true_rate'] = FLOORtoPercent(e['risk_disposeNum_true_rate']) if e['risk_disposeNum_true_rate'] else '-'
        v = [
            e['master_producter_name'],e['jg_count'],e['online_count'],e['online_rate'],
            e['high_drift_count'],e['drift_rate'],
            e['totalMileage'],e['continuousMileage'],e['track_rate'],
            e['ok_count'],e['never_gps_count'],e['never_alarm_count'],e['never_acc_count'],
            e['ok_rate'],e['accessoriesNum_count'],e['accessories_miss_count'],
            e['accessories_miss_rate'],e['risk_disposeNum'],e['risk_disposeNum_true'],
            e['risk_disposeNum_true_rate']
        ]
        ne = OrderedDict(zip(
            h,
            v
        ))
        outlist.append(ne)
    return outlist


def doSum(inlist):
    ne = OrderedDict()
    ne['master_producter_name'] = '总计'
    ne['jg_count'] = sum([e['jg_count'] for e in inlist])
    ne['online_count'] = sum([e['online_count'] for e in inlist])
    ne['online_rate'] = FLOORtoPercent(ne['online_count'] / ne['jg_count'])
    ne['high_drift_count'] = sum([e['high_drift_count'] for e in inlist])
    ne['drift_rate'] = FLOORtoPercent(ne['high_drift_count'] / ne['online_count'])
    ne['totalMileage'] = sum([e['totalMileage'] for e in inlist])
    ne['continuousMileage'] = sum([e['continuousMileage'] for e in inlist])
    ne['track_rate'] = FLOORtoPercent(ne['continuousMileage'] / ne['totalMileage'])
    ne['ok_count'] = sum([e['ok_count'] for e in inlist])
    ne['never_gps_count'] = sum([e['never_gps_count'] for e in inlist])
    ne['never_alarm_count'] = sum([e['never_alarm_count'] for e in inlist])
    ne['never_acc_count'] = sum([e['never_acc_count'] for e in inlist])
    ne['ok_rate'] = FLOORtoPercent(ne['ok_count'] / ne['jg_count'])
    ne['accessoriesNum_count'] = sum([e['accessoriesNum_count'] for e in inlist])
    ne['accessories_miss_count'] = sum([e['accessories_miss_count'] for e in inlist])
    ne['accessories_miss_rate'] = FLOORtoPercent(ne['accessories_miss_count'] / ne['accessoriesNum_count'])
    ne['risk_disposeNum'] = sum([e['risk_disposeNum'] for e in inlist])
    ne['risk_disposeNum_true'] = sum([e['risk_disposeNum_true'] for e in inlist])
    ne['risk_disposeNum_true_rate'] = FLOORtoPercent(ne['risk_disposeNum_true'] / ne['risk_disposeNum'])
    return ne

def load_data(start_day, end_day, days):
    avg_drift_sql, productor_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    avg_drift_list = db.query_to_dictList(avg_drift_sql)
    avg_drift_point_count = avg_drift_list[0]['average_drift_num']
    productor_sql = productor_sql.replace('avg_drift_point_count', str(avg_drift_point_count))
    datalist = db.query_to_dictList(productor_sql)
    db.close()
    outlist = export_outlist_orderDict(datalist)
    se = doSum(datalist)
    return outlist,se


def fill_sheet1(elhander, inlist, se, year, month):
    sheet = elhander.activeWorksheet("汇总")
    row_styles = elhander.copyRowStyle(sheet, 6)
    row_styles1 = elhander.copyRowStyle(sheet, 30)
    elhander.clearData(sheet, 6)
    sheet[2][0].value = sheet[2][0].value.format(year=year, month=month)
    elhander.fillData(sheet, inlist, row_styles)
    elhander.fillData(sheet, [se], row_styles1)


def writeByTemplate(inlist, se, year, month):
    _, fn = get_filePath_and_fileName(template_file)
    fn = fn.format(year=year, month=month).strip('template_month_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, inlist, se, year, month)
    elhander.save()


def main(start_day, end_day, days):
    outlist,se = load_data(start_day, end_day, days)
    year = start_day.split('-')[0]
    month = start_day.split('-')[1]
    writeByTemplate(outlist, se, year, month)



if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    main(start,end,days)
