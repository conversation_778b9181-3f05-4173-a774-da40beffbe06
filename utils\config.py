bpath = r'/data/track_report'

CITY_COUNT = 21

WEEK_COUNT = 7

CITY_ORDER = [
    '广州', '深圳', '珠海', '汕头', '佛山', '韶关', '湛江',
    '肇庆', '江门', '茂名', '惠州', '梅州', '汕尾', '河源',
    '阳江', '清远', '东莞', '中山', '潮州', '揭阳', '云浮',
]

VEHICLE_TYPE_ORDER = ['客运车辆', '危运车辆', '重货车辆']

vehicle_type_dict = {
    1: '客运车辆',
    3: '危运车辆',
    4: '重货车辆',
}
vehicle_color_dict = {
    1: '蓝色',
    2: '黄色',
    93: '黄绿双拼色',
    94: '渐变绿色'
}


def orderByCity(inlist, secondary_order=None, secondary_desc=True, pid=True):
    tmplist = []
    for e in inlist:
        idx = 100
        if e['city_name'] in CITY_ORDER:
            idx = CITY_ORDER.index(e['city_name'])
        elif e['city_name'] and e['city_name'][:-1] in CITY_ORDER:
            idx = CITY_ORDER.index(e['city_name'][:-1])
        tmplist.append((e,idx))
    if not secondary_order:
        tmplist.sort(key=lambda o: o[1])
    else:
        tmplist.sort(key=lambda o: o[1] if not secondary_order else (-o[1], o[0][secondary_order]),
                     reverse=secondary_desc)
    outlist = [t[0] for t in tmplist]
    if pid:
        for i in range(len(outlist)):
            outlist[i]['pid'] = i + 1
    return outlist


def orderByCityVT(inlist, third_order=None, third_desc=True, pid=True):
    tmplist = []
    for e in inlist:
        idx = 100
        if e['city_name'] in CITY_ORDER:
            idx = CITY_ORDER.index(e['city_name'])
        elif e['city_name'] and e['city_name'][:-1] in CITY_ORDER:
            idx = CITY_ORDER.index(e['city_name'][:-1])
        idx2 =  VEHICLE_TYPE_ORDER.index(e['vehicle_type']) \
            if e['vehicle_type'] in VEHICLE_TYPE_ORDER else int(e['vehicle_type'])
        tmplist.append((e,idx,idx2))

    if not third_order:
        tmplist.sort(key=lambda o: (o[1], o[2]))
    elif third_desc:
        tmplist.sort(key=lambda o: (-o[1], -o[2], o[0][third_order]),
                     reverse=third_desc)
    else:
        tmplist.sort(key=lambda o: (o[1], o[2], o[0][third_order]),
                     reverse=third_desc)
    outlist = [t[0] for t in tmplist]
    if pid:
        for i in range(len(outlist)):
            outlist[i]['pid'] = i + 1
    return outlist


def FLOORCUT(a, cut_offset=4):
    a = str(a)
    sps = a.split('.')
    int_part = sps[0]
    floor_part = sps[1] if len(sps) > 1 else ''
    if not floor_part:
        return int_part
    return f'{int_part}.{floor_part[:cut_offset]}'


def FLOORtoPercent(a):
    return FLOORCUT(a * 100, 2) + '%'


def do_rank_by_col(inlist, col, asc=True):
    if asc:
        inlist.sort(key=lambda o: o[col])
    else:
        inlist.sort(key=lambda o: -o[col])
    for i in range(len(inlist)):
        inlist[i][f'{col}_rank'] = i + 1
        if i == 0:
            continue
        if inlist[i][col] == inlist[i - 1][col]:
            inlist[i][f'{col}_rank'] = inlist[i - 1][f'{col}_rank']
