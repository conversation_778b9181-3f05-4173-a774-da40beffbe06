#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
from datetime import datetime, timedelta
import vehicle_week_report as vehicle_week_report
import vehicle_detail_report as vehicle_detail_report
import alarm_level_week_report as alarm_level_week_report
import alarm_week_report_v2 as alarm_week_report
import alarm_detail_report as alarm_detail_report
import system_owner_login_week_report as system_owner_login_week_report
import system_week_report as system_week_report
import need_acc_alarm_detail_report as need_acc_alarm_detail_report
import vehicle_alarm_day_stats_v1 as vehicle_alarm_day_stats


if __name__ == '__main__':
    begin_date = '2021-11-28'
    begin_date_1day = '2021-11-30'
    end_date = '2021-12-01'
    # 5大指标
    vehicle_week_report.export(begin_date, end_date)
    vehicle_detail_report.export(begin_date, end_date)
    # 风险等级
    alarm_level_week_report.export(begin_date, end_date)
    # 合格率(明细只能查一天)
    alarm_week_report.export(begin_date, end_date)
    alarm_detail_report.export(begin_date_1day, end_date)
    # 系统监控
    system_owner_login_week_report.export(begin_date, end_date)
    # 压缩打包
    os.system(
        'zip -q report/report_{}_{}.zip *.csv'.format(begin_date.replace('-', '')[-4:],
                                                      end_date.replace('-', '')[-4:]))
    os.system('rm -f *.csv')

