#!/usr/bin/env python
# -*- coding: utf-8 -*-
import datetime


def now_time():
    return datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')


def print_log(msg):
    print('{}: {}'.format(now_time(), msg))


def mysql_result_to_dict(rows, key):
    """
    将mysql的查询结果转换成字典
    :param rows:
    :param key:
    :return: (列名逗号分隔, 结果字典)
    """
    results = {}
    row0 = rows[0]
    value0 = row0.pop(key)
    cols = ',' + ','.join(row0.keys())
    results[value0] = ',' + ','.join([str(x) if x or x == 0 else '' for x in row0.values()])
    for row in rows[1:]:
        value = row[key]
        row.pop(key)
        results[value] = ',' + ','.join([str(x) if x or x == 0 else '' for x in row.values()])
    return cols, results


def ck_results_to_dict(ck_results, with_columns=False):
    """
    将ck的查询结果转换成字典,每行的第一个值是主键
    :param ck_results:
    :param with_columns:
    :return:
    """
    cols = [f[0] for f in ck_results[1]]
    result_dict = {None: dict(zip(cols[1:], [''] * (len(cols) - 1)))}  # None对应的行要去掉key列,不然合并时会有问题
    rows = ck_results[0]
    for row in rows:
        result_dict[row[0]] = dict(zip(cols, row))
    if with_columns:
        return ','.join(cols[1:]), result_dict
    else:
        return result_dict


def merge_ck_results_list(ck_results_list):
    """
    将ck的多个查询结果合并,每个结果里的每行的第一个值是合并key
    :param ck_results_list:
    :return:
    """
    merge_results = ck_results_to_dict(ck_results_list[0])
    for results in ck_results_list[1:]:
        result_i = ck_results_to_dict(results)
        for k in merge_results:
            if k in result_i:
                merge_results[k].update(result_i[k])
            else:
                merge_results[k].update(result_i[None])
    return merge_results


