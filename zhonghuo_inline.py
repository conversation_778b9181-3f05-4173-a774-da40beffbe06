#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

need_supervise_truck = """
    SELECT 
        jc.XiaQu<PERSON>hi, 
        count(*)
    FROM gdispx_data.jg_cheliang jc
    where jc.vehicle_type = '4' and jc.service_result = '1'
    group by jc.XiaQuShi
"""

has_supervise_truck = """
    SELECT
        any(ba.city_short),
        count(*)
    FROM gdispx_data.vehicle_related_infos vri 
    left join gdispx_data.basics_area ba on toString(vri.area_id) = toString(ba.id)
    where vri.vehicle_type = '4'
    group by ba.city_id;
"""

inline_truck = """
    SELECT
        city,
        COUNT(*),
        any(gps_time)
    FROM (SELECT
        city,
        toDate(gps_time) gps_time
    FROM gdispx_data.business_gps_log t
    left join 
        (select master_sim_code,any(ba.city_short) city,ba.city_id
        FROM vehicle_related_infos
        left join basics_area ba on vehicle_related_infos.area_id = ba.id where vehicle_type = '4'
        GROUP by master_sim_code,city_id 
        ) vri on t.device_id = vri.master_sim_code
    where toDate(gps_time) >= '{begin_date}' and toDate(gps_time) <= '{end_date}' and vehicle_type_id = '4') d
    group by city,gps_time
    order by gps_time,city ASC;
"""



def query_then_write_csv(query_sql, client, output_file):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join(['"' + col[0] + '"' for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join(['"' + str(x) + '"' for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        file_suffix = '_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])

        with open('重货在线' + file_suffix, 'w') as output_file:
            output_file.write('每日在线\n')
            query_then_write_csv(inline_truck.format(begin_date=begin_date, end_date=end_date), client, output_file)
            output_file.write('\n\n需监管\n')
            query_then_write_csv(need_supervise_truck, client, output_file)
            output_file.write('\n\n已监管\n')
            query_then_write_csv(has_supervise_truck, client, output_file)

    finally:
        ck.disconnect()


if __name__ == '__main__':
    begin_date = '2022-04-25'
    end_date = '2021-05-01'
    export(begin_date, end_date)
