from yyutils import *
from utils.YYSQLTemplate import YY<PERSON><PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    xjg_sql = YYSQLTemplate.xjg_stats([],
                                      "city_id,vehicle_type",
                                      '''
                                      toInt32(city_id) city_id,
                                      toUInt8(vehicle_type) vehicle_type, 
                                      count(distinct vehicle_no,vehicle_color) xjg_count 
                                      ''',
                                      start_day, end_day)
    v_jg_sql = YYSQLTemplate.wide_stats(
        [],
        "city_id,vehicle_no,vehicle_color,vehicle_type",
        '''
        city_id,
        vehicle_no,
        vehicle_color,
        vehicle_type,
        any(city_short_name) city_name,
        max(online) is_online,
        if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, 0,1) is_ok,
        min(never_gps) is_never_gps,
        min(never_alarm) is_never_alarm,
        min(never_acc) is_never_acc
        ''',
        start_day=start_day, end_day=end_day)
    jg_sql = SQLHelper().table(v_jg_sql) \
        .groupBy('city_id,vehicle_type') \
        .select('''
                city_id,
                vehicle_type,
                any(city_name) city_name,
                count(1) jg_count,
                sum(is_online) online_count,
                sum(is_ok) ok_count,
                sum(is_never_gps) never_gps_count,
                sum(is_never_alarm) never_alarm_count,
                sum(is_never_acc) never_acc_count
        ''')
    join_sql = SQLHelper().table(jg_sql, 'A').leftJoin() \
        .table(xjg_sql, 'B').on('A.city_id=B.city_id and A.vehicle_type = B.vehicle_type') \
        .select('''
                A.city_id city_id,
                city_name,
                A.vehicle_type vehicle_type,
                if(xjg_count<jg_count,jg_count,xjg_count) xjg_count,
                jg_count,
                online_count,
                ok_count,
                never_gps_count,
                never_alarm_count,
                never_acc_count,
                FLOOR(jg_count/xjg_count,4) jg_rate,
                FLOOR(online_count/jg_count,4) online_rate
            ''')
    final_sql = SQLHelper().table(join_sql).groupBy('city_id') \
        .select('''
        city_id,
        any(city_name) city_name,
        maxIf(xjg_count,vehicle_type =1) ke_xjg_count, 
        maxIf(jg_count,vehicle_type =1) ke_jg_count, 
        maxIf(online_count,vehicle_type =1) ke_online_count, 
        maxIf(jg_rate,vehicle_type =1) ke_jg_rate,
        maxIf(online_rate,vehicle_type =1) ke_online_rate,
        maxIf(xjg_count,vehicle_type =3) w_xjg_count, 
        maxIf(jg_count,vehicle_type =3) w_jg_count, 
        maxIf(online_count,vehicle_type =3) w_online_count, 
        maxIf(jg_rate,vehicle_type =3) w_jg_rate,
        maxIf(online_rate,vehicle_type =3) w_online_rate,
        maxIf(xjg_count,vehicle_type =4) z_xjg_count, 
        maxIf(jg_count,vehicle_type =4) z_jg_count, 
        maxIf(online_count,vehicle_type =4) z_online_count,
        maxIf(jg_rate,vehicle_type =4) z_jg_rate,
        maxIf(online_rate,vehicle_type =4) z_online_rate,
        maxIf(xjg_count,vehicle_type =1) + maxIf(xjg_count,vehicle_type =3)
            + maxIf(xjg_count,vehicle_type =4) t_xjg_count,
        maxIf(jg_count,vehicle_type =1) + maxIf(jg_count,vehicle_type =3)
            + maxIf(jg_count,vehicle_type =4) t_jg_count,
        maxIf(online_count,vehicle_type =1) + maxIf(online_count,vehicle_type =3)
            + maxIf(online_count,vehicle_type =4) t_online_count,
        FLOOR(t_jg_count/t_xjg_count,4) t_jg_rate,
        FLOOR(t_online_count/t_jg_count,4) t_online_rate,
        maxIf(ok_count,vehicle_type =1) + maxIf(ok_count,vehicle_type =3)
            + maxIf(ok_count,vehicle_type =4) t_ok_count,
        maxIf(never_gps_count,vehicle_type =1) + maxIf(never_gps_count,vehicle_type =3)
            + maxIf(never_gps_count,vehicle_type =4) t_never_gps_count,
        maxIf(never_alarm_count,vehicle_type =1) + maxIf(never_alarm_count,vehicle_type =3)
            + maxIf(never_alarm_count,vehicle_type =4) t_never_alarm_count,
        maxIf(never_acc_count,vehicle_type =1) + maxIf(never_acc_count,vehicle_type =3)
            + maxIf(never_acc_count,vehicle_type =4) t_never_acc_count,
        FLOOR(t_ok_count/t_jg_count,4) t_ok_rate
        ''')
    return final_sql


def doSum(inlist):
    ke_xjg_count = sum([e['ke_xjg_count'] for e in inlist])
    ke_jg_count = sum([e['ke_jg_count'] for e in inlist])
    ke_online_count = sum([e['ke_online_count'] for e in inlist])
    ke_jg_rate = FLOORtoPercent(ke_jg_count / ke_xjg_count)
    ke_online_rate = FLOORtoPercent(ke_online_count / ke_jg_count)
    w_xjg_count = sum([e['w_xjg_count'] for e in inlist])
    w_jg_count = sum([e['w_jg_count'] for e in inlist])
    w_online_count = sum([e['w_online_count'] for e in inlist])
    w_jg_rate = FLOORtoPercent(w_jg_count / w_xjg_count)
    w_online_rate = FLOORtoPercent(w_online_count / w_jg_count)
    z_xjg_count = sum([e['z_xjg_count'] for e in inlist])
    z_jg_count = sum([e['z_jg_count'] for e in inlist])
    z_online_count = sum([e['z_online_count'] for e in inlist])
    z_jg_rate = FLOORtoPercent(z_jg_count / z_xjg_count)
    z_online_rate = FLOORtoPercent(z_online_count / z_jg_count)
    t_xjg_count = sum([e['t_xjg_count'] for e in inlist])
    t_jg_count = sum([e['t_jg_count'] for e in inlist])
    t_online_count = sum([e['t_online_count'] for e in inlist])
    t_jg_rate = FLOORtoPercent(t_jg_count / t_xjg_count)
    t_online_rate = FLOORtoPercent(t_online_count / t_jg_count)
    t_ok_count = sum([e['t_ok_count'] for e in inlist])
    t_never_gps_count = sum([e['t_never_gps_count'] for e in inlist])
    t_never_alarm_count = sum([e['t_never_alarm_count'] for e in inlist])
    t_never_acc_count = sum([e['t_never_acc_count'] for e in inlist])
    t_ok_rate = FLOORtoPercent(t_ok_count / t_jg_count)
    ne = OrderedDict(zip(
        ('pid', 'city_name',
         'ke_xjg_count', 'ke_jg_count', 'ke_jg_rate', 'ke_online_count', 'ke_online_rate',
         'w_xjg_count', 'w_jg_count', 'w_jg_rate', 'w_online_count', 'w_online_rate',
         'z_xjg_count', 'z_jg_count', 'z_jg_rate', 'z_online_count', 'z_online_rate',
         't_jg_count', 't_jg_rate', 't_jg_rate_ratio', 't_online_count', 't_online_rate', 't_online_rate_ratio',
         't_ok_count', 't_never_gps_count', 't_never_alarm_count', 't_never_acc_count', 't_ok_rate',
         'last_ok_rate', 'ok_rate_ratio'
         ),
        ('全省平均', '-',
         ke_xjg_count, ke_jg_count, ke_jg_rate, ke_online_count, ke_online_rate,
         w_xjg_count, w_jg_count, w_jg_rate, w_online_count, w_online_rate,
         z_xjg_count, z_jg_count, z_jg_rate, z_online_count, z_online_rate,
         t_jg_count, t_jg_rate, '', t_online_count, t_online_rate, '',
         t_ok_count, t_never_gps_count, t_never_alarm_count, t_never_acc_count,
         t_ok_rate, '', ''
         )
    ))
    return ne


def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        e['ke_jg_rate'] = f"{round(e['ke_jg_rate'] * 100, 2)}%"
        e['ke_online_rate'] = f"{round(e['ke_online_rate'] * 100, 2)}%"
        e['w_jg_rate'] = f"{round(e['w_jg_rate'] * 100, 2)}%"
        e['w_online_rate'] = f"{round(e['w_online_rate'] * 100, 2)}%"
        e['z_jg_rate'] = f"{round(e['z_jg_rate'] * 100, 2)}%"
        e['z_online_rate'] = f"{round(e['z_online_rate'] * 100, 2)}%"
        e['t_jg_rate'] = f"{round(e['t_jg_rate'] * 100, 2)}%"
        e['t_online_rate'] = f"{round(e['t_online_rate'] * 100, 2)}%"
        e['t_ok_rate'] = f"{round(e['t_ok_rate'] * 100, 2)}%"
        ne = OrderedDict(zip(
            ('pid', 'city_name',
             'ke_xjg_count', 'ke_jg_count', 'ke_jg_rate', 'ke_online_count', 'ke_online_rate',
             'w_xjg_count', 'w_jg_count', 'w_jg_rate', 'w_online_count', 'w_online_rate',
             'z_xjg_count', 'z_jg_count', 'z_jg_rate', 'z_online_count', 'z_online_rate',
             't_jg_count', 't_jg_rate', 't_jg_rate_ratio', 't_online_count', 't_online_rate', 't_online_rate_ratio',
             't_ok_count', 't_never_gps_count', 't_never_alarm_count', 't_never_acc_count', 't_ok_rate',
             'last_ok_rate', 'ok_rate_ratio'
             ),
            (e['pid'], e['city_name'],
             e['ke_xjg_count'], e['ke_jg_count'], e['ke_jg_rate'], e['ke_online_count'], e['ke_online_rate'],
             e['w_xjg_count'], e['w_jg_count'], e['w_jg_rate'], e['w_online_count'], e['w_online_rate'],
             e['z_xjg_count'], e['z_jg_count'], e['z_jg_rate'], e['z_online_count'], e['z_online_rate'],
             e['t_jg_count'], e['t_jg_rate'], '', e['t_online_count'], e['t_online_rate'], '',
             e['t_ok_count'], e['t_never_gps_count'], e['t_never_alarm_count'], e['t_never_acc_count'],
             e['t_ok_rate'], '', ''
             )
        ))
        outlist.append(ne)
    return outlist




def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    outlist = orderByCity(datalist)
    outlist = export_outlist_orderDict(outlist)
    se = doSum(datalist)
    db.close()
    # h = list(outlist[0].keys())
    # otfile = join_path(bpath, 'test.csv')
    # outlist.append(se)
    # write_csv(otfile, h, outlist)
    return outlist,se

if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
