from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    third_sql = f'''
            select
                ba.city_short city_name,
                u.user_id,
                u.dept_id,
                d.id third_party_id,
    			d.name third_party_name,
                u.idcard,
                r.role_type,
                u.tenant_id,
                ifnull(L.login_days,0) login_days
            from gdispx.sys_user u
            left join gdispx.sys_user_role ur on u.user_id = ur.user_id
            left join gdispx.sys_role r on ur.role_id = r.role_id
            right join gdispx_basics.basics_third_party d on d.id = u.dept_id 
            left join gdispx_basics.basics_area ba on d.area_id = ba.id
            left join
            (
            select
            user_id,count(distinct date(create_time)) login_days
            from gdispx.sys_log_login
            where date(create_time) between '{start_day}' and '{end_day}'
            group by user_id
            ) L on L.user_id = u.user_id
            where u.tenant_id = 6
    				and d.is_delete = 0 
        '''
    v_sql = f'''
            select
            distinct vehicle_no,
            vehicle_color,
            vehicle_type,
            owner_id,
            owner_name,
            third_party_id,
            third_party_name
        from
            gdispx_data.v_vehicle_wide_day_all
        where
            inc_day BETWEEN '{start_day}' and '{end_day}'
        '''
    return third_sql, v_sql


def collect_third_party_report(inlist, vlist, days):
    tid_g = list_2_dictlist(vlist, 'third_party_id')
    tlist = list(filter(lambda o: o['tenant_id'] == 6, inlist))
    dp_g = list_2_dictlist(tlist, 'dept_id')
    outlist = []
    pid = 0
    for dept_id, g in dp_g.items():
        pid += 1
        id = g[0]['third_party_id']
        vn = len(tid_g.get(id, []))
        city_name = g[0]['city_name']
        name = g[0]['third_party_name']
        tlist1 = list(filter(lambda o: o['role_type'] == 1, g))
        n1 = len(tlist1)
        n2, n3 = 0, 0
        for e in tlist1:
            if e['login_days'] == days:
                n2 += 1
            elif e['login_days'] == 0:
                n3 += 1
        ne = OrderedDict(
            zip(
                ('pid', 'city_name', 'name', 't', 'vn',
                 'accountNum', 'open_accountNum',
                 'login_everyday', 'login_0day'),
                (pid, city_name, name, '', vn,
                 '', n1, n2, n3)
            )
        )
        outlist.append(ne)
    outlist = orderByCity(outlist)
    return outlist


def load_data(start_day, end_day, days):
    third_sql, v_sql = loadSQL(start_day, end_day, days)
    ck_db = DBUtil(DBType.CLICKHOUSE.value)
    vlist = ck_db.query_to_dictList(v_sql)
    ck_db.close()
    db = DBUtil(DBType.MYSQL.value)
    third_list = db.query_to_dictList(third_sql)
    db.close()
    outlist = collect_third_party_report(third_list, vlist, days)
    # h = list(outlist[0].keys())
    # otfile = join_path(bpath,'test.csv')
    # write_csv(otfile,h,outlist)
    return outlist


if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
