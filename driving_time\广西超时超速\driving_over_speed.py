import datetime
from itertools import groupby
from itertools import tee
import pandas as pd

def get_speed_limit(gps_time):
    """
    根据时间判断限速标准
    夜间（22:00-06:00）：80 km/h
    白天（其他时间）：100 km/h
    """
    hour = gps_time.hour
    if hour >= 22 or hour < 6:  # 夜间时段
        return 80
    else:  # 白天时段
        return 100

def is_over_speed(speed, gps_time):
    """
    判断是否超速
    """
    speed_limit = get_speed_limit(gps_time)
    return speed > speed_limit

files = ['X:\\track_report\\driving_time\\广西超时超速\\桂K66936车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KC4469车辆20250101-0624.csv',
         'X:\\track_report\\driving_time\\广西超时超速\\桂KH3499车辆20250101-0624.csv']

# 用于存储所有超速记录的列表
all_speeding_records = []

for file in files:
    df = pd.read_csv(file,engine='pyarrow')
    df['gps_time'] = pd.to_datetime(df['gps_time'])
    df = df.sort_values(by='gps_time')

    # 添加超速判断列
    df['is_over_speed'] = df.apply(lambda row: is_over_speed(row['speed'], row['gps_time']), axis=1)
    df['speed_limit'] = df['gps_time'].apply(get_speed_limit)

    total_driving_time = 0
    driver = {}

    # 按超速状态分组
    for is_speeding, group in groupby(df.iterrows(), key=lambda row: row[1]['is_over_speed']):
        itorMax, itorMin = tee(group, 2)

        maxItem = max(itorMax, key=lambda row: row[1]['gps_time'])
        minItem = min(itorMin, key=lambda row: row[1]['gps_time'])
        maxTime = maxItem[1]['gps_time']
        minTime = minItem[1]['gps_time']
        vehicleNo = maxItem[1]['vehicle_no']
        maxSpeed = maxItem[1]['speed']
        speedLimit = maxItem[1]['speed_limit']

        maxTimeStr = maxTime.strftime("%Y-%m-%d %H:%M:%S.000")
        minTimeStr = minTime.strftime("%Y-%m-%d %H:%M:%S.000")

        if is_speeding:
            duration = maxTime - minTime
            # 过滤掉超速时长小于30秒的记录
            if duration.total_seconds() > 30:
                # 将持续时长转换为x分y秒格式
                total_seconds = int(duration.total_seconds())
                minutes = total_seconds // 60
                seconds = total_seconds % 60
                duration_str = f"{minutes}分{seconds}秒"

                time_period = "夜间" if get_speed_limit(maxTime) == 80 else "白天"

                # 将超速记录添加到列表中
                speeding_record = {
                    '车辆号': vehicleNo,
                    '开始时间': minTimeStr,
                    '结束时间': maxTimeStr,
                    '持续时长': duration_str,
                    '时间段': time_period,
                    '限速(km/h)': speedLimit,
                    '最高速度(km/h)': maxSpeed
                }
                all_speeding_records.append(speeding_record)

# 将所有超速记录输出到CSV文件
if all_speeding_records:
    output_df = pd.DataFrame(all_speeding_records)
    output_file = 'X:\\track_report\\driving_time\\广西超时超速\\超速记录汇总.csv'
    output_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"超速记录已保存到: {output_file}")
    print(f"共发现 {len(all_speeding_records)} 条超速记录")
else:
    print("未发现符合条件的超速记录")