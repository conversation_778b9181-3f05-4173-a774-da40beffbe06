#!/usr/bin/env python
# -*- coding: utf-8 -*-
from multiprocessing import Process
from optparse import OptionParser
import sys
import math
import datetime
from cal import cal
from db import *


def get_user_paras():
    try:
        opt = OptionParser()
        opt.add_option('--imei_file',
                       dest="imei_file",
                       type=str,
                       help="the file of imei list")
        opt.add_option('--day',
                       dest='day',
                       type=str,
                       help='the statistic day, eg: 210801')
        opt.add_option('--thread_num',
                       dest="thread_num",
                       type=int,
                       default=8,
                       help="parallel thread num , default 8")
        (options, args) = opt.parse_args()
        is_valid_paras = True
        error_messages = []
        imei_file = options.imei_file
        day = options.day
        thread_num = options.thread_num
        # if not imei_file:
        #     error_messages.append("imei_file must be set;")
        #     is_valid_paras = False
        if not day:
            error_messages.append("day must be set;")
            is_valid_paras = False

        if is_valid_paras:
            user_paras = {"imei_file": imei_file, "day": day, "thread_num": thread_num}
            return user_paras
        else:
            for error_message in error_messages:
                print(error_message)
            opt.print_help()
            return None
    except Exception as ex:
        print(f"exception :{str(ex)}")
        return None


def read_imei(imei_file):
    imeis = []
    with open(imei_file, 'r') as f:
        for imei in f.readlines():
            imeis.append(imei.rstrip("\n"))
    return imeis


def read_imei_from_db(ck_client):
    imeis = []
    query_sql = """
    SELECT if(master_sim_code = '',AI_BOX_sim_code,master_sim_code) as imei
     from gdispx_data.v_regulator_vehicle_related_infos vrvri
    where ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
      or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
    """
    ret = ck_client.execute(query_sql)
    for items in ret:
        imeis.append(items[0])
    return imeis


def now_time():
    return datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')


GPS_TIME_FMT = '%y%m%d%H%M%S'


def query_track_infos(session, imei, day):
    rows = session.execute(query="select gps_time, latitude, longitude "
                                 "from dhiot.gps_data_{} where imei = '{}'".format(day, imei))
    track_points = []
    for row in rows:
        try:
            track_points.append({'x': row.longitude / 1000000, 'y': row.latitude / 1000000,
             'time': datetime.datetime.strptime(row.gps_time, GPS_TIME_FMT)})
        except:
            pass
    return track_points


def insert_results_to_ck(ck_client, items):
    insert_sql = "INSERT into gdispx_data.track_report (`imei`, `date`, " \
                 "`total_point_count`, `error_point_count`, `drift_point_count`, " \
                 "`continuous_mileage`, `total_mileage`) values"
    ck_client.execute(insert_sql, items)


BATCH_INSERT_SIZE = 10000


def run(imeis, day):
    cassandra_db = CassandraDB()
    try:
        cassandra_session = cassandra_db.get_session()
        ck_db = CkDB()
        try:
            ck_client = ck_db.get_client()
            items = []
            for imei in imeis:
                imei = imei.strip()
                try:
                    # 获取轨迹信息列表
                    track_points = query_track_infos(cassandra_session, imei, day)
                    if len(track_points) == 0:
                        continue
                    # 执行计算
                    ret = cal(track_points)
                    ret.update({'imei': imei, 'date': day})
                    items.append(ret)
                    if len(items) == BATCH_INSERT_SIZE:
                        # 结果写入数据库
                        insert_results_to_ck(ck_client, items)
                        items.clear()
                except:
                    print('设备号%s计算异常' % imei)
            if len(items) > 0:
                # 结果写入数据库
                insert_results_to_ck(ck_client, items)
        finally:
            ck_db.disconnect()
    finally:
        cassandra_db.shutdown()


def partition(big_list, size):
    """将大的list均分"""
    big_list_size = len(big_list)
    sub_list_list = []
    start = 0
    for i in range(size):
        sub_size = math.ceil((big_list_size - start) / (size - i))
        sub_list_list.append(big_list[start:start + sub_size])
        start += sub_size
        if start >= big_list_size:
            break
    return sub_list_list



def main(user_paras):
    imei_file = user_paras['imei_file']
    day = user_paras['day']
    thread_num = user_paras['thread_num']
    print('{}: begin execute imei_file={}, day={}, thread_num={}'.format(now_time(), imei_file, day, thread_num))
    # 1.加载设备号
    if imei_file:
        imei_list = read_imei(imei_file)
    else:
        ck_db = CkDB()
        try:
            ck_client = ck_db.get_client()
            imei_list = read_imei_from_db(ck_client)
        finally:
            ck_db.disconnect()
    # 2.根据进程数平均分配
    process_list = []
    for sub_imei_list in partition(imei_list, thread_num):
        process_list.append(Process(target=run, args=(sub_imei_list, day,)))
    # 3.启动进程
    for p in process_list:
        p.start()
    for p in process_list:
        p.join()
    print('{}: finish execute imei_file={}, day={}, thread_num={}'.format(now_time(), imei_file, day, thread_num))


if __name__ == '__main__':
    # 设备号文件, 日期, 线程数
    # user_paras = get_user_paras()
    # if user_paras is None:
    #     sys.exit(0)
    # main(user_paras)
    print('423')
    main({'day': '220423', 'imei_file': None, 'thread_num': 8})
    print('424')
    main({'day': '220424', 'imei_file': None, 'thread_num': 8})

