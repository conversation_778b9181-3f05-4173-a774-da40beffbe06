import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 尝试导入可选依赖
try:
    from scipy import signal
    from scipy.ndimage import gaussian_filter1d
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("注意: scipy未安装，部分高级滤波功能不可用")

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("注意: matplotlib未安装，绘图功能不可用")

class TrajectorySpeedSmoother:
    """
    轨迹速度平滑处理类
    用于处理GPS轨迹数据中的速度突变和噪声
    """
    
    def __init__(self, csv_file_path):
        """
        初始化速度平滑器
        
        Args:
            csv_file_path (str): CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.df = None
        self.original_speed = None
        self.smoothed_speed = None
        
    def load_data(self):
        """加载CSV数据"""
        try:
            self.df = pd.read_csv(self.csv_file_path)
            print(f"成功加载数据，共 {len(self.df)} 条记录")
            print(f"数据列: {list(self.df.columns)}")
            
            # 转换时间列为datetime
            if 'gps_time' in self.df.columns:
                self.df['gps_time'] = pd.to_datetime(self.df['gps_time'])
                self.df = self.df.sort_values('gps_time').reset_index(drop=True)
            
            self.original_speed = self.df['speed'].values
            print(f"速度范围: {self.original_speed.min():.2f} - {self.original_speed.max():.2f} km/h")
            return True
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def detect_speed_outliers(self, threshold_factor=2.5):
        """
        检测速度异常值
        
        Args:
            threshold_factor (float): 异常值检测阈值因子
            
        Returns:
            np.array: 异常值索引
        """
        if self.original_speed is None:
            raise ValueError("请先加载数据")
        
        # 使用IQR方法检测异常值
        Q1 = np.percentile(self.original_speed, 25)
        Q3 = np.percentile(self.original_speed, 75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - threshold_factor * IQR
        upper_bound = Q3 + threshold_factor * IQR
        
        outliers = np.where((self.original_speed < lower_bound) | 
                           (self.original_speed > upper_bound))[0]
        
        print(f"检测到 {len(outliers)} 个速度异常值")
        return outliers
    
    def moving_average_filter(self, window_size=5):
        """
        移动平均滤波
        
        Args:
            window_size (int): 窗口大小
            
        Returns:
            np.array: 平滑后的速度
        """
        if self.original_speed is None:
            raise ValueError("请先加载数据")
        
        # 使用pandas rolling方法进行移动平均
        smoothed = pd.Series(self.original_speed).rolling(
            window=window_size, center=True, min_periods=1
        ).mean().values
        
        print(f"移动平均滤波完成，窗口大小: {window_size}")
        return smoothed
    
    def gaussian_filter(self, sigma=1.0):
        """
        高斯滤波
        
        Args:
            sigma (float): 高斯核标准差
            
        Returns:
            np.array: 平滑后的速度
        """
        if self.original_speed is None:
            raise ValueError("请先加载数据")
        
        smoothed = gaussian_filter1d(self.original_speed, sigma=sigma)
        print(f"高斯滤波完成，sigma: {sigma}")
        return smoothed
    
    def median_filter(self, kernel_size=5):
        """
        中值滤波
        
        Args:
            kernel_size (int): 滤波核大小
            
        Returns:
            np.array: 平滑后的速度
        """
        if self.original_speed is None:
            raise ValueError("请先加载数据")
        
        smoothed = signal.medfilt(self.original_speed, kernel_size=kernel_size)
        print(f"中值滤波完成，核大小: {kernel_size}")
        return smoothed
    
    def savitzky_golay_filter(self, window_length=11, polyorder=3):
        """
        Savitzky-Golay滤波
        
        Args:
            window_length (int): 窗口长度（必须为奇数）
            polyorder (int): 多项式阶数
            
        Returns:
            np.array: 平滑后的速度
        """
        if self.original_speed is None:
            raise ValueError("请先加载数据")
        
        # 确保窗口长度为奇数且不超过数据长度
        if window_length % 2 == 0:
            window_length += 1
        window_length = min(window_length, len(self.original_speed))
        
        smoothed = signal.savgol_filter(self.original_speed, window_length, polyorder)
        print(f"Savitzky-Golay滤波完成，窗口长度: {window_length}, 多项式阶数: {polyorder}")
        return smoothed
    
    def butterworth_filter(self, cutoff_freq=0.1, order=4):
        """
        巴特沃斯低通滤波
        
        Args:
            cutoff_freq (float): 截止频率 (0-1之间)
            order (int): 滤波器阶数
            
        Returns:
            np.array: 平滑后的速度
        """
        if self.original_speed is None:
            raise ValueError("请先加载数据")
        
        # 设计巴特沃斯低通滤波器
        b, a = signal.butter(order, cutoff_freq, btype='low')
        smoothed = signal.filtfilt(b, a, self.original_speed)
        
        print(f"巴特沃斯滤波完成，截止频率: {cutoff_freq}, 阶数: {order}")
        return smoothed
    
    def combined_filter(self, methods=['gaussian', 'moving_average']):
        """
        组合滤波方法
        
        Args:
            methods (list): 要使用的滤波方法列表
            
        Returns:
            np.array: 平滑后的速度
        """
        if self.original_speed is None:
            raise ValueError("请先加载数据")
        
        smoothed = self.original_speed.copy()
        
        for method in methods:
            if method == 'gaussian':
                smoothed = gaussian_filter1d(smoothed, sigma=1.0)
            elif method == 'moving_average':
                smoothed = pd.Series(smoothed).rolling(
                    window=5, center=True, min_periods=1
                ).mean().values
            elif method == 'median':
                smoothed = signal.medfilt(smoothed, kernel_size=5)
            elif method == 'savgol':
                window_length = min(11, len(smoothed))
                if window_length % 2 == 0:
                    window_length += 1
                smoothed = signal.savgol_filter(smoothed, window_length, 3)
        
        print(f"组合滤波完成，使用方法: {methods}")
        return smoothed
    
    def apply_smoothing(self, method='gaussian', **kwargs):
        """
        应用指定的平滑方法
        
        Args:
            method (str): 平滑方法
            **kwargs: 方法参数
            
        Returns:
            np.array: 平滑后的速度
        """
        if method == 'moving_average':
            self.smoothed_speed = self.moving_average_filter(**kwargs)
        elif method == 'gaussian':
            self.smoothed_speed = self.gaussian_filter(**kwargs)
        elif method == 'median':
            self.smoothed_speed = self.median_filter(**kwargs)
        elif method == 'savgol':
            self.smoothed_speed = self.savitzky_golay_filter(**kwargs)
        elif method == 'butterworth':
            self.smoothed_speed = self.butterworth_filter(**kwargs)
        elif method == 'combined':
            self.smoothed_speed = self.combined_filter(**kwargs)
        else:
            raise ValueError(f"不支持的平滑方法: {method}")
        
        return self.smoothed_speed
    
    def calculate_metrics(self):
        """计算平滑效果指标"""
        if self.smoothed_speed is None:
            raise ValueError("请先应用平滑方法")
        
        # 计算速度变化率
        original_diff = np.abs(np.diff(self.original_speed))
        smoothed_diff = np.abs(np.diff(self.smoothed_speed))
        
        metrics = {
            '原始速度标准差': np.std(self.original_speed),
            '平滑后速度标准差': np.std(self.smoothed_speed),
            '原始速度变化率均值': np.mean(original_diff),
            '平滑后速度变化率均值': np.mean(smoothed_diff),
            '平滑度改善比例': (np.mean(original_diff) - np.mean(smoothed_diff)) / np.mean(original_diff) * 100
        }
        
        return metrics
    
    def plot_comparison(self, save_path=None):
        """绘制原始速度与平滑后速度的对比图"""
        if self.smoothed_speed is None:
            raise ValueError("请先应用平滑方法")
        
        plt.figure(figsize=(15, 8))
        
        # 时间轴
        if 'gps_time' in self.df.columns:
            time_axis = self.df['gps_time']
            xlabel = '时间'
        else:
            time_axis = range(len(self.original_speed))
            xlabel = '数据点索引'
        
        plt.subplot(2, 1, 1)
        plt.plot(time_axis, self.original_speed, 'b-', alpha=0.7, label='原始速度')
        plt.plot(time_axis, self.smoothed_speed, 'r-', linewidth=2, label='平滑后速度')
        plt.ylabel('速度 (km/h)')
        plt.title('速度对比图')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 1, 2)
        speed_diff = self.original_speed - self.smoothed_speed
        plt.plot(time_axis, speed_diff, 'g-', alpha=0.7)
        plt.ylabel('速度差值 (km/h)')
        plt.xlabel(xlabel)
        plt.title('原始速度与平滑速度的差值')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        
        plt.show()
    
    def save_results(self, output_path):
        """保存处理结果到CSV文件"""
        if self.smoothed_speed is None:
            raise ValueError("请先应用平滑方法")
        
        result_df = self.df.copy()
        result_df['original_speed'] = self.original_speed
        result_df['smoothed_speed'] = self.smoothed_speed
        result_df['speed_difference'] = self.original_speed - self.smoothed_speed
        
        result_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {output_path}")


def main():
    """主函数示例"""
    # 创建速度平滑器实例
    smoother = TrajectorySpeedSmoother('drivingtime.csv')
    
    # 加载数据
    if not smoother.load_data():
        return
    
    # 检测异常值
    outliers = smoother.detect_speed_outliers()
    
    # 应用不同的平滑方法
    print("\n=== 测试不同平滑方法 ===")
    
    methods = [
        ('moving_average', {'window_size': 5}),
        ('median', {'kernel_size': 7}),
    ]
    
    for method_name, params in methods:
        print(f"\n--- {method_name} 方法 ---")
        smoother.apply_smoothing(method_name, **params)
        metrics = smoother.calculate_metrics()
        # 绘制对比图
        smoother.plot_comparison(f'{method_name}:speed_comparison.png')
    
        # 保存结果
        for key, value in metrics.items():
            print(f"{key}: {value:.2f}")
    
    



if __name__ == "__main__":
    main()
