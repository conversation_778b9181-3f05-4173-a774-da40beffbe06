from yyutils import *
from utils.YYSQLTemplate import YY<PERSON><PERSON>Template
from utils.config import WEEK_COUNT, orderByCity, FLOORtoPercent
from collections import OrderedDict


def loadSQL(start_day, end_day):
    avg_drift_sql = YYSQLTemplate.calTrackAvgDriftPoints(start_day, end_day)
    xjg_sql = YYSQLTemplate.xjg_stats([],
                                      "city_id",
                                      " toInt32(city_id) city_id, count(distinct vehicle_no,vehicle_color) xjg_count ",
                                      start_day, end_day)

    v_jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([], "vehicle_no,vehicle_color,city_id",
                                                                    '''
                                                                    city_id,vehicle_no,vehicle_color,any(city_short_name) city_name,
                                                                    sum(risk_oneLevel) risk_oneLevel , sum(risk_twoLevel) risk_twoLevel,sum(risk_threeLevel) risk_threeLevel,
                                                                    max(online) online, 
                                                                    if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, 0,1) is_ok,
                                                                    if( sum(drift_point_count) > avg_drift_point_count ,1,0) is_high_drift,
                                                                    sum(total_mileage) total_mileage,
                                                                    sum(continuous_mileage) continuous_mileage
                                                                    ''', start_day=start_day, end_day=end_day)
    jg_sql = SQLHelper().table(v_jg_sql) \
        .groupBy('city_id').select('''
            city_id,any(city_name) city_name,
            count(1) jg_count,
            sum(risk_oneLevel) risk_oneLevel_count,
            sum(risk_twoLevel) risk_twoLevel_count,
            sum(risk_threeLevel) risk_threeLevel_count,
            sum(online) online_count,
            sum(is_ok) ok_count,
            sum(is_high_drift) drift_count,
            sum(total_mileage) total_mileage,
            sum(continuous_mileage) continuous_mileage
        ''')
    xjg_jg_sql = SQLHelper().table(jg_sql, 'B') \
        .leftJoin().table(xjg_sql, 'A').on('A.city_id=B.city_id') \
        .select('''
            A.city_id city_id,
            city_name,
            if(xjg_count>jg_count,xjg_count,jg_count) xjg_count,
            jg_count,
            risk_oneLevel_count,
            risk_twoLevel_count,
            risk_threeLevel_count,
            online_count,
            ok_count,
            drift_count,
            total_mileage,
            continuous_mileage,
            ROUND(risk_oneLevel_count/{days}/online_count,2) risk_oneLevel_rate,
            ROUND(risk_twoLevel_count/{days}/online_count,2) risk_twoLevel_rate,
            ROUND(risk_threeLevel_count/{days}/online_count,2) risk_threeLevel_rate,
            FLOOR(jg_count/xjg_count,4) jg_rate,
            FLOOR(online_count/jg_count,4) online_rate,
            FLOOR(drift_count/online_count,4) drift_rate,
            FLOOR(continuous_mileage/total_mileage,4) track_rate,
            FLOOR(ok_count/jg_count,4) ok_rate
        ''')
    return avg_drift_sql, xjg_jg_sql


def doSum(xjg_jg_list):
    total_xjg = sum([e['xjg_count'] for e in xjg_jg_list])
    total_jg = sum([e['jg_count'] for e in xjg_jg_list])
    total_online = sum([e['online_count'] for e in xjg_jg_list])
    total_ok = sum([e['ok_count'] for e in xjg_jg_list])
    total_drift = sum([e['drift_count'] for e in xjg_jg_list])
    total_mileage = sum([e['total_mileage'] for e in xjg_jg_list])
    continuous_mileage = sum([e['continuous_mileage'] for e in xjg_jg_list])
    risk_oneLevel_count = sum([e['risk_oneLevel_count'] for e in xjg_jg_list])
    risk_twoLevel_count = sum([e['risk_twoLevel_count'] for e in xjg_jg_list])
    risk_threeLevel_count = sum([e['risk_threeLevel_count'] for e in xjg_jg_list])
    ne = OrderedDict(zip(
        ('pid', 'city_name', 'risk_oneLevel_rate', 'risk_twoLevel_rate', 'risk_threeLevel_rate',
         'jg_rate', 'online_rate', 'drift_rate', 'track_rate', 'ok_rate'),
        ('全省', '-', round(risk_oneLevel_count / WEEK_COUNT / total_online, 2) if total_online else '-',
         round(risk_twoLevel_count / WEEK_COUNT / total_online, 2) if total_online else '-',
         round(risk_threeLevel_count / WEEK_COUNT / total_online, 2) if total_online else '-',
         FLOORtoPercent(total_jg / total_xjg),
         FLOORtoPercent(total_online / total_jg),
         FLOORtoPercent(total_drift / total_online),
         FLOORtoPercent(continuous_mileage / total_mileage),
         FLOORtoPercent(total_ok / total_jg)
         )
    ))
    return ne


def export_outlist_orderDict(xjg_jg_list):
    outlist = []
    for e in xjg_jg_list:
        e['jg_rate'] = f"{round(e['jg_rate'] * 100, 2)}%"
        e['online_rate'] = f"{round(e['online_rate'] * 100, 2)}%"
        e['drift_rate'] = f"{round(e['drift_rate'] * 100, 2)}%"
        e['track_rate'] = f"{round(e['track_rate'] * 100, 2)}%"
        e['ok_rate'] = f"{round(e['ok_rate'] * 100, 2)}%"
        ne = OrderedDict(zip(
            ('pid', 'city_name', 'risk_oneLevel_rate', 'risk_twoLevel_rate', 'risk_threeLevel_rate',
             'jg_rate', 'online_rate', 'drift_rate', 'track_rate', 'ok_rate'),
            (e['pid'], e['city_name'], e['risk_oneLevel_rate'], e['risk_twoLevel_rate'],
             e['risk_threeLevel_rate'], e['jg_rate'], e['online_rate'], e['drift_rate'],
             e['track_rate'], e['ok_rate'])
        ))
        outlist.append(ne)
    return outlist


def main(start_day, end_day):
    avg_drift_sql, xjg_jg_sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    avg_drift_list = db.query_to_dictList(avg_drift_sql)
    avg_drift_point_count = avg_drift_list[0]['average_drift_num']
    xjg_jg_sql = xjg_jg_sql.format(days=WEEK_COUNT).replace('avg_drift_point_count', str(avg_drift_point_count))
    xjg_jg_list = db.query_to_dictList(xjg_jg_sql)
    xjg_jg_list = list(filter(lambda o: o['city_name'], xjg_jg_list))
    db.close()
    se = doSum(xjg_jg_list)
    xjg_jg_list = orderByCity(xjg_jg_list)
    riskList = export_outlist_orderDict(xjg_jg_list)
    return riskList, se


if __name__ == '__main__':
    start, end = TimeUtils.getLastWeekRange()
    main(start, end)
