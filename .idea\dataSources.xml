<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="ck@jtt-lkyw@test" uuid="eeb503db-4ca4-494c-a107-b05afef7fd7b">
      <driver-ref>clickhouse</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.clickhouse.jdbc.ClickHouseDriver</jdbc-driver>
      <jdbc-url>********************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>