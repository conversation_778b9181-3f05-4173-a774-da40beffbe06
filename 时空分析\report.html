<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广东省营运车辆风险与轨迹时空分析报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/heatmapjs@2.0.2/heatmap.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f4f8;
        }
        .chart-container {
            position: relative;
            width: 100%;
            height: 350px;
            max-height: 350px;
            margin-left: auto;
            margin-right: auto;
        }
        #heatmapContainer {
            width: 100%;
            height: 100%;
            background-image: url('https://placehold.co/600x400/f0f4f8/374151?text=%E5%B9%BF%E4%B8%9C%E7%9C%81%E5%9C%B0%E5%9B%BE%E8%BD%AE%E5%BB%93');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
        }
        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
                max-height: 300px;
            }
        }
        .flow-line {
            position: absolute;
            background-color: #A3C7FF;
            transform-origin: 0 0;
            z-index: 0;
        }
        .flow-city {
            z-index: 20;
        }
        .kpi-card {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

<div class="container mx-auto p-4 md:p-8">
    <header class="text-center mb-12">
        <h1 class="text-4xl md:text-5xl font-bold text-[#004AAD] mb-2">广东省营运车辆风险与轨迹时空分析</h1>
        <p class="text-lg text-gray-600">洞察动态风险，赋能智慧交通 (数据截止：2024年底)</p>
    </header>

    <main class="space-y-16">

        <section id="overview">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#0A6BFF]">一、 市场概览：规模、增长与分布</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="kpi-card lg:col-span-1">
                    <h3 class="text-xl font-semibold mb-2">保有量</h3>
                    <p class="text-6xl font-bold text-[#004AAD]">480<span class="text-3xl ml-2">万辆</span></p>
                    <p class="text-gray-500 mt-2 text-sm">庞大的车辆基数对安全管理提出更高要求。</p>
                </div>
                <div class="kpi-card lg:col-span-1">
                    <h3 class="text-xl font-semibold mb-2">年均增长</h3>
                    <p class="text-6xl font-bold text-[#004AAD]">5.8<span class="text-3xl ml-2">%</span></p>
                    <p class="text-gray-500 mt-2 text-sm">市场规模持续增长，潜在风险同步上升。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">近五年保有量增长趋势</h3>
                    <div class="chart-container h-64 max-h-64"><canvas id="vehicleGrowthChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">营运车辆保有量呈持续线性上升趋势，表明运输需求日益增长。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 md:col-span-1 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">主要城市车辆保有量TOP 5</h3>
                    <div class="chart-container"><canvas id="cityVehicleRankChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">深圳与广州保有量遥遥领先，是全省车辆管理的重中之重。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 md:col-span-1 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">营运车辆类型构成</h3>
                    <div class="chart-container"><canvas id="vehicleTypeChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">货运车辆占据绝对主体，是风险管控的主要对象。危化品车虽占比小但风险高。</p>
                </div>
            </div>
        </section>

        <section id="risk-landscape">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#0A6BFF]">二、 风险全景透视</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">主要风险驾驶行为年度统计</h3>
                    <div class="chart-container"><canvas id="riskBehaviorChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">超速行驶是最高发的风险行为，其数量远超其他类型，是事故首要诱因。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">风险事件月度变化趋势</h3>
                    <div class="chart-container"><canvas id="riskTrendChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">年底运输高峰期及节假日（春节）前后，风险事件呈现明显峰值。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">风险事件24小时分布规律</h3>
                    <div class="chart-container"><canvas id="riskByHourChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">风险呈早晚双高峰，下午15-18点为全天最高峰。夜间风险同样值得警惕。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">不同道路类型风险事件占比</h3>
                    <div class="chart-container"><canvas id="riskByRoadTypeChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">高速公路是风险事件最主要场所，占比超40%，与车速快、易疲劳等因素相关。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">各类营运车辆风险指数对比</h3>
                    <div class="chart-container"><canvas id="riskByVehicleTypeChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">危险品车与重型货车在疲劳、急变动和事故率上风险指数更高，需重点监管。</p>
                </div>
            </div>
        </section>

        <section id="hotspots">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#0A6BFF]">三、 时空热点演化</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-6 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">广东省营运车辆风险密度热力图</h3>
                    <div class="chart-container h-96 max-h-96">
                        <div id="heatmapContainer"></div>
                    </div>
                    <p class="text-gray-500 mt-4 text-center text-sm">热力图直观展示了风险事件在地理空间上的高度聚集性，热点主要集中在珠三角核心城市群，特别是交通大动脉沿线。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">珠三角风险热点演变模式</h3>
                    <div class="chart-container"><canvas id="hotspotBubbleChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">广深呈现“持续热点”，风险长期存在；莞佛为“增强热点”，风险加剧需关注。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">全省风险热点类型分布</h3>
                    <div class="chart-container"><canvas id="hotspotTypeDistributionChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">“持续”与“增强”热点占比最高，说明部分区域交通安全问题具长期性和恶化趋势。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">主要城市热点强度(Z-Score)对比</h3>
                    <div class="chart-container"><canvas id="hotspotIntensityChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">东莞、广州、深圳等珠三角核心城市的风险集聚程度最高，监管资源需重点倾斜。</p>
                </div>
            </div>
        </section>

        <section id="trajectories">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#0A6BFF]">四、 轨迹与廊道分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-8">
                    <h3 class="text-xl font-semibold mb-8 text-center">广东省三大核心物流运输廊道示意</h3>
                    <div id="flow-diagram" class="relative w-full h-80"></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">以广深、广佛、广莞为核心的三大廊道承载了主要货运流量，也是风险高发带。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">热门运输OD(起终点)线路 TOP 10</h3>
                    <div class="chart-container"><canvas id="topODPairsChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">广深线流量最大。珠三角核心城市间的短途运输构成了路网的主要交通压力。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">长途货运单次行驶里程分布</h3>
                    <div class="chart-container"><canvas id="tripDistanceDistributionChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">货运里程集中在101-200公里的中短途，与珠三角高效物流配送需求相符。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">TOP 5 长途货运热门停靠服务区</h3>
                    <div class="chart-container"><canvas id="topRestStopsChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">G4瓦窑岗服务区最繁忙。热门停靠点是疲劳驾驶干预和安全宣传的关键节点。</p>
                </div>
            </div>
        </section>

        <section id="driver-insights">
            <h2 class="text-xl md:text-3xl font-bold text-center mb-8 text-[#0A6BFF]">五、 驾驶员与企业洞察</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">风险指数与连续驾驶时长的关系</h3>
                    <div class="chart-container"><canvas id="riskVsDrivingHoursChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">连续驾驶超4小时后，风险指数急剧增高，是疲劳驾驶的直接数据体现。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">不同年龄段驾驶员平均风险分</h3>
                    <div class="chart-container"><canvas id="riskByAgeGroupChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">风险呈“两头高，中间低”：年轻驾驶员因经验不足，年长驾驶员因反应下降。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">运输企业安全评分对比 (抽样)</h3>
                    <div class="chart-container"><canvas id="companySafetyScoreChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">企业安全管理水平差异显著，加强企业监管是降低整体风险的关键抓手。</p>
                </div>
            </div>
        </section>

        <section id="environmental-factors">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#0A6BFF]">六、 环境因素与预测分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">不同天气下的风险事件发生率</h3>
                    <div class="chart-container"><canvas id="riskByWeatherChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">雨天和雾天等恶劣天气会导致风险事件显著增加，尤其在事故发生率上。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">2025年度风险事件月度预测</h3>
                    <div class="chart-container"><canvas id="riskForecastChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">预测显示2025年风险走势与2024年基本一致，年底高峰尤其值得警惕。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-center">2025年Q1季度TOP 10高风险路段预测</h3>
                    <div class="overflow-x-auto">
                        <table class="w-full text-left table-auto">
                            <thead class="bg-gray-100 text-gray-600">
                            <tr><th class="p-3">排名</th><th class="p-3">路段名称</th><th class="p-3">城市</th><th class="p-3">主要风险类型</th><th class="p-3">预测风险指数</th></tr>
                            </thead>
                            <tbody>
                            <tr class="border-b"><td class="p-3">1</td><td class="p-3">G4京港澳高速-广州机场段</td><td class="p-3">广州</td><td class="p-3">拥堵、追尾</td><td class="p-3 font-semibold text-red-600">95.2</td></tr>
                            <tr class="border-b"><td class="p-3">2</td><td class="p-3">G15沈海高速-东莞长安段</td><td class="p-3">东莞</td><td class="p-3">超速、违规变道</td><td class="p-3 font-semibold text-red-500">93.8</td></tr>
                            <tr class="border-b"><td class="p-3">3</td><td class="p-3">S82佛山一环-南庄段</td><td class="p-3">佛山</td><td class="p-3">货车事故</td><td class="p-3 font-semibold text-red-500">91.5</td></tr>
                            <tr class="border-b"><td class="p-3">4</td><td class="p-3">G94珠三角环线-深圳宝安段</td><td class="p-3">深圳</td><td class="p-3">疲劳驾驶</td><td class="p-3 font-semibold text-orange-500">89.7</td></tr>
                            <tr class="border-b"><td class="p-3">5</td><td class="p-3">G25长深高速-惠州段</td><td class="p-3">惠州</td><td class="p-3">超速</td><td class="p-3 font-semibold text-orange-500">88.1</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="text-gray-500 mt-4 text-center text-sm">该列表为实施“预测性警务”和前瞻性道路安全管理提供了直接的目标清单。</p>
                </div>
            </div>
        </section>

        <section id="key-road-analysis">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#0A6BFF]">七、 重点路段深度剖析：G4京港澳高速（广州机场段）</h2>
            <p class="text-center max-w-3xl mx-auto mb-8 text-gray-600">作为连接广州市区与白云国际机场的核心通道，G4京港澳高速的机场段车流量巨大、交通状况复杂，是本报告识别出的持续性高风险路段。本章节将对其风险特征进行深度剖析。</p>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">主要风险类型构成</h3>
                    <div class="chart-container"><canvas id="g4RiskCompositionChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">与全省平均水平不同，该路段的首要风险是“拥堵缓行”及其引发的“急加速/减速”，合计占比超过65%，追尾风险高。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">风险时段分布 (24小时)</h3>
                    <div class="chart-container"><canvas id="g4RiskByHourChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">风险高度集中在白天的航班高峰期，特别是08-11时和15-19时，与通勤高峰叠加，形成持续高压态势。</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-center">涉险车辆类型分析</h3>
                    <div class="chart-container"><canvas id="g4RiskByVehicleTypeChart"></canvas></div>
                    <p class="text-gray-500 mt-4 text-center text-sm">小型客车（包括私家车、网约车）是该路段最主要的涉险车辆类型，占比超60%，这与机场客流的交通模式高度相关。</p>
                </div>
            </div>
        </section>

        <section id="recommendations">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#0A6BFF]">八、 决策赋能与未来展望</h2>
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h3 class="text-xl font-semibold mb-6 text-center">综合管理对策与建议</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-gray-700">
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">🎯</span> <div><h4 class="font-semibold">精准化动态监管</h4><p class="text-sm">针对“新增”与“增强”热点，动态调整警力与电子警察布设。</p></div> </div>
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">🔧</span> <div><h4 class="font-semibold">技术赋能预警</h4><p class="text-sm">推广疲劳驾驶及恶劣天气下的智能监测提醒技术，向高风险车辆实时预警。</p></div> </div>
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">🚚</span> <div><h4 class="font-semibold">优化廊道管理</h4><p class="text-sm">在核心运输廊道优化配套设施，研究错峰运输引导策略，缓解拥堵与风险。</p></div> </div>
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">👨‍🏫</span> <div><h4 class="font-semibold">强化驾驶员培训</h4><p class="text-sm">针对不同年龄段和风险行为特征，开展个性化安全教育。</p></div> </div>
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">🏢</span> <div><h4 class="font-semibold">落实企业责任</h4><p class="text-sm">建立运输企业安全评分体系，实施奖优罚劣，督促企业加强内部管理。</p></div> </div>
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">🌦️</span> <div><h4 class="font-semibold">完善恶劣天气应对</h4><p class="text-sm">建立风险评估与分级响应机制，及时采取限速、限行等措施。</p></div> </div>
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">🔮</span> <div><h4 class="font-semibold">发展预测性警务</h4><p class="text-sm">基于风险预测模型，提前对高风险路段和时段进行干预和资源布防。</p></div> </div>
                    <div class="flex items-start p-2 bg-gray-50 rounded-lg"> <span class="text-2xl mr-3 text-[#0A6BFF]">📊</span> <div><h4 class="font-semibold">构建一体化数据平台</h4><p class="text-sm">整合多源时空数据，构建全省统一的风险动态监测与智能决策支持平台。</p></div> </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="text-center mt-16 py-6 border-t border-gray-200">
        <p class="text-gray-500">&copy; 2025 广东省智慧交通数据分析中心. All Rights Reserved.</p>
    </footer>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {

        const brilliantBlues = {
            primary: '#004AAD',
            secondary: '#0A6BFF',
            tertiary: '#69A5FF',
            light: '#A3C7FF',
            background: 'rgba(10, 107, 255, 0.2)',
            font: '#374151'
        };

        const extendedPalette = [
            brilliantBlues.primary, brilliantBlues.secondary, brilliantBlues.tertiary, '#FF6384', '#FF9F40', '#4BC0C0', '#9966FF', '#FFCD56', '#C9CBCF'
        ];

        const tooltipCallback = {
            plugins: {
                legend: { labels: { color: brilliantBlues.font, font: { size: 12 }}},
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) { return label.join(' '); }
                            return label;
                        }
                    }
                }
            }
        };

        const defaultChartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: { beginAtZero: true, grid: { color: '#e5e7eb' }, ticks: { color: brilliantBlues.font } },
                x: { grid: { display: false }, ticks: { color: brilliantBlues.font, maxRotation: 45, minRotation: 0 } }
            },
            plugins: { ...tooltipCallback.plugins, legend: { display: false } }
        };

        // --- All Charts Initialization ---
        if (document.getElementById('vehicleGrowthChart')) new Chart(document.getElementById('vehicleGrowthChart'), { type: 'line', data: { labels: ['2020', '2021', '2022', '2023', '2024'], datasets: [{ label: '保有量 (万辆)', data: [395, 418, 435, 458, 480], fill: true, backgroundColor: brilliantBlues.background, borderColor: brilliantBlues.secondary, tension: 0.1 }] }, options: defaultChartOptions });
        if (document.getElementById('cityVehicleRankChart')) new Chart(document.getElementById('cityVehicleRankChart'), { type: 'bar', data: { labels: ['深圳', '广州', '东莞', '佛山', '惠州'], datasets: [{ label: '保有量 (万辆)', data: [85, 82, 65, 60, 45], backgroundColor: brilliantBlues.tertiary, borderColor: brilliantBlues.secondary, borderWidth: 1, borderRadius: 5 }] }, options: defaultChartOptions });
        if (document.getElementById('vehicleTypeChart')) new Chart(document.getElementById('vehicleTypeChart'), { type: 'doughnut', data: { labels: ['中/轻型货车', '重型货车', '长途客运', '危险品运输车', '其他'], datasets: [{ data: [45, 35, 8, 5, 7], backgroundColor: extendedPalette, borderColor: '#fff', borderWidth: 4 }] }, options: { ...defaultChartOptions, plugins: { ...tooltipCallback.plugins, legend: { display: true, position: 'right' }}}});
        if (document.getElementById('riskBehaviorChart')) new Chart(document.getElementById('riskBehaviorChart'), { type: 'bar', data: { labels: ['超速行驶', '疲劳驾驶', '急加速/急减速', '违规变道', '其他'], datasets: [{ label: '年度风险事件数 (万件)', data: [120, 85, 65, 50, 30], backgroundColor: brilliantBlues.background, borderColor: brilliantBlues.secondary, borderWidth: 2, borderRadius: 5 }] }, options: defaultChartOptions });
        if (document.getElementById('riskTrendChart')) new Chart(document.getElementById('riskTrendChart'), { type: 'line', data: { labels: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'], datasets: [{ label: '风险事件指数', data: [88, 95, 75, 80, 85, 90, 70, 75, 98, 110, 105, 120], fill: true, backgroundColor: brilliantBlues.background, borderColor: brilliantBlues.secondary, tension: 0.4, pointRadius: 3 }] }, options: defaultChartOptions });
        if (document.getElementById('riskByHourChart')) new Chart(document.getElementById('riskByHourChart'), { type: 'bar', data: { labels: Array.from({length: 24}, (_, i) => `${i}`.padStart(2, '0')), datasets: [{ label: '风险事件相对频率', data: [8,6,5,4,6,8,15,25,30,28,22,18,16,20,24,35,40,32,25,28,33,20,15,10], backgroundColor: brilliantBlues.secondary, borderRadius: 3 }] }, options: {...defaultChartOptions, scales: { ...defaultChartOptions.scales, x: { ...defaultChartOptions.scales.x, ticks: { ...defaultChartOptions.scales.x.ticks, stepSize: 2 }}} }});
        if (document.getElementById('riskByRoadTypeChart')) new Chart(document.getElementById('riskByRoadTypeChart'), { type: 'doughnut', data: { labels: ['高速公路', '普通国道/省道', '城市快速路', '城市主干道', '其他'], datasets: [{ data: [42, 28, 15, 10, 5], backgroundColor: extendedPalette, borderColor: '#fff', borderWidth: 4 }] }, options: { ...defaultChartOptions, plugins: { ...tooltipCallback.plugins, legend: { display: true, position: 'bottom' } } } });
        if (document.getElementById('riskByVehicleTypeChart')) new Chart(document.getElementById('riskByVehicleTypeChart'), { type: 'radar', data: { labels: ['超速', '疲劳', '急变动', '违章', '事故率'], datasets: [{ label: '危险品车', data: [7, 9, 6, 8, 7], fill: true, backgroundColor: 'rgba(255, 99, 132, 0.2)', borderColor: 'rgb(255, 99, 132)'}, { label: '重型货车', data: [8, 8, 9, 6, 8], fill: true, backgroundColor: 'rgba(54, 162, 235, 0.2)', borderColor: 'rgb(54, 162, 235)' }, { label: '长途客车', data: [6, 7, 5, 7, 6], fill: true, backgroundColor: 'rgba(255, 205, 86, 0.2)', borderColor: 'rgb(255, 205, 86)' }] }, options: { responsive: true, maintainAspectRatio: false, scales: { r: { angleLines: { color: '#e5e7eb' }, grid: { color: '#e5e7eb' }, pointLabels: { font: { size: 12 }, color: brilliantBlues.font }, ticks: { backdropColor: 'white', color: brilliantBlues.font } } }, plugins: { ...tooltipCallback.plugins, legend: { display: true, position: 'top' } } } });
        if (document.getElementById('hotspotBubbleChart')) new Chart(document.getElementById('hotspotBubbleChart'), { type: 'bubble', data: { labels: ['广州', '深圳', '东莞', '佛山', '惠州', '珠海'], datasets: [{ label: '持续热点', data: [{x: 20, y: 60, r: 25}], backgroundColor: 'rgba(217, 39, 39, 0.7)' }, { label: '持续热点', data: [{x: 75, y: 55, r: 22}], backgroundColor: 'rgba(217, 39, 39, 0.7)' }, { label: '增强热点', data: [{x: 55, y: 65, r: 20}], backgroundColor: 'rgba(255, 128, 0, 0.7)' }, { label: '增强热点', data: [{x: 30, y: 40, r: 18}], backgroundColor: 'rgba(255, 128, 0, 0.7)' }, { label: '新增热点', data: [{x: 80, y: 30, r: 15}], backgroundColor: 'rgba(255, 191, 0, 0.7)' }, { label: '零星热点', data: [{x: 60, y: 20, r: 12}], backgroundColor: 'rgba(179, 179, 179, 0.7)' }] }, options: { responsive: true, maintainAspectRatio: false, scales: { y: { display: false, min: 0, max: 100}, x: { display: false, min: 0, max: 100 } }, plugins: { ...tooltipCallback.plugins, tooltip: { callbacks: { label: (c) => `${c.dataset.label}: ${c.chart.data.labels[c.datasetIndex]}`, title: () => null }}, legend: { position: 'bottom' } } }});
        if (document.getElementById('hotspotTypeDistributionChart')) new Chart(document.getElementById('hotspotTypeDistributionChart'), { type: 'pie', data: { labels: ['持续热点', '增强热点', '新增热点', '零星热点', '减弱热点', '其他'], datasets: [{ data: [25, 20, 15, 18, 12, 10], backgroundColor: extendedPalette, borderColor: '#fff', borderWidth: 2 }] }, options: { ...defaultChartOptions, plugins: { ...tooltipCallback.plugins, legend: { display: true, position: 'bottom' }}}});
        if (document.getElementById('hotspotIntensityChart')) new Chart(document.getElementById('hotspotIntensityChart'), { type: 'bar', data: { labels: ['东莞', '广州', '深圳', '佛山', '中山'], datasets: [{ label: '平均Z-Score', data: [3.8, 3.5, 3.4, 3.1, 2.8], backgroundColor: brilliantBlues.tertiary, borderRadius: 5 }] }, options: defaultChartOptions });
        const topODData = { labels: ['广-深', '广-佛', '广-莞', '深-莞', '佛-中', '深-惠', '莞-惠', '珠-中', '广-清', '佛-肇'], datasets: [{ label: '相对流量', data: [100, 95, 92, 85, 70, 68, 65, 55, 50, 48], backgroundColor: brilliantBlues.secondary, borderRadius: 3 }]};
        if (document.getElementById('topODPairsChart')) new Chart(document.getElementById('topODPairsChart'), { type: 'bar', data: topODData, options: { ...defaultChartOptions, indexAxis: 'y', scales: { x: { beginAtZero: true, grid: { color: '#e5e7eb' }, ticks: { color: brilliantBlues.font } }, y: { grid: { display: false }, ticks: { color: brilliantBlues.font } } } }});
        if (document.getElementById('tripDistanceDistributionChart')) new Chart(document.getElementById('tripDistanceDistributionChart'), { type: 'bar', data: { labels: ['0-50', '51-100', '101-200', '201-400', '401-600', '600+'], datasets: [{ label: '行程次数占比(%)', data: [15, 25, 35, 15, 7, 3], backgroundColor: brilliantBlues.background, borderColor: brilliantBlues.secondary, borderWidth: 1}] }, options: defaultChartOptions });
        if (document.getElementById('topRestStopsChart')) new Chart(document.getElementById('topRestStopsChart'), { type: 'bar', data: { labels: ['G4-瓦窑岗服务区', 'G15-梁金山服务区', 'G25-泰美服务区', 'S2-火村服务区', 'G15-民众服务区'], datasets: [{ label: '日均停靠车次', data: [2500, 2200, 2150, 1900, 1800], backgroundColor: brilliantBlues.tertiary, borderRadius: 5 }] }, options: defaultChartOptions });
        if (document.getElementById('riskVsDrivingHoursChart')) new Chart(document.getElementById('riskVsDrivingHoursChart'), { type: 'scatter', data: { datasets: [{ label: '风险指数', data: [{x:0.5,y:15},{x:1,y:18},{x:1.5,y:22},{x:2,y:25},{x:2.5,y:28},{x:3,y:35},{x:3.5,y:45},{x:4,y:60},{x:4.5,y:75},{x:5,y:85}], backgroundColor: brilliantBlues.secondary }] }, options: { ...defaultChartOptions, scales: { ...defaultChartOptions.scales, x: { ...defaultChartOptions.scales.x, title: { display: true, text: '连续驾驶时长 (小时)'}}, y: { ...defaultChartOptions.scales.y, title: { display: true, text: '风险指数'}}}} });
        if (document.getElementById('riskByAgeGroupChart')) new Chart(document.getElementById('riskByAgeGroupChart'), { type: 'bar', data: { labels: ['20-30岁', '31-40岁', '41-50岁', '51-60岁'], datasets: [{ label: '平均风险分', data: [68, 55, 62, 75], backgroundColor: brilliantBlues.tertiary, borderRadius: 5 }] }, options: defaultChartOptions });
        if (document.getElementById('companySafetyScoreChart')) new Chart(document.getElementById('companySafetyScoreChart'), { type: 'bar', data: { labels: ['优良企业A', '优良企业B', '中等企业C', '中等企业D', '待改进企业E', '待改进企业F'], datasets: [{ label: '安全评分', data: [95, 92, 78, 75, 58, 55], backgroundColor: ['#4BC0C0', '#4BC0C0', '#FFCD56', '#FFCD56', '#FF6384', '#FF6384'], borderRadius: 5 }] }, options: defaultChartOptions });
        if (document.getElementById('riskByWeatherChart')) new Chart(document.getElementById('riskByWeatherChart'), { type: 'bar', data: { labels: ['超速', '急变动', '事故'], datasets: [{ label: '晴天', data: [100, 80, 20], backgroundColor: brilliantBlues.tertiary }, { label: '雨天', data: [70, 95, 45], backgroundColor: brilliantBlues.secondary }, { label: '雾天', data: [50, 60, 65], backgroundColor: brilliantBlues.primary }] }, options: { ...defaultChartOptions, scales: { x: { stacked: false }, y: { stacked: false } }, plugins: { ...tooltipCallback.plugins, legend: { display: true, position: 'top' } } } });
        if (document.getElementById('riskForecastChart')) new Chart(document.getElementById('riskForecastChart'), { type: 'line', data: { labels: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'], datasets: [{ label: '2024年实际', data: [88, 95, 75, 80, 85, 90, 70, 75, 98, 110, 105, 120], borderColor: brilliantBlues.tertiary }, { label: '2025年预测', data: [90, 98, 78, 82, 88, 93, 72, 78, 102, 115, 110, 125], borderColor: brilliantBlues.secondary, borderDash: [5, 5] }] }, options: { ...defaultChartOptions, plugins: { ...tooltipCallback.plugins, legend: { display: true, position: 'top' } } } });

        // Key Road Section Charts
        if (document.getElementById('g4RiskCompositionChart')) new Chart(document.getElementById('g4RiskCompositionChart'), { type: 'doughnut', data: { labels: ['拥堵缓行', '急加速/减速', '追尾风险', '违规变道'], datasets: [{ data: [40, 25, 20, 15], backgroundColor: extendedPalette, borderColor: '#fff', borderWidth: 4 }] }, options: { ...defaultChartOptions, plugins: { ...tooltipCallback.plugins, legend: { display: true, position: 'bottom' }}}});
        if (document.getElementById('g4RiskByHourChart')) new Chart(document.getElementById('g4RiskByHourChart'), { type: 'bar', data: { labels: Array.from({length: 24}, (_, i) => `${i}`.padStart(2, '0')), datasets: [{ label: '相对风险频率', data: [10,8,6,6,8,15,25,45,55,50,40,30,25,35,48,60,65,58,45,30,20,15,12,10], backgroundColor: brilliantBlues.secondary, borderRadius: 3 }] }, options: {...defaultChartOptions, scales: { ...defaultChartOptions.scales, x: { ...defaultChartOptions.scales.x, ticks: { ...defaultChartOptions.scales.x.ticks, stepSize: 2 }}} }});
        if (document.getElementById('g4RiskByVehicleTypeChart')) new Chart(document.getElementById('g4RiskByVehicleTypeChart'), { type: 'bar', data: { labels: ['小型客车', '大型货车', '长途客车', '其他'], datasets: [{ label: '涉险车辆占比 (%)', data: [65, 20, 10, 5], backgroundColor: brilliantBlues.tertiary, borderRadius: 5 }] }, options: defaultChartOptions });


        // --- Heatmap, Flow Diagram ---
        function renderHeatmap() {
            const container = document.getElementById('heatmapContainer');
            if (!container) return;
            requestAnimationFrame(() => {
                const width = container.clientWidth, height = container.clientHeight;
                if (width === 0 || height === 0) return;
                const existingCanvas = container.querySelector('canvas');
                if (existingCanvas) existingCanvas.remove();
                const heatmapInstance = h337.create({ container: container, radius: 20, maxOpacity: .7, minOpacity: 0, blur: .75 });
                let points = [], max = 0, len = 200;
                const cityCenters = { guangzhou: { x: width*0.4, y: height*0.4, i:90 }, shenzhen: { x: width*0.7, y: height*0.5, i:85 }, dongguan: { x: width*0.55, y: height*0.45, i:80 }, foshan: { x: width*0.3, y: height*0.45, i:75 }, zhuhai: { x: width*0.45, y: height*0.7, i:60 }, shantou: { x: width*0.9, y: height*0.3, i:50 }, zhanjiang: { x: width*0.1, y: height*0.8, i:40 } };
                for (const city in cityCenters) {
                    for(let i = 0; i < len / 7; i++) {
                        const val = Math.floor(Math.random() * cityCenters[city].i) + 20;
                        max = Math.max(max, val);
                        points.push({ x: Math.floor(Math.random() * width*0.2 - width*0.1 + cityCenters[city].x), y: Math.floor(Math.random() * height*0.2 - height*0.1 + cityCenters[city].y), value: val });
                    }
                }
                heatmapInstance.setData({ max: max, data: points });
            });
        }

        function createFlowDiagram() {
            const container = document.getElementById('flow-diagram');
            if (!container) return;
            container.innerHTML = '';
            const cities = { gz: { name: '广州', top: '45%', left: '20%' }, sz: { name: '深圳', top: '65%', left: '80%' }, fs: { name: '佛山', top: '55%', left: '5%' }, dg: { name: '东莞', top: '55%', left: '60%' }, zs: { name: '中山', top: '80%', left: '35%' }};
            const flows = [ { from: 'gz', to: 'sz', s: 8 }, { from: 'gz', to: 'fs', s: 6 }, { from: 'gz', to: 'dg', s: 7 }, { from: 'sz', to: 'dg', s: 5 }, { from: 'fs', to: 'zs', s: 4 }];
            Object.keys(cities).forEach(key => {
                const city = cities[key];
                const cityEl = document.createElement('div');
                cityEl.className = 'flow-city absolute bg-[#004AAD] text-white px-3 py-1.5 text-sm rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2';
                cityEl.style.top = city.top; cityEl.style.left = city.left; cityEl.textContent = city.name;
                container.appendChild(cityEl); city.el = cityEl;
            });
            container.offsetHeight;
            flows.forEach(flow => {
                const fromRect = cities[flow.from].el.getBoundingClientRect(), toRect = cities[flow.to].el.getBoundingClientRect(), containerRect = container.getBoundingClientRect();
                const x1 = fromRect.left + fromRect.width / 2 - containerRect.left, y1 = fromRect.top + fromRect.height / 2 - containerRect.top, x2 = toRect.left + toRect.width / 2 - containerRect.left, y2 = toRect.top + toRect.height / 2 - containerRect.top;
                const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2), angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
                const line = document.createElement('div');
                line.className = 'flow-line';
                line.style.width = `${length}px`; line.style.height = `${flow.s}px`; line.style.top = `${y1}px`; line.style.left = `${x1}px`; line.style.transform = `rotate(${angle}deg)`; line.style.borderRadius = `${flow.s/2}px`;
                container.appendChild(line);
            });
        }

        renderHeatmap();
        createFlowDiagram();
        window.addEventListener('resize', () => {
            renderHeatmap();
            createFlowDiagram();
        });
    });
</script>

</body>
</html>
