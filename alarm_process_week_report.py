#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

alarm_process_day_stats_query = """
select 
	`stats_day` `日期`
	, `fxcz_zs` `风险警情处置数`
	, `fxcz_zbs` `正报数`
	, round(`fxcz_zbs`/`fxcz_zs`*100,2) `正报率`
from (
	SELECT 
		toDate(a.create_time) as `stats_day`
		,count() `fxcz_zs`
		,countIf(a.process_status in (1,2)) `fxcz_zbs`
	FROM gdispx_data.business_alarm_audit_info AS a 
	WHERE a.create_time >= toDate('{begin_date}') 
	and a.create_time < toDate('{end_date}') 
	and a.dispose_type = 0
	group by toDate(a.create_time)
)
"""

alarm_process_detial_query = """
SELECT 
	t.alarm_code_name `风险类型`
	,t.alarm_level `风险等级`
	,transform(a.process_status,[0,1,2,3,4,5,6],['待处置','违规属实','附件属实','设备误报','无附件警情','附件不完整','不构成违规'],'-') `处置类型`
	,a.process_info `处置描述`
	,t.vehicle_no `车牌号`
	,t.vehicle_color_name `车辆颜色`
	,t.vehicle_type_name `车辆类型`
	,t.speed `行车速度`
	,t.owner_name `业户名称`
	,a.create_time `处置时间`
FROM (select a.alarm_id,a.process_status,a.process_info,a.create_time
    from gdispx_data.business_alarm_audit_info a
	WHERE a.create_time >= toDate('{begin_date}') 
	and a.create_time < toDate('{end_date}') 
	and a.dispose_type = 0) AS a
join (select t.id,t.alarm_code_name,t.alarm_level,t.vehicle_no,t.vehicle_color_name,t.vehicle_type_name,t.speed,t.owner_name
    from gdispx_data.business_ai_alarm_info t
    join (select t.id, max(alarm_level) max_alarm_level 
    		from gdispx_data.business_ai_alarm_info t
    		where t.create_time >= toDate('{alarm_begin_date}') 
			  and t.create_time < toDate('{end_date}') 
			  and t.monitor_intervene = 1
    		group by t.id) t1 on t.id = t1.id and t.alarm_level = t1.max_alarm_level
	where t.create_time >= toDate('{alarm_begin_date}') 
	and t.create_time < toDate('{end_date}') 
	and t.monitor_intervene = 1) AS t on t.id = a.alarm_id
"""


def query_then_write_csv(query_sql):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join([col[0] for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join([str(x) for x in list(row)]) + '\n')


if __name__ == '__main__':
    ck = CkDB()
    try:
        client = ck.get_client()
        alarm_begin_date = '2021-09-01'
        begin_date = '2021-09-01'
        end_date = '2021-09-05'
        output_file_name = '风险属实率统计_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            query_then_write_csv(alarm_process_day_stats_query.format(begin_date=begin_date, end_date=end_date))

        output_file_name = '风险属实明细_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            query_then_write_csv(alarm_process_detial_query.format(begin_date=begin_date,
                                                                   end_date=end_date,
                                                                   alarm_begin_date=alarm_begin_date))
    finally:
        ck.disconnect()

