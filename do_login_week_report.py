from yyutils import *

'''
每周日统计
'''
bpath = r'/data/track_report'


def collect_third_party_report(inlist, start_day, end_day, vlist):
    tid_g = list_2_dictlist(vlist, 'third_party_id')
    tlist = list(filter(lambda o: o['tenant_id'] == 6, inlist))
    dp_g = list_2_dictlist(tlist, 'dept_id')
    outlist = []
    for dept_id, g in dp_g.items():
        id = g[0]['third_party_id']
        vn = len(tid_g.get(id, []))
        city_name = g[0]['city_name']
        total_n = len(g)
        name = g[0]['third_party_name']
        tlist1 = list(filter(lambda o: o['role_type'] == 1, g))
        n1 = len(tlist1)
        tlist2 = list(filter(lambda o: o['idcard'], tlist1))
        n2 = len(tlist2)
        n3, n4, n5, n6 = 0, 0, 0, 0
        for e in tlist1:
            if e['login_days'] == 7:
                n3 += 1
            elif e['login_days'] in (4, 5, 6):
                n4 += 1
            elif e['login_days'] in (1, 2, 3):
                n5 += 1
            else:
                n6 += 1
        ne = {}
        ne['地市'] = city_name
        ne['第三方机构名'] = name
        ne['已接受委托监控的车辆数量'] = vn
        ne['账号总数'] = total_n
        ne['实际开通的账号数'] = n1
        ne['已实名认证的账号数'] = n2
        ne['连续登录7天的账号数'] = n3
        ne['登录4-6天的账号数'] = n4
        ne['登录1-3天的账号数'] = n5
        ne['本周未登录的账号数'] = n6
        outlist.append(ne)
    h = ['地市', '第三方机构名', '已接受委托监控的车辆数量', '账号总数', '实际开通的账号数',
         '已实名认证的账号数',
         '连续登录7天的账号数', '登录4-6天的账号数', '登录1-3天的账号数',
         '本周未登录的账号数']
    otfile = join_path(bpath, f'{start_day}_{end_day}第三方机构登录情况.csv')
    write_csv(otfile, h, outlist)


def collect_owner_report(owner_log_outlist, vlist,yz_list, start_day, end_day):
    id_g = list_2_dictlist(owner_log_outlist,'owner_id')
    vid_g = list_2_dictlist(vlist,'owner_id')
    on_g = list_2_dict(yz_list,'owner_no')
    outlist1 = []
    outlist2 = []
    for id,g in id_g.items():
        owner_no = g[0]['owner_no']
        on_e = on_g.get(owner_no)
        if not on_e:
            continue
        vg = vid_g.get(id,[])
        if not vg:
            continue
        city_name = g[0]['city_name']
        owner_name = g[0]['owner_name']
        total_n = len(g)
        tlist1 = list(filter(lambda o: o['role_type'] == 1, g))
        n1 = len(tlist1)
        tlist2 = list(filter(lambda o: o['idcard'], tlist1))
        n2 = len(tlist2)
        n3, n4, n5, n6,n7 = 0, 0, 0, 0,0
        for e in tlist1:
            if e['login_days'] == 7:
                n3 += 1
                n7 += 1
            elif e['login_days'] in (4, 5, 6):
                n4 += 1
                n7 += 1
            elif e['login_days'] in (1, 2, 3):
                n5 += 1
                n7 += 1
            else:
                n6 += 1
        ne = {}
        t_vlist = list(filter(lambda o:o['third_party_id'],vg))
        if not t_vlist:
            ne['是否委托'] = '无委托'
        elif len(t_vlist)<len(vg):
            ne['是否委托'] = '部分委托'
        else:
            ne['是否委托'] = '全部委托'
        ts = set( [e['third_party_name'] for e in t_vlist])
        ne['第三方监管机构'] = ','.join(ts)
        ne['地市'] = city_name
        ne['业户代码'] = owner_no
        ne['业户名称'] = owner_name
        ne['经营范围'] = on_e.get('vehicle_type_name') if on_e else ''
        ne['需监管车辆数'] = on_e.get('xj_count') if on_e else 0
        ne['账号总数'] = total_n
        ne['实际开通的企业监控员账号数'] = n1
        ne['已实名认证的账号数'] = n2
        ne['本周登录的账号数'] = n7
        ne['连续登录7天的账号数'] = n3
        ne['登录4-6天的账号数'] = n4
        ne['登录1-3天的账号数'] = n5
        ne['本周未登录的账号数'] = n6
        ne['已监管车辆总数'] = len(vg)
        if ne['已监管车辆总数'] > ne['需监管车辆数']:
            ne['需监管车辆数'] = ne['已监管车辆总数']
        tlist = list(filter(lambda o:o['vehicle_type'] == 4,vg))
        if tlist:
            outlist2.append(ne)
        if len(tlist)<len(vg):
            outlist1.append(ne)
    otfile1 = join_path(bpath,f'{start_day}_{end_day}_两客一危业户登录情况.csv')
    otfile2 = join_path(bpath,f'{start_day}_{end_day}_重货业户登录情况.csv')
    h = ['地市', '业户代码','业户名称','是否委托','第三方监管机构',
         '经营范围','需监管车辆数','已监管车辆总数', '账号总数', '实际开通的企业监控员账号数',
         '已实名认证的账号数','本周登录的账号数' ,'连续登录7天的账号数',
         '登录4-6天的账号数', '登录1-3天的账号数',
         '本周未登录的账号数']
    write_csv(otfile1 ,h,outlist1)
    write_csv(otfile2 ,h,outlist2)


def download_loginInfo(start_day, end_day):
    db = DBUtil(DBType.MYSQL.value)

    v_sql = f'''
        select
        distinct vehicle_no,
        vehicle_color,
        vehicle_type,
        owner_id,
        owner_name,
        third_party_id,
        third_party_name
    from
        gdispx_data.v_vehicle_wide_day_all
    where
        inc_day BETWEEN '{start_day}' and '{end_day}'
    '''
    ck_db = DBUtil(DBType.CLICKHOUSE.value)
    vlist = ck_db.query_to_dictList(v_sql)
    ck_db.close()

    third_sql = f'''
        select
            ba.city_short city_name,
            u.user_id,
            u.dept_id,
            d.id third_party_id,
			d.name third_party_name,
            u.idcard,
            r.role_type,
            u.tenant_id,
            ifnull(L.login_days,0) login_days
        from gdispx.sys_user u
        left join gdispx.sys_user_role ur on u.user_id = ur.user_id
        left join gdispx.sys_role r on ur.role_id = r.role_id
        right join gdispx_basics.basics_third_party d on d.id = u.dept_id 
        left join gdispx_basics.basics_area ba on d.area_id = ba.id
        left join
        (
        select
        user_id,count(distinct date(create_time)) login_days
        from gdispx.sys_log_login 
        where date(create_time) between '{start_day}' and '{end_day}'
        group by user_id
        ) L on L.user_id = u.user_id
        where u.tenant_id = 6
				and d.is_delete = 0 
    '''
    third_list = db.query_to_dictList(third_sql)

    owner_sql = f'''
            select
            ba.city_short city_name,
            u.user_id,
            u.dept_id,
            d.owner_id,
            d.owner_no,
			d.owner_name,
			d.scope_details,
            u.idcard,
            r.role_type,
            u.tenant_id,
            ifnull(L.login_days,0) login_days
        from gdispx.sys_user u
        left join gdispx.sys_user_role ur on u.user_id = ur.user_id
        left join gdispx.sys_role r on ur.role_id = r.role_id
        left join gdispx_basics.basics_owner d on d.owner_id = u.dept_id 
        left join gdispx_basics.basics_owner_area oa on oa.owner_id = d.owner_id
        left join gdispx_basics.basics_area ba on ba.id = oa.area_id
        left join
        (
        select
        user_id,count(distinct date(create_time)) login_days
        from gdispx.sys_log_login 
        where date(create_time) between '{start_day}' and '{end_day}'
        group by user_id
        ) L on L.user_id = u.user_id
        where u.tenant_id = 1
				and d.is_delete = 0 
    '''
    yz_sql = f'''
        select 
        owner_no, 
        vehicle_type_name, 
        count(1) xj_count
        from yunzheng.jg_cheliang jc
        where ((vehicle_type in (1,2) and service_result in (1,2))
                   or (vehicle_type in (3,4) and service_result in (1)))
        GROUP BY owner_no
    '''
    yz_list = db.query_to_dictList(yz_sql)
    owner_log_outlist = db.query_to_dictList(owner_sql)
    db.close()
    collect_third_party_report(third_list, start_day, end_day, vlist)

    collect_owner_report(owner_log_outlist, vlist,yz_list, start_day, end_day)


def main(start_day, end_day):
    download_loginInfo(start_day, end_day)


if __name__ == '__main__':
    start = TimeUtils.DeltaDay(-7)
    end = TimeUtils.DeltaDay(-1)
    main(start, end)
    # pass
