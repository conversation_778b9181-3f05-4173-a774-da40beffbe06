import matplotlib.pyplot as plt
import pandas as pd
from matplotlib.font_manager import FontProperties

# 读取完整数据（确保处理可能的合并单元格）
df = pd.read_csv("""X:\\track_report\offline\\5月客运车辆离线漂移明细(已过滤).csv""", encoding='gb2312')
# 数据预处理
df['离线时长'] = df['离线时长'].str.extract(r'(\d+):(\d+):(\d+)').apply(lambda x: int(x[0]) * 24 + int(x[1]) + int(x[2]) / 60, axis=1)
# 配置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
# 创建10个子图的画布
fig = plt.figure(figsize=(24, 36))
fig.suptitle('客运车辆离线漂移分析报告', y=0.92, fontsize=20)
# 图1：各地市离线次数分布
ax1 = plt.subplot(5, 2, 1)
df['地市'].value_counts().head(10).plot(kind='bar', ax=ax1)
ax1.set_title('TOP10地市离线次数分布')
ax1.set_ylabel('次数')
# 图2：离线时长分布直方图
ax2 = plt.subplot(5, 2, 2)
df['离线时长'].plot(kind='hist', bins=20, ax=ax2)
ax2.set_title('离线时长分布直方图')
ax2.set_xlabel('时长(小时)')
# 图3：设备厂商离线次数TOP5
ax3 = plt.subplot(5, 2, 3)
df['设备厂商'].value_counts().head(5).plot(kind='pie', autopct='%1.1f%%', ax=ax3)
ax3.set_title('设备厂商故障分布')
# 图4：开始离线时间小时分布
ax4 = plt.subplot(5, 2, 4)
df['开始离线小时'] = pd.to_datetime(df['开始离线轨迹时间']).dt.hour
df['开始离线小时'].value_counts().sort_index().plot(kind='line', ax=ax4, marker='o')
ax4.set_title('开始离线时间小时分布')
ax4.set_xlabel('小时')
# 图5：离线距离分布箱线图
ax5 = plt.subplot(5, 2, 5)
df[['离线距离']].boxplot(ax=ax5)
ax5.set_title('离线距离分布箱线图')
# 图6：速度与离线时长的关系
ax6 = plt.subplot(5, 2, 6)
df.plot.scatter(x='速度', y='离线时长', ax=ax6, alpha=0.5)
ax6.set_title('行驶速度与离线时长的关系')
# 图7：服务商离线时长对比
ax7 = plt.subplot(5, 2, 7)
df.groupby('设备服务商')['离线时长'].mean().nlargest(5).plot(kind='barh', ax=ax7)
ax7.set_title('服务商平均离线时长TOP5')
# 图8：高频离线车牌分析
ax8 = plt.subplot(5, 2, 8)
df['车牌号'].value_counts().head(5).plot(kind='bar', color='orange', ax=ax8)
ax8.set_title('高频离线车辆TOP5')
# 图9：报警位分布
ax9 = plt.subplot(5, 2, 9)
df['报警位'].value_counts().plot(kind='pie', autopct='%1.1f%%', ax=ax9)
ax9.set_title('报警状态分布')
# 图10：不同车牌的离线距离对比
ax10 = plt.subplot(5, 2, 10)
df.groupby('车牌号')['离线距离'].mean().nlargest(5).plot(kind='bar', ax=ax10)
ax10.set_title('车辆平均离线距离TOP5')

plt.show()
