#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB

GROUP_BY_WHOLE = 0
GROUP_BY_CITY = 1
GROUP_BY_FACILITATOR = 2
GROUP_BY_PRODUCTER = 3


def export_L5(group_type, begin_date, end_date, ruiming=False):
    query_sql = "SELECT "
    if group_type == GROUP_BY_CITY:
        query_sql += "city as `城市`,"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += "if(facilitator_name = '', toString(facilitator_id), facilitator_name) as `运营商`,"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += "if(master_producter_name = '', toString(master_producter_id), master_producter_name) as `设备厂家`,"
    query_sql += """
            alarm_num as `报警车辆数`
           , freq_alarm_num as `频繁报警车辆数`
           , round(freq_alarm_num/alarm_num*100,2) as `频繁报警率`
           , acc_num - acchave_num as `附件丢失报警数`
           , acc_num as `需上传附件报警总数`
           , round((acc_num-acchave_num)/acc_num*100,2) as `附件丢失率`
         from
         (
            WITH (
            select round(avg(alarm_cnt_100km))
            from (
                select toUInt64(round(device_alarm_cnt / mileage_100km)) alarm_cnt_100km
                from (
                    select imei, round(total_mileage/100,1) mileage_100km
                    from gdispx_data.track_report
                    where `date` = '{track_begin_date}' and total_mileage > 1 AND total_mileage < 2400
                    ) a 
                join (select device_id, count() device_alarm_cnt from gdispx_data.business_alarm_info
                    where alarm_time >= toDate('{begin_date}') and alarm_time < toDate('{end_date}')
                    group by device_id
                    ) b on a.imei = b.device_id
                )
           ) AS device_alarm_cnt_avg
            select
        """
    if group_type == GROUP_BY_CITY:
        query_sql += " splitByChar(',', vrvri.manger_short_name)[2] city,"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += " vrvri.facilitator_id as facilitator_id, any(vrvri.facilitator_name) facilitator_name,"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += " vrvri.master_producter_id as master_producter_id, any(vrvri.master_producter_name) master_producter_name,"
    query_sql += """
                 countIf(alarm.device_id != '') alarm_num
                , countIf(alarm.device_alarm_cnt_100km > device_alarm_cnt_avg) freq_alarm_num
                , sum(bai.device_acc_num) acc_num
                , sum(bai.device_acchave_num) acchave_num
            from gdispx_data.v_regulator_vehicle_related_infos vrvri
            left join (select device_id, if(mileage_100km > 0, toUInt64(round(device_alarm_cnt / mileage_100km)), 0) device_alarm_cnt_100km
                       from (select device_id, count() device_alarm_cnt from gdispx_data.business_alarm_info
                			where alarm_time >= toDate('{begin_date}') and alarm_time < toDate('{end_date}') group by device_id) bai
            		   left join (select imei, round(total_mileage/100,1) mileage_100km from gdispx_data.track_report 
                  				where `date` = '{track_begin_date}' and total_mileage > 1 and total_mileage < 2400) tr on tr.imei = bai.device_id
                        ) alarm on alarm.device_id = vrvri.master_sim_code
            left join (select 
                         device_id
                         , count() device_acc_num
                         , countIf(alarm_time >= addDays(create_time,-1) and b.alarm_tag != '') device_acchave_num
                        from (select device_id, alarm_time, alarm_tag from gdispx_data.business_alarm_info 
                                where alarm_time >= toDate('{begin_date}') and alarm_time < toDate('{end_date}')
                                and alarm_code in (10400,10401,10402,10410,10413,10412)) a
                        left join (select alarm_tag,create_time from gdispx_data.business_accessories_info
                                    where create_time >= toDate('{begin_date}') and create_time < toDate('{end_date}')+2) b 
                          on a.alarm_tag = b.alarm_tag
                        group by device_id) bai
                    on bai.device_id = trim(vrvri.master_sim_code)
            where vrvri.master_sim_code != ''
             and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','0','-1'))
                    or (vrvri.vehicle_type in ('3','4') and vrvri.service_result in ('1')))
        """
    if ruiming:
        query_sql += " and vrvri.master_producter_name = '深圳市锐明技术股份有限公司'"
    if group_type == GROUP_BY_CITY:
        query_sql += " group by splitByChar(',', vrvri.manger_short_name)[2]"
    elif group_type == GROUP_BY_FACILITATOR:
        query_sql += " group by vrvri.facilitator_id"
    elif group_type == GROUP_BY_PRODUCTER:
        query_sql += " group by vrvri.master_producter_id"
    query_sql += ") t"
    # query_sql
    query_then_write_csv(query_sql.format(begin_date=begin_date,
                                          end_date=end_date,
                                          track_begin_date=begin_date.replace('-','')[-6:],
                                          track_end_date=end_date.replace('-','')[-6:]))


def query_then_write_csv(query_sql):
    print(query_sql)
    rows = client.execute(query_sql, with_column_types=True)
    output_file.write(','.join([col[0] for col in rows[1]]) + '\n')
    for row in rows[0]:
        output_file.write(','.join([str(x) for x in list(row)]) + '\n')


if __name__ == '__main__':
    ck = CkDB()
    try:
        client = ck.get_client()
        begin_date = '2021-10-09'
        end_date = '2021-10-10'

        output_file_name = '合格率指标统计_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])
        with open(output_file_name, 'w') as output_file:
            output_file.write('整体\n')
            export_L5(GROUP_BY_WHOLE, begin_date, end_date)
            output_file.write('\n\n按城市\n')
            export_L5(GROUP_BY_CITY, begin_date, end_date)
            output_file.write('\n\n按运营商\n')
            export_L5(GROUP_BY_FACILITATOR, begin_date, end_date)
            output_file.write('\n\n按设备厂商\n')
            export_L5(GROUP_BY_PRODUCTER, begin_date, end_date)
            output_file.write('\n\n锐明专项\n')
            output_file.write('整体\n')
            export_L5(GROUP_BY_WHOLE, begin_date, end_date, True)
            output_file.write('\n\n按城市\n')
            export_L5(GROUP_BY_CITY, begin_date, end_date, True)
            output_file.write('\n\n按运营商\n')
            export_L5(GROUP_BY_FACILITATOR, begin_date, end_date, True)
    finally:
        ck.disconnect()



