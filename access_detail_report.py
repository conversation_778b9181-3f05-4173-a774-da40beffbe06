#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import MySqlDB

access_detail_sql = """
    SELECT
        bv.vehicle_no AS vehicleNo,
        sdivc.label AS vehicleColor,
        sdivt.label AS vehicleType,
        bap.manger_name AS porxyAreaName,
        bam.manger_name AS areaName,
        bv.service_result AS serviceResult,
        IF(bv.access_time IS NOT NULL, '已接入', '未接入') AS accessStatus,
        bv.access_time AS accessTime,
        bv.break_time AS breakTime,
        IF(bs.is_master = 0, '一体机', IF(bs1.is_master = 1, '分体机', '')) AS deviceType,
        bs.device_id AS masterDeviceId,
        bs.sim_code AS masterSimCode,
        bp.producter_name AS masterProducterName,
        sdidb.label AS masterDeviceBrand,
        bdt.type_num AS masterTypeNum,
        bs1.device_id AS aiBoxDeviceId,
        bs1.sim_code AS aiBoxSimCode,
        bp1.producter_name AS aiBoxProducterName,
        sdidb1.label AS aiBoxDeviceBrand,
        bdt1.type_num AS aiBoxTypeNum,
        bo.owner_name AS ownerName
    FROM gdispx_basics.basics_vehicle AS bv
    LEFT JOIN gdispx.sys_dict_item AS sdivc ON bv.vehicle_color = sdivc.`value` AND sdivc.type = 'vehicle_color' AND sdivc.tenant_id = 1
    LEFT JOIN gdispx.sys_dict_item AS sdivt ON bv.vehicle_type = sdivt.`value` AND sdivt.type = 'vehicle_type'  AND sdivt.tenant_id = 1
    LEFT JOIN gdispx_basics.basics_vehicle_area AS bvap ON bv.vehicle_id = bvap.vehicle_id AND bvap.area_type = 1
    LEFT JOIN gdispx_basics.basics_area AS bap ON bvap.area_id = bap.id
    LEFT JOIN gdispx_basics.basics_vehicle_area AS bvam ON bv.vehicle_id = bvam.vehicle_id AND bvam.area_type = 0
    LEFT JOIN gdispx_basics.basics_area AS bam ON bvam.area_id = bam.id
    LEFT JOIN gdispx_basics.basics_sim AS bs ON bv.vehicle_id = bs.vehicle_id AND bs.is_master = 0
    LEFT JOIN gdispx_basics.basics_device_type AS bdt ON bs.device_type_id = bdt.id
    LEFT JOIN gdispx_basics.basics_producter AS bp ON bdt.producter_id = bp.id
    LEFT JOIN gdispx.sys_dict_item AS sdidb ON bdt.device_brand = sdidb.`value` AND sdidb.type = 'device_brand'  AND sdidb.tenant_id = 1
    LEFT JOIN gdispx_basics.basics_sim AS bs1 ON bv.vehicle_id = bs1.vehicle_id AND bs1.is_master = 1
    LEFT JOIN gdispx_basics.basics_device_type AS bdt1 ON bs1.device_type_id = bdt1.id
    LEFT JOIN gdispx_basics.basics_producter AS bp1 ON bdt1.producter_id = bp1.id
    LEFT JOIN gdispx.sys_dict_item AS sdidb1 ON bdt1.device_brand = sdidb1.`value` AND sdidb1.type = 'device_brand'  AND sdidb1.tenant_id = 1
    LEFT JOIN gdispx_basics.basics_owner AS bo ON bv.owner_id = bo.owner_id
    WHERE bv.is_delete = 0
    ORDER BY bv.create_time DESC
"""


def query_then_write_csv(query_sql):
    print(query_sql)
    cursor = mysql.get_cursor()
    cursor.execute(query_sql)
    rows = cursor.fetchall()
    output_file.write(','.join(rows[0].keys()) + '\n')
    for row in rows:
        output_file.write(','.join([str(x) if x or x == 0 else '' for x in row.values()]) + '\n')


if __name__ == '__main__':
    mysql = MySqlDB()
    try:
        output_file_name = '接入明细.csv'
        with open(output_file_name, 'w') as output_file:
            output_file.write('接入明细\n')
            query_then_write_csv(access_detail_sql)
    finally:
        mysql.close()

