select a.*, b.*
from (select sim_code, latitude, longitude, speed, gps_time, mileage
      from business_raw_gps_info_all
      where gps_time between '2025-05-18 15:00:00' and '2025-05-20 15:00:00'
      order by sim_code, gps_time
         ) a
         asof
         LEFT join (select sim_code,
                           vehicle_no,
                           ic_time,
                           if(length(id_card) == 18 or length(id_card) == 15, driver_name, '无法解析') driver_name,
                           ic_status,
                           if(length(id_card) == 18 or length(id_card) == 15, ic_code, '无法解析')     ic_code,
                           if(length(id_card) == 18 or length(id_card) == 15, id_card, '无法解析')     id_card
                    from
                        remote('172.16.123.221', 'gdispx_data', driver_alarm_detail, 'default', '<EMAIL>')
                    where create_time between '2025-05-18 00:00:00' and '2025-05-21 00:00:00'
                      and vehicle_type = '1'
                    order by sim_code, ic_time
                        settings force_primary_key = 0
    ) b on a.sim_code = b.sim_code and a.gps_time >= b.ic_time
WHERE a.sim_code = '000207323236';
