from yyutils import *
from utils.YYSQLTemplate import YYSQLTemplate
from collections import OrderedDict


def loadSQL(start_day, end_day):
    avg_drift_sql = YYSQLTemplate.calTrackAvgDriftPoints(start_day, end_day)
    v_jg_select_columns = '''
                    vehicle_no ,
                    vehicle_color ,
                    any(master_producter_id) master_producter_id0,
                    any(master_producter_name) master_producter_name0,
                    max(online) online,
                    if( sum(drift_point_count) > avg_drift_point_count,1,0  ) is_drift,
                    sum(total_mileage) total_mileage,
                    sum(continuous_mileage) continuous_mileage,
                    if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, 0,1  ) is_ok,
                    min(never_gps) is_never_gps,
                    min(never_alarm) is_never_alarm,
                    min(never_acc) is_never_acc,
                    sum(accessoriesNum) accessoriesNum,
                    sum(accessoriesonTimeNum) accessoriesonTimeNum,
                    sum(risk_disposeNum) risk_manualNum,
                    risk_manualNum - sum(check_confirm_riskNum) risk_manualNum_ok
                '''
    v_jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([], 'vehicle_no,vehicle_color',
                                                                    v_jg_select_columns,
                                                                    start_day=start_day,
                                                                    end_day=end_day
                                                                    )
    v_jg_sql = SQLHelper().table(v_jg_sql).select('''
                    vehicle_no ,
                    vehicle_color ,
                    if(master_producter_id0 is null,18,master_producter_id0) master_producter_id,
                    if(master_producter_id0 is null,'非粤标设备（1月18日前安装）',master_producter_name0) master_producter_name,
                    online,
                    is_drift,
                    total_mileage,
                    continuous_mileage,
                    is_ok,
                    is_never_gps,
                    is_never_alarm,
                    is_never_acc,
                    accessoriesNum,
                    accessoriesonTimeNum,
                    risk_manualNum,
                    risk_manualNum_ok
    ''')
    productor_sql = SQLHelper().table(v_jg_sql).groupBy('master_producter_id') \
        .select('''
            master_producter_id,
            any(master_producter_name) master_producter_name,
            count(1) jg_count,
            sum(online) online_count,
            FLOOR(sum(online) / count(1),4 ) online_rate,
            sum(is_drift) high_drift_count,
            FLOOR(if(online_count != 0,high_drift_count / online_count,0),4 ) drift_rate,
            FLOOR(sum(total_mileage),2) totalMileage,
            FLOOR(sum(continuous_mileage),2) continuousMileage,
            FLOOR(if(totalMileage != 0,continuousMileage / totalMileage,0),4 ) track_rate,
            sum(is_ok) ok_count,
            sum(is_never_gps) never_gps_count,
            sum(is_never_alarm) never_alarm_count,
            sum(is_never_acc) never_acc_count,
            FLOOR(ok_count / jg_count,4 ) ok_rate,
            sum(accessoriesNum) accessoriesNum_count,
            accessoriesNum_count - sum(accessoriesonTimeNum) accessories_miss_count,
            FLOOR(if(accessoriesNum_count!=0,accessories_miss_count / accessoriesNum_count,0),4 ) accessories_miss_rate,
            sum(risk_manualNum) risk_manualNum,
            sum(risk_manualNum_ok) risk_manualNum_ok,
            FLOOR(risk_manualNum_ok/risk_manualNum,4) risk_manualNum_ok_rate
            ''')
    return avg_drift_sql, productor_sql


def main(start_day, end_day):
    avg_drift_sql, productor_sql = loadSQL(start_day, end_day)
    print(productor_sql)




if __name__ == '__main__':
    start, end = TimeUtils.getLastMonthRange()
    main(start, end)
