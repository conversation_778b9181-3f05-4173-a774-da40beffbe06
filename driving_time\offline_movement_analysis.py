import pandas as pd
import numpy as np
from math import radians

# 向量化Haversine距离计算
def haversine_vectorized(df):
    R = 6371000  # 地球半径(米)
    
    lat1 = np.radians(df['latitude'].shift())
    lon1 = np.radians(df['longitude'].shift())
    lat2 = np.radians(df['latitude'])
    lon2 = np.radians(df['longitude'])
    
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    
    a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1-a))
    
    return R * c

def analyze_movements(input_path):
    # 读取数据并转换时间格式
    df = pd.read_csv(input_path, parse_dates=['gps_time'])
    
    # 按imei和gps_time排序
    df = df.sort_values(['gps_time'])
    # 计算时间差（分钟）
    time_diff = df['gps_time'].diff().dt.total_seconds() / 60
    
    # 计算位移距离
    df['distance'] = haversine_vectorized(df)
    
    # 应用筛选条件
    mask = (time_diff >= 30) & (df['distance'] >= 2000)
    
    # 重组结果数据
    result = df.reset_index()
    result = result.join(df.shift().loc[result.index, ['gps_time', 'latitude', 'longitude']].add_prefix('start_'))
    result = result[['start_gps_time', 'gps_time', 
                   'start_latitude', 'start_longitude',
                   'latitude', 'longitude', 'distance']]
    
    # 计算持续时长并重命名字段
    result['duration'] = result['gps_time'] - result['start_gps_time']
    result = result.rename(columns={
        'start_gps_time': '离线开始时间',
        'gps_time': '离线结束时间',
        'start_latitude': '开始纬度',
        'start_longitude': '开始经度',
        'latitude': '结束纬度',
        'longitude': '结束经度',
        'duration': '离线时长',
        'distance': '位移距离'
    })
    
    # 保存结果并返回数据框
    return result[mask].dropna(subset=['离线开始时间', '离线结束时间'])

if __name__ == '__main__':
    result = analyze_movements('x:/track_report/driving_time/013303187759_2025-05.csv')
    result.to_csv('x:/track_report/driving_time/offline_movements.csv', index=False)
    print(result.head(10))
    print(f'分析完成，共发现{len(result)}条离线位移记录')