#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB, MySqlDB
import utils


def get_owner_stat_ck_sql():
    query_sql = """
    select vrvri.owner_id 
        , count() as `已监管车辆总数`
        , count() as `入网车辆数`
    from gdispx_data.v_regulator_vehicle_related_infos vrvri
    where vrvri.master_sim_code != ''
      and ((vrvri.vehicle_type in ('1','2') and vrvri.service_result in ('1','2'))
            or (vrvri.vehicle_type in ('3') and vrvri.service_result in ('1')))
    group by vrvri.owner_id 
    """
    return query_sql


def get_owner_stat_mysql_sql(begin_date, end_date):
    query_sql = """
     select a.owner_id, b.`<PERSON>a<PERSON>u<PERSON>hi` `地市`, b.owner_no `业户代码`, trim(b.owner_name) `业户名称`, b.vehicle_type_name `经营范围`, b.`需监管车辆数`
       , a.`总账号数`,a.`实际开通的企业监控员账号数`,a.`已实名认证的账号数`,a.`本周登录的账号数`,a.`连续7天登录的账号数`
			 , a.`登录4-6天的账号数`, a.`登录1-3天的账号数`,a.`本周未登录的账号数`  from 
        (SELECT bo.owner_id
            , bo.owner_no 
            , count(1) `总账号数`
            , sum(if(su.role_type_1_cnt > 0, 1, 0)) `实际开通的企业监控员账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.idcard != '', 1, 0)) `已实名认证的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days > 0, 1, 0)) `本周登录的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days = 7, 1, 0)) `连续7天登录的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days >=4 and su.login_days <= 6, 1, 0)) `登录4-6天的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days >=1 and su.login_days <= 3, 1, 0)) `登录1-3天的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days = 0, 1, 0)) `本周未登录的账号数`
            from
            (select su.user_id
                    , dept_id
                    , ifnull(su.idcard,'') idcard
                    , count(distinct date(sl.create_time)) login_days
                    , sum(if(sr.role_type = 1, 1, 0)) role_type_1_cnt
                FROM gdispx.sys_user su
                left join gdispx.sys_user_role sur on su.user_id = sur.user_id
                left join gdispx.sys_role sr on sur.role_id = sr.role_id
                left join gdispx.sys_log sl on su.user_id = sl.user_id and sl.create_time >= '{begin_date}' and sl.create_time < '{end_date}'
                WHERE su.tenant_id = 1
                group by su.user_id
            ) su 
            right join gdispx_basics.basics_owner bo on bo.owner_id = su.dept_id
            GROUP BY bo.owner_id) a 
   right join (select owner_no, owner_name, vehicle_type_name, `XiaQuShi`, count(1) `需监管车辆数`
            from yunzheng.jg_cheliang jc
            where ((vehicle_type in (1,2) and service_result in (1,2,3,4,5))
                   or (vehicle_type in (3) and service_result in (1)))
            GROUP BY owner_no) b on a.owner_no = b.owner_no 
    """
    return query_sql.format(begin_date=begin_date, end_date=end_date)

def get_third_party_stat_mysql_sql(begin_date, end_date):
    query_sql = """
    select 
       b.`地市`
       , trim(b.third_party_name) `第三方机构名称`
       , b.`已接受委托监控的车辆数量`
       , a.`帐号总数`
       , a.`实际开通的第三方监控员账号数`
       , a.`已实名认证的账号数`
       , a.`本周登录的账号数`
       , a.`连续7天登录的账号数`
	   , a.`登录4-6天的账号数`
	   , a.`登录1-3天的账号数`
	   , a.`本周未登录的账号数`  
	   from 
        (SELECT btp.id
            , btp.name 
            , count(1) `帐号总数`
            , sum(if(su.role_type_1_cnt > 0, 1, 0)) `实际开通的第三方监控员账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.idcard != '', 1, 0)) `已实名认证的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days > 0, 1, 0)) `本周登录的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days = 7, 1, 0)) `连续7天登录的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days >=4 and su.login_days <= 6, 1, 0)) `登录4-6天的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days >=1 and su.login_days <= 3, 1, 0)) `登录1-3天的账号数`
            , sum(if(su.role_type_1_cnt > 0 and su.login_days = 0, 1, 0)) `本周未登录的账号数`
            from
            (select su.user_id
                    , dept_id
                    , ifnull(su.idcard,'') idcard
                    , count(distinct date(sl.create_time)) login_days
                    , sum(if(sr.role_type = 1, 1, 0)) role_type_1_cnt
                FROM gdispx.sys_user su
                left join gdispx.sys_user_role sur on su.user_id = sur.user_id
                left join gdispx.sys_role sr on sur.role_id = sr.role_id
                left join gdispx.sys_log sl on su.user_id = sl.user_id and sl.create_time >= '{begin_date} 00:00:00' and sl.create_time < '{end_date} 00:00:00'
                WHERE su.tenant_id = 6
                group by su.user_id
            ) su 
            right join gdispx_basics.basics_third_party btp on btp.id = su.dept_id where btp.is_delete = 0 
            GROUP BY btp.id) a 
            right join(
             select 
              vvri.third_party_name 
             , vvri.third_party_id 
             , vvri.vehicle_type 
             , count(1) `已接受委托监控的车辆数量` 
             , ba.name `地市`
            from  gdispx_basics.v_vehicle_related_infos vvri 
            left join gdispx_basics.basics_area ba on concat(substr(vvri.area_id, 1, 4), '00') = ba.id 
            group by vvri.third_party_id
            ) b on a.id = b.third_party_id
            """
    return query_sql.format(begin_date=begin_date, end_date=end_date)

def query_then_write_csv(ck_query_sql, mysql_query_sql, join_key, ck_client, mysql_cursor, output_file):
    ck_rows = ck_client.execute(ck_query_sql, with_column_types=True)
    ck_cols, ck_results = utils.ck_results_to_dict(ck_rows, with_columns=True)
    none_str = ',' * ck_cols.count(',')
    mysql_cursor.execute(mysql_query_sql)
    mysql_rows = mysql_cursor.fetchall()
    row0 = mysql_rows[0]
    mysql_cols = ','.join(list(row0.keys())[1:])
    output_file.write(mysql_cols + ',' + ck_cols + '\n')
    for row in mysql_rows:
        value = row[join_key]
        row.pop(join_key)
        if value is not None and value in ck_results:
            output_file.write(','.join([str(x) if x or x == 0 else '' for x in row.values()]) + ','
                              + ','.join([str(x) for x in list(ck_results.get(value).values())[1:]]) + '\n')
        else:
            output_file.write(','.join([str(x) if x or x == 0 else '' for x in row.values()]) + ','
                              + none_str + '\n')


def query_then_write_csv1(mysql_query_sql, mysql_cursor, output_file):
    print("query_then_write_csv \n")
    print(mysql_query_sql)
    mysql_cursor.execute(mysql_query_sql)
    mysql_rows = mysql_cursor.fetchall()
    row0 = mysql_rows[0]
    mysql_cols = ','.join(list(row0.keys()))
    output_file.write(mysql_cols + '\n')
    for row in mysql_rows[1:]:
        output_file.write(','.join([str(x) for x in list(row)]) + '\n')


def export(begin_date, end_date):
    ck = CkDB()
    mysql = MySqlDB()
    try:
        ck_client = ck.get_client()
        mysql_cursor = mysql.get_cursor()

        output_file_name1 = '广东省车辆智能监管系统企业用户登录情况_{}_{}.csv'.format(begin_date.replace('-', '')[-4:],end_date.replace('-', '')[-4:])

        with open(output_file_name1, 'w',encoding='utf-8') as output_file:
            output_file.write('广东省车辆智能监管系统企业用户登录情况\n')
            query_then_write_csv(get_owner_stat_ck_sql(), get_owner_stat_mysql_sql(begin_date, end_date), 'owner_id', ck_client, mysql_cursor,output_file)

    finally:
        ck.disconnect()
        mysql.close()


def export1(begin_date, end_date):
    mysql = MySqlDB()
    try:
        mysql_cursor = mysql.get_cursor()

        output_file_name2 = '广东省车辆智能监管系统第三方机构登录情况_{}_{}.csv'.format(begin_date.replace('-', '')[-4:], end_date.replace('-', '')[-4:])

        with open(output_file_name2, 'w', encoding='utf-8') as output_file:
            output_file.write('广东省车辆智能监管系统第三方机构登录情况\n')
            query_then_write_csv1(get_third_party_stat_mysql_sql(begin_date, end_date), mysql_cursor, output_file)
    finally:
        mysql.close()


if __name__ == '__main__':
    begin_date = '2022-04-19'
    end_date = '2021-04-22'
    export1(begin_date, end_date)
