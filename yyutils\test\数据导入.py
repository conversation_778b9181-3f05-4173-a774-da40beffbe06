from yyutils import *
import taos


def insert_gps(bpath):
    infile = join_path(bpath, 'vc.csv')
    vn_e = {}
    for g in read_by_trunk(readCsv(infile), 10000):
        for e in g:
            vn_e.setdefault(e['vehicle_no'], e)
        break

    infile = join_path(bpath, 'gps.csv')
    conn = taos.connect(host="chai-server", port=6030, database="gps_db")
    n = 0
    for g in read_a_group(readCsv(infile), 'vehicle_no'):
        n += 1

        print(f"开始写入{g[0]['vehicle_no']}")
        e = vn_e.get(g[0]['vehicle_no'])
        ns = f'{e["vehicle_id"]}'.zfill(6)
        tags = f'''
           {e['vehicle_id']},'{e['vehicle_no']}',{e['vehicle_color']},
            {e['vehicle_type']},{e.get('owner_id',0)},'{e['owner_name']}',
            {e.get('facilitator_id',0)},'{e['facilitator_name']}',
            {0},'{e['third_party_name']}',
            {e.get('device_type',0)},{0},
            '{e['master_producter_name']}',
            {0},
            '{e['AI_BOX_producter_name']}',
            {e.get('area_id',0)},'{e['area_name']}',{e.get('city_id',0)},'{e['city_name']}'
        '''
        sql0 = f'''
            INSERT INTO gps{ns} USING gps 
            TAGS({tags})
            VALUES 
        '''
        outlist = []
        for e in g:
            if not e['gps_time'].startswith('220814'):
                continue
            t = TimeUtils.strptime(e['gps_time'], TimeFormat.ymdHMS.value)
            ts = TimeUtils.strftime(t, '%Y-%m-%d %H:%M:%S.%f')[:-3].replace('2022-08-14','2022-08-18')
            vstr = f'''('{ts}',{e['lng']},{e['lat']})'''
            outlist.append(vstr)
        for o in read_by_trunk(outlist,500):
            final_sql = sql0 + ','.join(o)
            conn.execute(final_sql)
        print(f"第{n}辆 - 写入{g[0]['vehicle_no']}完毕,导入数据({len(outlist)})!")
        break
    conn.close()


def insert_alarm(bpath):
    infile = join_path(bpath, 'vc.csv')
    vn_e = {}
    for g in read_by_trunk(readCsv(infile), 10000):
        for e in g:
            vn_e.setdefault(e['vehicle_no'], e)
        break
    infile1 = join_path(bpath,'alarm.csv')
    conn = taos.connect(host="chai-server", port=6030, database="test")
    conn.execute("USE gps_db")
    n = 0
    for g in read_by_trunk(readCsv(infile1),500):
        vn_sg = list_2_dictlist(g,'vehicle_no')
        sql0 = 'INSERT INTO '
        sqls = []
        for vn,sg in vn_sg.items():
            e = vn_e.get(sg[0]['vehicle_no'])
            ns = f'{e["vehicle_id"]}'.zfill(6)
            tags = f'''
            {e['vehicle_id']},'{e['vehicle_no']}',{e['vehicle_color']},
            {e['vehicle_type']},{e.get('owner_id',0)},'{e['owner_name']}',
            {e.get('facilitator_id',0)},'{e['facilitator_name']}',
            {0},'{e['third_party_name']}',
            {e.get('device_type',0)},{0},
            '{e['master_producter_name']}',
            {0},
            '{e['AI_BOX_producter_name']}',
            {e.get('area_id',0)},'{e['area_name']}',{e.get('city_id',0)},'{e['city_name']}'
                    '''
            sql1 = f'''
                        alarm{ns} USING alarm 
                        TAGS({tags})
                        VALUES 
                    '''
            outlist = []
            for e in sg:
                if not e['alarm_time'].startswith('2022-08-14'):
                    continue
                if len(e['alarm_code'])> 5:
                    continue
                ts = e['alarm_time']+'.000'
                vstr = f'''
                    ('{ts}',{e['longitude']},{e['latitude']},
                    '{e['id']}',{e['alarm_code']},{e['alarm_level']},
                    '{e['alarm_tag'] if len(e['alarm_tag'])>70 else ''}')
                '''
                outlist.append(vstr)
            if not outlist:
                continue
            final_sql = sql1 + ','.join(outlist)
            sqls.append(final_sql)
        final_sql = sql0 + ' '.join(sqls)
        try:
            conn.execute(final_sql)
        except Exception as ex:
            print(final_sql)
            break
        n += len(g)
        print(f'完成了{n}条数据导入!')
    conn.close()


def insert_risk(bpath):
    infile = join_path(bpath, 'vc.csv')
    vn_e = {}
    for g in read_by_trunk(readCsv(infile), 10000):
        for e in g:
            vn_e.setdefault(e['vehicle_no'], e)
        break
    infile1 = join_path(bpath, 'risk.csv')
    conn = taos.connect(host="chai-server", port=6030, database="test")
    conn.execute("USE gps_db")

    n = 0
    for g in read_by_trunk(readCsv(infile1), 500):
        vn_sg = list_2_dictlist(g, 'vehicle_no')
        sql0 = 'INSERT INTO '
        sqls = []
        for vn, sg in vn_sg.items():
            e = vn_e.get(sg[0]['vehicle_no'])
            ns = f'{e["vehicle_id"]}'.zfill(6)
            tags = f'''
            {e['vehicle_id']},'{e['vehicle_no']}',{e['vehicle_color']},
            {e['vehicle_type']},{e.get('owner_id',0)},'{e['owner_name']}',
            {e.get('facilitator_id',0)},'{e['facilitator_name']}',
            {0},'{e['third_party_name']}',
            {e.get('device_type',0)},{0},
            '{e['master_producter_name']}',
            {0},
            '{e['AI_BOX_producter_name']}',
            {e.get('area_id',0)},'{e['area_name']}',{e.get('city_id',0)},'{e['city_name']}'
                                '''
            sql1 = f'''
                            risk{ns} USING risk 
                            TAGS({tags})
                            VALUES 
                        '''
            outlist = []
            for e in sg:
                if not e['alarm_time'].startswith('2022-08-14'):
                    continue
                if len(e['alarm_code']) > 5:
                    continue
                ts = e['alarm_time'] + '.000'
                vstr = f'''
                        ('{ts}',{e['longitude']},{e['latitude']},
                        '{e['id']}',{e['alarm_code']},{e['alarm_level']},
                        {e['alarm_classify']},
                        '{e['alarm_tag'] if len(e['alarm_tag']) > 70 else ''}')
                    '''
                outlist.append(vstr)
            if not outlist:
                continue
            final_sql = sql1 + ','.join(outlist)
            sqls.append(final_sql)
        final_sql = sql0 + ' '.join(sqls)
        conn.execute(final_sql)
        # try:
        #     conn.execute(final_sql)
        # except Exception as ex:
        #     print(final_sql)
        #     break
        n += len(g)
        print(f'完成了{n}条数据导入!')
    conn.close()


def main(bpath):
    insert_gps(bpath)
    # insert_alarm(bpath)
    # insert_risk(bpath)


if __name__ == '__main__':
    bpath = r'/data/taosdata/test'
    main(bpath)
