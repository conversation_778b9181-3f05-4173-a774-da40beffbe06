import datetime
from itertools import groupby
from itertools import tee
import pandas as pd

df = pd.read_csv('X:\\track_report\\driving_time\\drivingtime.csv')

df['gps_time'] = pd.to_datetime(df['gps_time'])

total_driving_time = 0
driver = {}
for i, j in groupby(df.iterrows(), key=lambda row: (row[1]['speed'] if row[1]['speed'] <= 100 else 1)):
    itorMax, itorMin = tee(j, 2)
    # str to datetime
    maxItem = max(itorMax, key=lambda row: row[1]['gps_time'])
    minItem = min(itorMin, key=lambda row: row[1]['gps_time'])
    maxTimeStr = maxItem[1]['gps_time']
    minTimeStr = minItem[1]['gps_time']
    driverId = maxItem[1]['id_card']
    if driverId not in driver:
        driver[driverId] = 0
    maxTime = datetime.datetime.strptime(maxTimeStr, "%Y-%m-%d %H:%M:%S.000")
    minTime = datetime.datetime.strptime(minTimeStr, "%Y-%m-%d %H:%M:%S.000")

    if i == 1:
        print(f"{driverId} 超速时间段：开始时间：{minTimeStr}，结束时间：{maxTimeStr}，持续时长：{maxTime - minTime}")