from yyutils import *
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT,orderByCityVT
from collections import OrderedDict


def loadSQL(start_day, end_day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=end_day)
    jg_sql = f'''
            select 
            toUInt32(third_party_id) third_party_id  ,
            count(distinct vehicle_no) jg_count,
            any(third_party_name) third_party_name
            from gdispx_data.v_vehicle_wide_day_all vvwda 
            where inc_day BETWEEN '{start_day}'  and '{end_day}'
            group by third_party_id
        '''
    covered_sql = f'''
            select 
                toUInt32(third_party_id) third_party_id  ,
                count(1) except_count
            from 
            (
            SELECT 
                vehicle_no ,
                third_party_id ,
                count(distinct toDate(alarm_time) ) except_days
                from gdispx_data.business_ai_alarm_info baai 
                where toDate(alarm_time) BETWEEN '{start_day}' and '{end_day}'
                and alarm_code = 10405
                and id not in 
                (
                select 
                alarm_id
                from gdispx_data.business_alarm_audit_info baai2 
                where toDate(create_time) BETWEEN '{start_day}' and '{risk_dispose_end_day}'
                and process_status=2 and dispose_type=7
                )
            group by vehicle_no,third_party_id
            ) 
            group by third_party_id
        '''
    sql = SQLHelper().table(jg_sql,'A')\
        .leftJoin().table(covered_sql,'B').on('A.third_party_id = B.third_party_id') \
        .leftJoin().table('gdispx_data.basics_third_party_info','C').on('A.third_party_id=toUInt32(C.id)') \
        .leftJoin().table('gdispx_data.basics_area','D').on('C.area_id = D.id') \
        .where('1=1 order by p desc') \
        .select(
        f'''
        A.third_party_id third_party_id,
        A.jg_count jg_count,
        B.except_count except_count,
        C.name third_part_name,
        D.id city_id,
        D.name city_name,
        Floor(except_count/jg_count,4) p
        '''
    )
    # print(sql)
    return sql


def export_OrderDictList(inlist):
    outlist = []
    for e in inlist:
        ne = OrderedDict(zip([
            'city_name', 'third_part_name', 'jg_count',
            'except_count', 'p'
        ], [
            e['city_name'], e['third_part_name'], e['jg_count'],
            e['except_count'], FLOORtoPercent(e['p'])
        ]))
        outlist.append(ne)
    return outlist

def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = orderByCity(datalist)
    outlist = export_OrderDictList(outlist)
    return outlist



if __name__ == '__main__':
    '''
            '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
