from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from utils.config import orderByCity
from collections import OrderedDict

def loadSQL(start_day, end_day):
    xjg_sql = YYSQLTemplate.xjg_stats([],
                                      "city_id",
                                      " toInt32(city_id) city_id, count(distinct vehicle_no,vehicle_color) xjg_count ",
                                      start_day, end_day)
    v_jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([], "vehicle_no,vehicle_color,city_id",
                                                                    '''
                                                                    city_id,vehicle_no,vehicle_color,any(city_short_name) city_name,
                                                                    if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, 0,1) is_ok,
                                                                    min(never_gps) is_never_gps,min(never_alarm) is_never_alarm, min(never_acc) is_never_acc
                                                                    ''', start_day=start_day, end_day=end_day)
    jg_sql = SQLHelper().table(v_jg_sql).groupBy('city_id').select('''
            city_id,
            any(city_name) city_name,
            count(1) jg_count,
            sum(is_ok) ok_count,
            sum(is_never_gps) never_gps_count,
            sum(is_never_alarm) never_alarm_count,
            sum(is_never_acc) never_acc_count,
            FLOOR(ok_count/jg_count,4) ok_rate
    ''')
    sql = SQLHelper().table(jg_sql,'A').leftJoin()\
        .table(xjg_sql,'B').on('A.city_id=B.city_id')\
        .select('''
            city_name,
            xjg_count,
            jg_count,
            ok_count,
            never_gps_count,
            never_alarm_count,
            never_acc_count,
            ok_rate
        ''')
    return sql


def main(start_day, end_day):
    sql = loadSQL(start_day,end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    outlist = db.query_to_dictList(sql)
    db.close()
    outlist1 = []
    for e in outlist:
        ne = OrderedDict()
        ne['pid'] = 0
        ne['city_name'] = e['city_name']
        ne['xjg_count'] = e['xjg_count'] if e['xjg_count'] > e['jg_count'] else e['jg_count']
        ne['jg_count'] = e['jg_count']
        ne['ok_count'] = e['ok_count']
        ne['never_gps_count'] = e['never_gps_count']
        ne['never_alarm_count'] = e['never_alarm_count']
        ne['never_acc_count'] = e['never_acc_count']
        ne['ok_rate'] = f"{round(e['ok_rate'] * 100,2) }%"
        outlist1.append(ne)
    city_ok_list = orderByCity(outlist1)
    city_ok_list = list(filter(lambda o:o['city_name'],city_ok_list))
    return city_ok_list


if __name__ == '__main__':
    start, end = TimeUtils.getLastWeekRange()
    main(start, end)