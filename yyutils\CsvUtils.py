import csv, os
import shutil
import math, re
import subprocess
from yyutils.CharUtils import detectEncoding
from yyutils.PathUtil import get_filePath_and_fileName
from yyutils.DateUtil import TimeUtils

csv.field_size_limit(500 * 1024 * 1024)


class Dialect(type):
    encoding = None
    delimiter = None
    prehandler = None
    printFormat = None


class basic_dialect(metaclass=Dialect):
    encoding = 'auto'
    delimiter = ','
    prehandler = lambda o: o.replace('NaN', '')
    printFormat = '{} {filename} -- '


class complex_dialect(metaclass=Dialect):
    encoding = 'auto'
    delimiter = ','
    prehandler = lambda o: o.replace('\0', '').replace('null', '').replace('NaN', '')
    printFormat = None


class CsvReader:
    def __init__(self, infile, encoding=None, delimiter=None, print_count=10000,
                 print_title=None, dialect: Dialect = basic_dialect):
        _, self.filename = get_filePath_and_fileName(infile)
        self.encoding = encoding if encoding else dialect.encoding
        self.__delimiter = delimiter if delimiter else dialect.delimiter
        self.__prehandler = dialect.prehandler
        if self.encoding == 'auto':
            self.encoding = detectEncoding(infile)
        self.__f = open(infile, newline='', encoding=self.encoding)
        if self.__prehandler:
            self.__reader = csv.DictReader((self.__prehandler(line) for line in self.__f), delimiter=self.__delimiter)
        else:
            self.__reader = csv.DictReader(self.__f, delimiter=self.__delimiter)
        self.header = self.__reader.fieldnames
        self.count = 0
        self.__print_count = print_count
        self.__print_title = print_title if print_title else ''
        self.__log = '{asctime} {title} 读取 {filename} {count} 行..'

    def __iter__(self):
        print('-------------------------')
        return self

    def __next__(self):
        self.count += 1
        if self.count % self.__print_count == 0:
            print(self.__log.format(asctime=TimeUtils.now(), title=self.__print_title,
                                    filename=self.filename, count=self.count))
        row = next(self.__reader)
        return row

    def close(self):
        self.__log = self.__log[:-2] + ',读取完毕！'
        print(self.__log.format(asctime=TimeUtils.now(), title=self.__print_title,
                                filename=self.filename, count=self.count))
        self.__f.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class CsvWriter:
    def __init__(self, otfile, header, encoding='utf-8', delimiter=','):
        self.otfile = otfile
        self.header = header
        self.encoding = encoding
        self.__f = open(otfile, 'w+', newline='', encoding=self.encoding)
        self.__writer = csv.DictWriter(self.__f, fieldnames=header, delimiter=delimiter)

    def writeheader(self):
        self.__writer.writeheader()

    def writerows(self, rowdicts):
        self.__writer.writerows(rowdicts)

    def writerow(self, rowdict):
        self.__writer.writerow(rowdict)

    def close(self):
        self.__f.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


if __name__ == '__main__':
    reader = CsvReader(r'C:\工作目录\粤运\doc\工单处理\月报\厂商统计.csv',
                       dialect=complex_dialect)
    header = reader.header
    outlist = []
    with reader:
        for e in reader:
            outlist.append(e)
    writer = CsvWriter(r'C:\工作目录\粤运\doc\工单处理\月报\厂商统计_out.csv', header)
    writer.writeheader()
    writer.writerows(outlist)
    writer.writerow(outlist[-1])
    writer.close()
