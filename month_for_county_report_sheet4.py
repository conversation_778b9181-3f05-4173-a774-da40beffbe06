from yyutils import *
from utils.YYSQLTemplate import YY<PERSON><PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath, orderByCityVT
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    jgsql = YYSQLTemplate.wide_stats([
        'exception_type = 2'
    ],
        'vehicle_no,vehicle_color',
        ''' 
            vehicle_no,
            dictGetString('gdispx_data.sys_dict_item','label',(toString(vehicle_color),'vehicle_color')) vehicle_color,
            any(city_id) city_id ,
            any(city_short_name) city_name,
            any(area_name) area_name,
            any(owner_id) owner_id ,
            any(owner_name) owner_name ,
            any(vehicle_type) vehicle_type,
            dictGetString('gdispx_data.sys_dict_item','label',(toString(vehicle_type),'vehicle_type')) vehicle_type_name,
            any(third_party_name) third_party_name,
            count(1) ex_days
            ''', start_day=start_day, end_day=end_day)
    print(jgsql)
    return jgsql


def getTop10(datalist):
    city_g = list_2_dictlist(datalist, 'city_id')
    outlist = []
    for _, g in city_g.items():
        vt_g = list_2_dictlist(g, 'vehicle_type')
        for vt, sg in vt_g.items():
            sg.sort(key=lambda o: - o['ex_days'])
            if len(sg) > 10:
                tmplist = list(filter(lambda o: o['ex_days'] >= sg[9]['ex_days'], sg))
            else:
                tmplist = sg[:10]
            outlist.extend(tmplist)
    outlist1 = []
    for e in outlist:
        ne = OrderedDict(zip([
            'city_name', 'area_name', 'vehicle_no', 'vehicle_color',
            'vehicle_type', 'third_party_name', 'owner_name', 'ex_days'
        ], [
            e['city_name'], e['area_name'], e['vehicle_no'],
            e['vehicle_color'],
            e['vehicle_type_name'], e['third_party_name'],
            e['owner_name'], e['ex_days']
        ]))
        outlist1.append(ne)
    outlist = orderByCity(outlist1, pid=False)
    return outlist


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = getTop10(datalist)
    return outlist


if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
