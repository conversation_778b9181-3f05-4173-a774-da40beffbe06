#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
from datetime import datetime, timedelta
from apscheduler.schedulers.blocking import BlockingScheduler
from main import main
import io_province_report
import vehicle_week_report
import vehicle_detail_report
import alarm_level_week_report
import alarm_week_report_v2 as alarm_week_report
import alarm_detail_report
import system_owner_login_week_report
import system_week_report
import need_acc_alarm_detail_report
import everyday_accessory_lose
import zhonghuo_inline
import do_vehicle_wide_day_all_report
import do_login_week_report
import do_zhonghuo_login_week_report
import do_inquire_daily_report
from yyutils import *
import week_main
import vt4_online_week_report
import exception_week_report
import do_user_day_all_report


def do_log_in_report():
    start = TimeUtils.DeltaDay(-7)
    end = TimeUtils.DeltaDay(-1)
    do_login_week_report.main(start, end)


def cal_track_job():
    """
    计算轨迹数据job
    :return:
    """
    yesterday = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    main(yesterday)
    print('ok! wait next...')


def report_wide_table():
    yesterday = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    do_vehicle_wide_day_all_report.main(yesterday)


def report_user_wide_table():
    yesterday = TimeUtils.DeltaDay(-1, DateFormat.Y_m_d.value)
    do_user_day_all_report.main(yesterday)


def report_job_sunday():
    """
    每周日报表
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today()
    # 一周之前
    begin_date = (today - timedelta(days=7)).strftime('%Y-%m-%d')
    begin_date_1day = (today - timedelta(days=1)).strftime('%Y-%m-%d')
    end_date = today.strftime('%Y-%m-%d')
    # 5大指标
    vehicle_week_report.export(begin_date, end_date)
    vehicle_detail_report.export(begin_date, end_date)
    # 风险等级
    alarm_level_week_report.export(begin_date, end_date)
    # 合格率(明细只能查一天)
    alarm_week_report.export(begin_date, end_date)
    alarm_detail_report.export(begin_date_1day, end_date)
    # 系统监控
    system_owner_login_week_report.export(begin_date, end_date)
    system_owner_login_week_report.export1(begin_date, end_date)
    # # 压缩打包
    # os.system(
    #     'zip -q report/report_{}_{}.zip *.csv'.format(begin_date.replace('-', '')[-4:],
    #                                                   end_date.replace('-', '')[-4:]))
    # os.system('rm -f *.csv')
    print('ok! wait next...')


def report_job_thursday():
    """
    每周四报表
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today()
    # 一周之前
    begin_date = (today - timedelta(days=7)).strftime('%Y-%m-%d')
    end_date = today.strftime('%Y-%m-%d')
    system_week_report.export(begin_date, end_date)
    # # 压缩打包
    # os.system(
    #     'zip -q report/system_report_{}_{}.zip *.csv'.format(begin_date.replace('-', '')[-4:],
    #                                                          end_date.replace('-', '')[-4:]))
    # os.system('rm -f *.csv')
    print('ok! wait next...')


def report_job_everyday():
    """
    每天报表
    :return:
    """
    # 获取今天(现在时间)
    today = datetime.today()
    # 2天之前
    begin_date = (today - timedelta(days=2)).strftime('%Y-%m-%d')
    end_date = (today - timedelta(days=1)).strftime('%Y-%m-%d')
    need_acc_alarm_detail_report.export(begin_date, end_date)
    # 每日附件丢失表
    everyday_accessory_lose.export(end_date)
    print('ok! wait next...')


def zip_today_all_csv():
    """
    压缩打包当天所有报表
    :return:
    """
    # 压缩打包
    os.system(f'zip -q report/{TimeUtils.Today(DateFormat.Ymd.value)}.zip *.csv')
    os.system(f'zip -q report/{TimeUtils.Today(DateFormat.Ymd.value)}.zip *.xlsx')
    os.system('rm -f *.csv')
    os.system('rm -f *.xlsx')
    print('ok! wait next...')


def io_province_daily_job():
    io_province_report.main()
    print('出入省统计完成..')


def report_job_Monday():
    """
    每周一重货相关
    :return:
    """
    start = TimeUtils.DeltaDay(-7)
    end = TimeUtils.DeltaDay(-1)
    do_zhonghuo_login_week_report.main(start, end)
    # zhonghuo_inline.export(begin_date, end_date)
    print('ok! wait next...')


def do_inquire_report():
    inc_day = TimeUtils.DeltaDay(-1)
    do_inquire_daily_report.main(inc_day)


def do_week_report():
    start, end = TimeUtils.getLastWeekRange()
    try:
        week_main.main(start, end)
        vt4_online_week_report.main(start, end)
        exception_week_report.main(start, end)
    except Exception as ex:
        print('周报问题',ex)


if __name__ == '__main__':
    """
    定点输出的csv全部放到当天的文件夹
    所有任务最好都在6点前要执行完
    每日凌晨6点将当天文件夹打包成zip
    """
    do_week_report()
