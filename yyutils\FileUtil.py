import csv, os
import shutil
import math, re
# import pandas as pd
import subprocess

csv.field_size_limit(500 * 1024 * 1024)


def read_csv(infile, encode='utf-8', getHeader=False, sep=','):
    outlist = []
    with open(infile, 'r', newline='', encoding=encode) as csvfile:
        reader = csv.DictReader((line.replace('\0', '').replace('null', '') for line in csvfile), delimiter=sep)
        header = reader.fieldnames
        for e in reader:
            outlist.append(e)
        csvfile.close()
    if getHeader:
        return outlist, header
    return outlist


def write_csv(infile, headers=[], outlist=[], encode='utf-8', sep=','):
    with open(infile, 'w', newline='', encoding=encode) as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers, delimiter=sep)
        writer.writeheader()
        writer.writerows(outlist)


def write_csv_no_header(infile, headers=[], outlist=[], encode='utf-8'):
    with open(infile, 'w', newline='', encoding=encode) as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers)
        writer.writerows(outlist)


def readCsv(infile, encode='utf-8', print_count=10000, sep=',', print_title=''):
    count = 0
    with open(infile, newline='', encoding=encode) as csvfile:
        reader = csv.DictReader((line.replace('\0', '').replace('null', '') for line in csvfile), delimiter=sep)
        for e in reader:
            count += 1
            if print_count:
                if count % print_count == 0:
                    print(print_title, count)
            yield e
        if print_count:
            print(print_title, 'total', count)
        csvfile.close()


def read_by_trunk(reader, num=10000):
    count = 0
    gdict = {}
    for e in reader:
        gdict.setdefault(count, []).append(e)
        if gdict and gdict[count] and len(gdict[count]) == num:
            cur_count = count
            count += 1
            if isinstance(reader, set):
                yield set(gdict.pop(cur_count))
            else:
                yield gdict.pop(cur_count)
    if gdict:
        if isinstance(reader, set):
            yield set(gdict.pop(count))
        else:
            yield gdict.pop(count)


def read_a_group(reader, col='pid'):
    gdict = {}
    cur_pid = ''
    for e in reader:
        if not cur_pid:
            cur_pid = e[col]
        pid = e[col]
        if pid == cur_pid:
            gdict.setdefault(cur_pid, []).append(e)
        else:
            gdict.setdefault(pid, []).append(e)
            o_pid = cur_pid
            cur_pid = pid
            yield gdict.pop(o_pid)
    if gdict:
        yield gdict.pop(cur_pid)


def resetGid(inlist, col='pid'):
    pid = 0
    for g in read_a_group(inlist, col):
        pid += 1
        for e in g:
            e[col] = pid


def write_csv_Header(otfile, header, encode='utf-8', sep=','):
    with open(otfile, 'w', newline='', encoding=encode) as dstfile:
        writer = csv.DictWriter(dstfile, fieldnames=header, delimiter=sep)
        writer.writeheader()
        dstfile.close()


def write_csv_a(output_file, header, outlist, encode='utf-8', sep=','):
    with open(output_file, 'a', newline='', encoding=encode) as dstfile:
        if outlist:
            writer = csv.DictWriter(dstfile, fieldnames=header, delimiter=sep)
            writer.writerows(outlist)
        dstfile.close()


def getHeader(filename, encode='utf-8', sep=','):
    if not os.path.exists(filename):
        return []
    f = open(filename, 'r', encoding=encode)
    headers = f.readline().strip('\r\n').replace('\"', '').split(sep)
    return headers


def list_2_dictlist(inlist, key):
    out_dictlist = {}
    for e in inlist:
        key_value = e[key]
        if not key_value:
            continue
        if key_value not in out_dictlist:
            out_dictlist[key_value] = [e]
        else:
            out_dictlist[key_value].append(e)
    return out_dictlist


def list_2_dict(inlist, key):
    out_dict = {}
    for e in inlist:
        key_value = e[key]
        if not key_value:
            continue
        out_dict[key_value] = e
    return out_dict


def movefile(srcfile, dstfile):
    if not os.path.isfile(srcfile):
        print("%s not exist!" % (srcfile))
    else:
        fpath, fname = os.path.split(dstfile)
        if not os.path.exists(fpath):
            os.makedirs(fpath)
        shutil.move(srcfile, dstfile)
        print("move %s -> %s" % (srcfile, dstfile))


def copyfile(srcfile, dstfile):
    if not os.path.isfile(srcfile):
        print("%s not exist!" % (srcfile))
    else:
        fpath, fname = os.path.split(dstfile)
        if not os.path.exists(fpath):
            os.makedirs(fpath)
        shutil.copyfile(srcfile, dstfile)
        print("copy %s -> %s" % (srcfile, dstfile))


def cat(srcfile, dstfile):
    if not os.path.exists(dstfile):
        copyfile(srcfile, dstfile)
        return
    h = getHeader(dstfile)
    reader = readCsv(srcfile, 'utf-8')
    for g in read_by_trunk(reader):
        write_csv_a(dstfile, h, g)


def removeDirs(path):
    try:
        if os.path.exists(path):
            shutil.rmtree(path)
    except Exception as ex:
        print('del', path, 'failed')


def removeFile(filepath):
    try:
        os.remove(filepath)
    except Exception as ex:
        print('del', filepath, 'failed!')


def convert_encoding(in_path, out_path, src_code='gb18030', dst_code='utf-8'):
    r_list, header = read_csv(in_path, src_code, True)
    write_csv(out_path, header, r_list, dst_code)


def divide_file(file_path, prefix='', num=80, tmp_path=None):
    record_num = len(open(file_path, 'r', encoding='utf-8').readlines()) - 1
    if record_num == 0:
        print('src_data.csv is empty, exit()')
        return False
    chunk_size = int(math.ceil(float(record_num) / float(num)))
    if chunk_size < 1:
        return False
    dir_path = os.path.dirname(file_path)
    if not tmp_path:
        tmp_path = os.path.join(dir_path, "tmp")
    h = getHeader(file_path)
    pid = 0
    for g in read_by_trunk(readCsv(file_path), chunk_size):
        pid += 1
        sub_dir = os.path.join(tmp_path, "%d" % pid)
        if not os.path.exists(sub_dir):
            os.makedirs(sub_dir)
        tmp_file_path = os.path.join(sub_dir, "in_%s_%d.csv" % (prefix, pid))
        write_csv(tmp_file_path, h, g)
    return True


def getTotalPageCount(infile, encoding, chunk_size):
    total_count = -1
    for total_count, line in enumerate(open(infile, 'rU', encoding=encoding)):
        pass
    total_page_count = math.ceil(total_count / chunk_size)
    return total_page_count


# def cut_file(infile, chunk_size=1000000, otpath='', encoding='utf-8', sep=','):
#     bpath, fn = get_filePath_and_fileName(infile)
#     fn = fn.split('.')[0]
#     if not otpath:
#         otpath = createOutpath(bpath, fn + '_pieces')
#     else:
#         if not os.path.exists(otpath):
#             os.makedirs(otpath)
#     total_page_count = getTotalPageCount(infile, encoding, chunk_size)
#     params = []
#     for i in range(total_page_count):
#         otfile = os.path.join(otpath, f'{fn}_{i + 1}.csv')
#         params.append((infile, otfile, i, chunk_size, total_page_count, encoding, sep))
#     with ProcessPoolExecutor(max_workers=min(10, len(params), os.cpu_count() - 3)) as executor:
#         for r in executor.map(cut_pieces, params):
#             pass
#     return otpath


# def cut_pieces(args):
#     infile, otfile, page_no, chunk_size, total_page_count, encoding, sep = args
#     h = getHeader(infile, encode=encoding, sep=sep)
#     if page_no == 0:
#         skip_rows = 1
#         n_rows = chunk_size
#     else:
#         skip_rows = page_no * chunk_size + 1
#         n_rows = chunk_size
#     company_df = pd.read_csv(infile,
#                              skiprows=skip_rows,
#                              dtype=str,
#                              nrows=n_rows,
#                              sep=sep,
#                              encoding=encoding,
#                              low_memory=False,
#                              engine='c',
#                              header=None,
#                              names=h)
#     company_df.to_csv(otfile, index=False, sep=',')


def subprocess_popen(statement):
    p = subprocess.Popen(statement, shell=True, stdout=subprocess.PIPE)  # 执行shell语句并定义输出格式
    while p.poll() is None:  # 判断进程是否结束（Popen.poll()用于检查子进程（命令）是否已经执行结束，没结束返回None，结束后返回状态码）
        if p.wait() is not 0:  # 判断是否执行成功（Popen.wait()等待子进程结束，并返回状态码；如果设置并且在timeout指定的秒数之后进程还没有结束，将会抛出一个TimeoutExpired异常。）
            print("命令执行失败，请检查设备连接状态")
            return False
        else:
            re = p.stdout.readlines()  # 获取原始执行结果
            result = []
            for i in range(len(re)):  # 由于原始结果需要转换编码，所以循环转为utf8编码并且去除\n换行
                res = re[i].decode('utf-8').strip('\r\n')
                result.append(res)
            return result


def merge_file(tmp_dir, outputFile, file_name):
    sub_dirs = os.listdir(tmp_dir)
    out_list1 = []
    for d in sub_dirs:
        sub_dir = os.path.join(tmp_dir, d)
        if not os.path.isdir(sub_dir):
            continue
        out_list1.append(os.path.join(sub_dir, file_name))
    combine_files(out_list1, outputFile)
    return outputFile


def combine_files(path_list, out_path):
    if not path_list:
        return
    new_list = [x for x in path_list if os.path.exists(x)]
    with open(out_path, 'w', newline='', encoding='utf8') as fs:
        for i, in_path in enumerate(new_list):
            fs1 = open(in_path, 'r', newline='', encoding='utf8')
            if i == 0:
                fs.write(fs1.readline())
            else:
                fs1.readline()
            for l in fs1:
                fs.write(l)


def readDir(inpath, readOnlyFile=True, readOnlyDir=False, filter_reg='', return_mode=1):
    fs = os.listdir(inpath)
    fnlist = []
    for f in fs:
        infile = os.path.join(inpath, f)
        if filter_reg:
            if not re.findall(filter_reg, f):
                continue
        if readOnlyDir and not os.path.isdir(infile):
            continue
        elif not readOnlyDir and readOnlyFile and not os.path.isfile(infile):
            continue
        fnlist.append((infile, f))
    for f, fn in fnlist:
        if return_mode == 1:
            yield f
        else:
            yield f, fn
