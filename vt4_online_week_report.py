from yyutils import *
from utils.YYSQLTemplate import YYSQLTemplate
from utils.config import bpath, CITY_COUNT, WEEK_COUNT,orderByCity
from collections import OrderedDict

template_file = './templates/template_week_近一周全省重型货车上线情况（{start_day}-{end_day})【数值版】.xlsx'

title_row_end = 3


def loadSQL(start_day, end_day):
    xjg_sql = YYSQLTemplate.xjg_stats(["vehicle_type = '4' and city_id != ''"],
                                      "city_id",
                                      " toInt32(city_id) city_id, count(distinct vehicle_no,vehicle_color) xjg_count ",
                                      start_day, end_day)
    jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats(["vehicle_type = 4"],
                                                                  "city_id",
                                                                  "city_id,any(city_short_name) city_name,count(distinct vehicle_no) jg_count",
                                                                  start_day=start_day, end_day=end_day)
    day_online_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats(["vehicle_type = 4", "online = 1"],
                                                                          "city_id,inc_day",
                                                                          "city_id,inc_day,count(1) online_count",
                                                                          start_day=start_day, end_day=end_day)

    return xjg_sql, jg_sql, day_online_sql


def validate(xjgList, jgList, dayOnlineList):
    if len(xjgList) != CITY_COUNT:
        raise RuntimeError("运政日结异常！")
    if len(jgList) != CITY_COUNT:
        raise RuntimeError("宽表日结异常！")
    city_g = list_2_dictlist(dayOnlineList, 'city_id')
    if len(city_g) != CITY_COUNT:
        raise RuntimeError("宽表日结异常！")
    for city, g in city_g.items():
        day_g = list_2_dictlist(g, 'inc_day')
        if len(day_g) != WEEK_COUNT:
            raise RuntimeError("宽表日结缺少某天数据!")


def packageData(xjgList, jgList, dayOnlineList):
    city_xjg = list_2_dict(xjgList, 'city_id')
    city_jg = list_2_dict(jgList, 'city_id')
    city_online = list_2_dictlist(dayOnlineList, 'city_id')
    outlist = []
    for city, xjg in city_xjg.items():
        jg = city_jg.get(city)
        days_online = city_online.get(city)
        day_e = list_2_dict(days_online, 'inc_day')
        days_online_list = list(day_e.items())
        days_online_list.sort(key=lambda o: o[0])
        headers = ['pid', 'city_name', 'xjg_count', 'jg_count'] + [t[0] for t in days_online_list]
        values = [1, jg['city_name'], xjg['xjg_count'], jg['jg_count']] + [t[1]['online_count'] for t in
                                                                           days_online_list]
        ne = OrderedDict(zip(headers, values))
        outlist.append(ne)
    return outlist





def doSum(datalist: list):
    keys = list(datalist[0].keys())
    ne = OrderedDict(zip(keys, [sum([e[k] for e in datalist]) if k not in ('city_name','pid') else '—' for k in keys]))
    ne['pid'] = '合计'
    datalist.append(ne)


def writeByTemplate(datalist, start_day, end_day):
    _, fn = get_filePath_and_fileName(template_file)
    start_day_format = start_day.replace('-','.')
    end_day_format = end_day.replace('-','.')[5:] if start_day[:4] == end_day[:4] else  end_day.replace('-','.')
    fn = fn.format(start_day=start_day_format, end_day=end_day_format).strip('template_week_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    sheet1 = elhander.activeWorksheet('Sheet1')
    row_styles = elhander.copyRowStyle(sheet1, 4)
    elhander.clearData(sheet1, 4)
    datalist = orderByCity(datalist)
    doSum(datalist)
    sheet1[1][0].value =  sheet1[1][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet1, datalist, row_styles)
    elhander.save()


def main(start_day, end_day):
    xjg_sql, jg_sql, day_online_sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    xjgList = db.query_to_dictList(xjg_sql)
    jgList = db.query_to_dictList(jg_sql)
    dayOnlineList = db.query_to_dictList(day_online_sql)
    validate(xjgList, jgList, dayOnlineList)
    datalist = packageData(xjgList, jgList, dayOnlineList)
    writeByTemplate(datalist, start_day, end_day)


if __name__ == '__main__':
    # start, end = TimeUtils.getLastWeekRange()
    # main(start, end)
    print(YYSQLTemplate.xjg_stats(["vehicle_type = '4' and city_id != ''"],
                                      "city_id",
                                      " toInt32(city_id) city_id, count(distinct vehicle_no) xjg_count ",
                                      'start_day', 'end_day'))