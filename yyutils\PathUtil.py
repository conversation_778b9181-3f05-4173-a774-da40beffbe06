# encoding=utf-8
import os, sys, time


def getTime_for_fileStampDetail():
    now = time.time()
    timestruct = time.localtime(now)
    dateStr = time.strftime('%Y%m%d%H%M', timestruct)
    return dateStr[:4] + '年' + dateStr[4:6] + '月' + dateStr[6:8] + '号' + dateStr[8:10] + '时' + dateStr[10:] + '分'


def join_path(path, *paths):
    return os.path.join(path, *paths)


def get_filePath_and_fileName(fullpath):
    if os.path.isdir(fullpath):
        return fullpath, ''
    parent_path = os.path.dirname(fullpath)
    original_file_name = os.path.split(fullpath)[-1]
    return parent_path, original_file_name


def get_js_path():
    '''
        获取脚本路径，可调用,
    '''
    return os.path.split(os.path.realpath(sys.argv[0]))[0]


def get_js_path0():
    '''
        此方法不可调用，复制使用
    '''
    return os.path.split(os.path.realpath(__file__))[0]


def get_cwd_path():
    return os.getcwd()


def makesure_dir(in_dir):
    if not os.path.exists(in_dir):
        os.makedirs(in_dir)


def createdir(parent_dir, *child_dirname, bk=False):
    dirpath = os.path.join(parent_dir, *child_dirname)
    if not os.path.exists(dirpath):
        os.makedirs(dirpath)
    elif bk:
        if os.path.exists(dirpath):
            parent_pach = os.path.join(parent_dir, *child_dirname[:-1])
            bk_path = os.path.join(parent_pach, child_dirname[-1] + '_备份_' + getTime_for_fileStampDetail())
            if not os.path.exists(bk_path):
                try:
                    os.renames(dirpath, bk_path)
                    os.makedirs(dirpath)
                except Exception as ex:
                    print(ex)
                    print('备份失败')
    return dirpath


def createOutpath(target_file, *outpath_name, bk=False):
    '''
    在目标文件所在目录下生成输出路径
    :param target_file:
    :param outpath_name:
    :return:
    '''
    dirname, filename = get_filePath_and_fileName(target_file)
    # outpath = createdir(dirname, *outpath_name, bk)
    if isinstance(outpath_name[-1], bool):
        outpath = createdir(dirname, *outpath_name[:-1], bk=outpath_name[-1])
    else:
        outpath = createdir(dirname, *outpath_name, bk=bk)
    return outpath
