import os
import datetime
from yyutils import *



bpath = r'/data/gp/jgCheliang'


def main():
    db = DBUtil(DBType.MYSQL.value)

    start_day = datetime.date.today()

    fileName = f'jg{start_day}.csv'
    zipFileName = f'jg'
    print(fileName)
    print(join_path(bpath,fileName))
    end_day = start_day
    sql = f'''
            SELECT * from yunzheng.jg_cheliang where service_result =1 ;
        '''
    db.query_to_csv(sql, join_path(bpath, fileName))


    db.close()
    os.system( f"cd {bpath} && zip -r {zipFileName}.zip {fileName} && mv {zipFileName}.zip /data/track_report/report/")


if __name__ == '__main__':
    main()
