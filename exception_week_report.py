from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from utils.config import bpath,orderByCity, orderByCityVT,vehicle_type_dict,vehicle_color_dict
from collections import OrderedDict

template_file = './templates/template_week_疑似屏蔽信号运行车辆情况周报（{start_day}-{end_day}).xlsx'


def loadSQLByCity(start_day, end_day):
    jgsql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([],
                  'vehicle_no,vehicle_color,city_id','''
                  city_id,
                  any(city_short_name) city_name,
                  vehicle_no,
                  vehicle_color,
                  if(any(vehicle_type) = 1, 1,0 ) is_vt1,
                  if(any(vehicle_type) = 3, 1,0 ) is_vt3,
                  if(any(vehicle_type) = 4, 1,0 ) is_vt4,
                  if(max(exception_type)=2 and is_vt1 = 1,1,0) is_except_vt1,
                  if(max(exception_type)=2 and is_vt3 = 1,1,0) is_except_vt3,
                  if(max(exception_type)=2 and is_vt4 = 1,1,0) is_except_vt4
                  ''',start_day=start_day, end_day=end_day)
    citysql = SQLHelper().table(jgsql,'A')\
        .groupBy('city_id').select('''
            city_id,
            any(city_name) city_name,
            count(1) jg_count,
            sum(is_except_vt1) is_except_vt1_count,
            sum(is_except_vt3) is_except_vt3_count,
            sum(is_except_vt4) is_except_vt4_count,
            is_except_vt1_count+is_except_vt3_count+is_except_vt4_count except_count,
            FLOOR(except_count/ jg_count,4) except_rate
        ''')
    return citysql


def convert_city_datalist(datalist):
    outlist = []
    for e in datalist:
        ne = OrderedDict(zip([
            'pid','city_name','vt1','vt3','vt4','total','except_rate'
        ],[
            0,e['city_name'],e['is_except_vt1_count'],e['is_except_vt3_count'],
            e['is_except_vt4_count'],e['except_count'],f"{round(e['except_rate']*100,2)}%"
        ]))
        outlist.append(ne)
    outlist = orderByCity(outlist)
    se = OrderedDict(zip([
        'pid', 'city_name', 'vt1', 'vt3', 'vt4', 'total', 'except_rate'
    ],[
        '合计','-',sum([e['is_except_vt1_count'] for e in datalist]),
        sum([e['is_except_vt3_count'] for e in datalist]),
        sum([e['is_except_vt4_count'] for e in datalist]),
        sum([e['is_except_vt1_count'] for e in datalist])+
        sum([e['is_except_vt3_count'] for e in datalist])+
        sum([e['is_except_vt4_count'] for e in datalist]),
        str(round((sum([e['is_except_vt1_count'] for e in datalist])+
        sum([e['is_except_vt3_count'] for e in datalist])+
        sum([e['is_except_vt4_count'] for e in datalist]))*100/
              sum([e['jg_count'] for e in datalist]),2)) + '%'
    ]))
    outlist.append(se)
    return outlist



def do_stats_city(start_day, end_day):
    citysql = loadSQLByCity(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(citysql)
    db.close()
    outlist = convert_city_datalist(datalist)
    return outlist


def loadSQLByCityTop10(start_day, end_day):
    jgsql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([
        'exception_type = 2'
    ],
                     'vehicle_no,vehicle_color',
                  ''' 
                      vehicle_no,
                      vehicle_color,
                      any(city_id) city_id ,
                      any(city_short_name) city_name,
                      any(area_name) area_name,
                      any(owner_id) owner_id ,
                      any(owner_name) owner_name ,
                      any(vehicle_type) vehicle_type,
                      count(1) ex_days
                      ''', start_day=start_day, end_day=end_day)
    return jgsql


def getTop10(datalist):
    city_g = list_2_dictlist(datalist,'city_id')
    outlist = []
    for _,g in city_g.items():
        vt_g = list_2_dictlist(g,'vehicle_type')
        for vt,sg in vt_g.items():
            sg.sort(key=lambda o: - o['ex_days'])
            if len(sg) > 10:
                tmplist = list(filter(lambda o:o['ex_days']>= sg[9]['ex_days'],sg))
            else:
                tmplist = sg[:10]
            outlist.extend(tmplist)
    outlist1 = []
    for e in outlist:
        ne = OrderedDict(zip([
            'city_name','area_name','vehicle_no','vehicle_color',
            'vehicle_type','owner_name','ex_days'
        ],[
            e['city_name'],e['area_name'],e['vehicle_no'],
            vehicle_color_dict.get(e['vehicle_color']),
            vehicle_type_dict.get(e['vehicle_type']),
            e['owner_name'] , e['ex_days']
        ]))
        outlist1.append(ne)
    outlist = orderByCityVT(outlist1,pid=False)
    return outlist


def do_stats_city_top10(start_day, end_day):
    jgsql = loadSQLByCityTop10(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(jgsql)
    db.close()
    outlist = getTop10(datalist)
    return outlist


def loadSQLByOwnerTop10(start_day, end_day):
    exsql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([
        'exception_type = 2'
    ],
        'owner_id',
        ''' 
            any(city_id) city_id ,
            any(city_short_name) city_name,
            any(area_name) area_name,
            owner_id ,
            any(owner_name) owner_name ,
            any(vehicle_type) vehicle_type,
            count(distinct vehicle_no) ex_count
            ''', start_day=start_day, end_day=end_day)
    jg_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([],'owner_id','''
            owner_id,
            count(distinct vehicle_no) jg_count
    ''',start_day=start_day,end_day=end_day)
    xjg_sql = YYSQLTemplate.xjg_stats([],'owner_id','''
            owner_id,
            count(DISTINCT vehicle_no,vehicle_color) xjg_count
    ''',start_day, end_day)
    sql = SQLHelper().table(exsql,'A')\
        .leftJoin().table(jg_sql,'B').on('A.owner_id=B.owner_id')\
        .leftJoin().table(xjg_sql,'C').on('A.owner_id=toUInt32(C.owner_id)') \
        .select('''
            city_id,
            city_name,
            area_name,
            owner_name,
            vehicle_type,
            if(xjg_count>jg_count,xjg_count,jg_count) xjg_count,
            jg_count,
            ex_count,
            FLOOR(ex_count/jg_count,4) ex_rate
        ''')
    return sql


def getOwnerTop10(datalist):
    city_g = list_2_dictlist(datalist, 'city_id')
    outlist = []
    for _, g in city_g.items():
        vt_g = list_2_dictlist(g, 'vehicle_type')
        for vt, sg in vt_g.items():
            sg.sort(key=lambda o: - o['ex_rate'])
            if len(sg) > 10:
                tmplist = list(filter(lambda o: o['ex_rate'] >= sg[9]['ex_rate'], sg))
            else:
                tmplist = sg
            outlist.extend(tmplist)
    outlist1 = []
    for e in outlist:
        ne = OrderedDict(zip([
            'city_name', 'area_name', 'owner_name',
            'vehicle_type',  'xjg_count','jg_count','ex_count','ex_rate'
        ], [
            e['city_name'], e['area_name'], e['owner_name'],
            vehicle_type_dict.get(e['vehicle_type']),
            e['xjg_count'],e['jg_count'],e['ex_count'],f"{round(e['ex_rate']*100,2)}%"
        ]))
        outlist1.append(ne)
    outlist = orderByCityVT(outlist1, pid=False)
    return outlist


def do_stats_owner_top10(start_day, end_day):
    jgsql = loadSQLByOwnerTop10(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(jgsql)
    db.close()
    outlist = getOwnerTop10(datalist)
    return outlist


def fill_sheet1(elhander, city_list, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("表1各地市总体情况")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[1][0].value = sheet[1][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, city_list, row_styles)


def fill_sheet2(elhander, city_top10_list, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("表2次数最多的车辆")
    row_styles = elhander.copyRowStyle(sheet, 5)
    elhander.clearData(sheet, 5)
    sheet[3][0].value = sheet[3][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, city_top10_list, row_styles)


def fill_sheet3(elhander, owner_top10_list, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("表3占比最高的企业")
    row_styles = elhander.copyRowStyle(sheet, 5)
    elhander.clearData(sheet, 5)
    sheet[3][0].value = sheet[3][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, owner_top10_list, row_styles)


def writeByTemplate(city_list, city_top10_list, owner_top10_list,start_day, end_day):
    _, fn = get_filePath_and_fileName(template_file)
    start_day_format = start_day.replace('-', '.')
    end_day_format = end_day.replace('-', '.')[5:] if start_day[:4] == end_day[:4] else end_day.replace('-', '.')
    fn = fn.format(start_day=start_day_format, end_day=end_day_format).strip('template_week_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, city_list, start_day_format, end_day_format)
    fill_sheet2(elhander, city_top10_list, start_day_format, end_day_format)
    fill_sheet3(elhander, owner_top10_list, start_day_format, end_day_format)
    elhander.save()


def main(start_day, end_day):
    city_list = do_stats_city(start_day, end_day)
    city_top10_list = do_stats_city_top10(start_day, end_day)
    owner_top10_list = do_stats_owner_top10(start_day, end_day)
    writeByTemplate(city_list,city_top10_list,owner_top10_list,start_day, end_day)


if __name__ == '__main__':
    start, end = TimeUtils.getLastWeekRange()
    main(start, end)

