from cal import cal, cp_dist, cp_speed
import pandas as pd
import numpy as np
from typing import Tuple
#gps_time:datetime

def compute_distance_and_speed(lat1: np.ndarray, lon1: np.ndarray,
                              lat2: np.ndarray, lon2: np.ndarray,
                              time_diff: np.ndarray) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
    """
    向量化计算两点之间的距离和速度。

    :param lat1: 起始点纬度数组
    :param lon1: 起始点经度数组
    :param lat2: 结束点纬度数组
    :param lon2: 结束点经度数组
    :param time_diff: 时间差（秒）数组
    :return: 距离（米）、速度（公里/小时）
    """
    # 地球半径(米)
    R = 6371000

    # 将角度转换为弧度
    lat1, lon1 = np.radians(lat1), np.radians(lon1)
    lat2, lon2 = np.radians(lat2), np.radians(lon2)

    # Haversine公式计算距离
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
    c = 2 * np.arcsin(np.sqrt(a))
    distance = R * c

    # 计算速度（公里/小时）
    time_hours = time_diff / 3600.0
    speed = (distance / 1000.0) / time_hours  # 先将米转换为公里

    return distance, speed

df = pd.read_csv('X:\\duckdb\\041028913413.csv',
                 dtype={'longitude': 'float64', 'latitude': 'float64', 'sim_code': 'str'})
# longitude as x, latitude as y, gps_time as time
df['gps_time'] = pd.to_datetime(df['gps_time'])
df.reset_index(drop=True, inplace=True)
df = df.rename(columns={'longitude': 'x', 'latitude': 'y', 'gps_time': 'time'})
df = df.sort_values('time')
df = df[(df['x'] > 0) & (df['y'] > 0)]

# 添加前一个点的信息
df['prev_x'] = df['x'].shift()
df['prev_y'] = df['y'].shift()
df['prev_time'] = df['time'].shift()
df['prev_time_diff'] = (df['time'] - df['prev_time']).dt.total_seconds()

# 使用新函数计算距离和速度
df['distance'], df['speed'] = compute_distance_and_speed(
    df['prev_y'], df['prev_x'], df['y'], df['x'], df['prev_time_diff']
)

# 添加后一个点的信息
df['next_x'] = df['x'].shift(-1)
df['next_y'] = df['y'].shift(-1)
df['next_time'] = df['time'].shift(-1)
df['next_distance'], df['next_speed'] = compute_distance_and_speed(
    df['y'], df['x'], df['next_y'], df['next_x'],
    (df['next_time'] - df['time']).dt.total_seconds()
)


# 筛选异常高速的数据点
df = df[(df['speed'] >= 160) & (df['next_speed'] >= 160)]
df.to_csv('X:\\duckdb\\041028913413_abnormal.csv', mode='w', index=False, encoding='utf-8')
