from yyutils import *
from datetime import datetime, timedelta
def from_A_to_B(start, end):
    start_day = datetime.strptime(start, '%Y-%m-%d')
    if not end:
        end = start
    end_day = datetime.strptime(end, '%Y-%m-%d')
    n = 0
    while True:
        one = start_day + timedelta(days=n)
        if one <= end_day:
            n += 1
            yield str(one.strftime('%Y-%m-%d'))
        else:
            break

def main(inc_day):
    ck_sql = f'''
        SELECT 
            DISTINCT 
            user_id , toDate(create_time) create_time
        from gdispx_nk_data.sys_log sl 
        where title = '用户登录'
            and toDate(create_time) = '{inc_day}' 
    '''

    db = DBUtil(DBType.CLICKHOUSE.value,{
        'host':'**************',
        'port':'9000',
        'user':'default',
        'password':'<EMAIL>',
        'database':'gdispx_nk_data'
    })
    dlist = db.query_to_dictList(ck_sql)
    db.close()
    ins_sql = "INSERT INTO gdispx.sys_log_login(user_id,create_time)VALUES ({},{});"
    sql = ins_sql.format( '%s', '%s')
    db = DBUtil(DBType.MYSQL.value)
    for g in read_by_trunk(dlist,1000):
        db.client.executemany(sql,[(e['user_id'],e['create_time']) for e in g])
        db.conn.commit()
    db.close()


def create_history_log(start_day,end_day):
    for day in from_A_to_B(start_day,end_day):
        main(day)

if __name__ == '__main__':
    create_history_log('2023-05-01','2023-05-31')
