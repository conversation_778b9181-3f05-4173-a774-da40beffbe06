'''
自定义分组函数

'''

def read_by_trunk(reader, num=10000):
    count = 0
    gdict = {}
    for e in reader:
        gdict.setdefault(count, []).append(e)
        if gdict and gdict[count] and len(gdict[count]) == num:
            cur_count = count
            count += 1
            yield gdict.pop(cur_count)
    if gdict:
        yield gdict.pop(count)


def do_same_filter(g, idx_newIdx, idx_sameIdxs, same_g, unique_g, same_feature_fun=None, layer=1):
    if layer == 1:
        if not same_feature_fun:
            unique_g.extend(g)
            ln = len(g)
            for i in range(ln):
                idx_newIdx.setdefault(i, i)
        else:
            f_sg = {}
            blank_fv_g = []
            for e in g:
                fv = same_feature_fun(e)
                if not fv:
                    blank_fv_g.append(e)
                    continue
                f_sg.setdefault(fv, []).append(e)
            for _, sg in f_sg.items():
                unique_g.append(sg[0])
                for i in range(len(sg[1:])):
                    idx_sameIdxs.setdefault(len(unique_g) - 1, set()).add(len(same_g) + i)
                same_g.extend(sg[1:])
            unique_g.extend(blank_fv_g)
            ln = len(unique_g)
            for i in range(ln):
                idx_newIdx.setdefault(i, i)
        return ln
    unique_g.extend(g)
    ln = len(g)
    for i in range(ln):
        idx_newIdx.setdefault(i, i)
    return ln


def do_sub_group_core_layer1(g, is_a_group_fun, idx_newIdx, ln):
    n = 0
    for i in range(ln):
        for j in range(i + 1, ln):
            new_idx1 = idx_newIdx.get(i)
            new_idx2 = idx_newIdx.get(j)
            if new_idx1 != new_idx2 and is_a_group_fun(g[i], g[j]):
                n += 1
                if new_idx1 < new_idx2:
                    # idx_newIdx[j] = new_idx1
                    for bj in range(j+1):
                        if idx_newIdx[bj] == new_idx2:
                            idx_newIdx[bj] = new_idx1
                else:
                    # idx_newIdx[i] = new_idx2
                    for bi in range(i+1):
                        if idx_newIdx[bi] == new_idx1:
                            idx_newIdx[bi] = new_idx2

def collect_result(unique_g, same_g, idx_sameIdxs, idx_newIdx, sub_groups):
    newIdx_idxs = {}
    for idx, newIdx in idx_newIdx.items():
        newIdx_idxs.setdefault(newIdx, set()).add(idx)
    newIdx_idxs_list = list(newIdx_idxs.values())
    newIdx_idxs_list.sort(key=lambda o: -len(o))
    for sub_idxs in newIdx_idxs_list:
        sg = [unique_g[sub_idx] for sub_idx in sub_idxs]
        for idx in sub_idxs:
            if idx_sameIdxs.get(idx):
                same_list = [same_g[idx] for idx in idx_sameIdxs.get(idx)]
                sg.extend(same_list)
        sub_groups.append(sg)


def is_a_groups_fun(g1, g2, is_a_group_fun, same_feature_fun):
    for e1 in g1:
        for e2 in g2:
            if same_feature_fun and same_feature_fun(e1) == same_feature_fun(e2):
                return True
            if is_a_group_fun(e1, e2):
                return True
    return False


def do_sub_group_core_layer2(g, is_a_group_fun, idx_newIdx, ln, same_feature_fun):
    n = 0
    for i in range(ln):
        for j in range(i + 1, ln):
            new_idx1 = idx_newIdx.get(i)
            new_idx2 = idx_newIdx.get(j)
            if new_idx1 != new_idx2 and is_a_groups_fun(g[i], g[j], is_a_group_fun, same_feature_fun):
                n += 1
                if new_idx1 < new_idx2:
                    # idx_newIdx[j] = new_idx1
                    for bj in range(j+1):
                        if idx_newIdx[bj] == new_idx2:
                            idx_newIdx[bj] = new_idx1
                else:
                    # idx_newIdx[i] = new_idx2
                    for bi in range(i+1):
                        if idx_newIdx[bi] == new_idx1:
                            idx_newIdx[bi] = new_idx2


def do_a_round_sub_group(g, is_a_group_fun, sub_groups, same_feature_fun=None, layer=1):
    if layer == 1:
        if len(g) < 2:
            sub_groups.append(g)
            return
        idx_sameIdxs = {}
        same_g = []
        unique_g = []
        idx_newIdx = {}
        ln = do_same_filter(g, idx_newIdx, idx_sameIdxs, same_g, unique_g, same_feature_fun, 1)
        do_sub_group_core_layer1(unique_g, is_a_group_fun, idx_newIdx, ln)
        collect_result(unique_g, same_g, idx_sameIdxs, idx_newIdx, sub_groups)
        return
    if len(g) < 2:
        sub_groups.append(g)
        return
    idx_sameIdxs = {}
    same_g = []
    unique_g = []
    idx_newIdx = {}
    ln = do_same_filter(g, idx_newIdx, idx_sameIdxs, same_g, unique_g, layer=2)
    do_sub_group_core_layer2(unique_g, is_a_group_fun, idx_newIdx, ln, same_feature_fun=None)
    collect_result(unique_g, same_g, idx_sameIdxs, idx_newIdx, sub_groups)


def groups_layer2_to_layer1(layer2_sub_groups):
    groups = []
    for sg1 in layer2_sub_groups:
        g = []
        for sg2 in sg1:
            g.extend(sg2)
        groups.append(g)
    return groups


def do_sub_group(g, is_a_group_fun, same_feature_fun=None, round_size=500):
    '''
    自定义分组函数
    :param g:
    :param is_a_group_fun: 分组逻辑
    :param same_feature_fun: 特征值判断
    :param round_size: 分组size
    :return:
    '''
    # 先构建初始组
    layer1_sub_groups = []
    for sg in read_by_trunk(g, round_size):
        do_a_round_sub_group(sg, is_a_group_fun, layer1_sub_groups, same_feature_fun)
    if len(g) <= round_size:
        return layer1_sub_groups

    layer2_sub_groups = []
    do_a_round_sub_group(layer1_sub_groups, is_a_group_fun, layer2_sub_groups, same_feature_fun, 2)
    sub_groups = groups_layer2_to_layer1(layer2_sub_groups)

    return sub_groups


def is_a_group(e1, e2):
    if abs(e1 - e2) <= 10:
        return True
    return False


def same_feature_fun(e):
    return e


def do_sub_group_test():
    g = [1, 5, 30, 80, 35, 20, 66, 20, 4, 75, 100, 4, 31, 1]
    sub_groups = do_sub_group(g, is_a_group, same_feature_fun, 7)
    print(sub_groups)

if __name__ == '__main__':
    do_sub_group_test()