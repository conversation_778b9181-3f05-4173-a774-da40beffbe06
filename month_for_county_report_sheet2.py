from yyutils import *
from utils.YYSQLTemplate import YY<PERSON><PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    xjg_sql = YYSQLTemplate.xjg_stats(
        [],
        "city_id,area_id,vehicle_type",
        '''
        toInt32(city_id) city_id,
        toInt32(area_id) area_id,
        toUInt8(vehicle_type) vehicle_type, 
        count(distinct vehicle_no,vehicle_color) xjg_count 
        ''',
        start_day, end_day)
    wide_sql = YYSQLTemplate.wide_stats(
        [],
        "city_id,area_id,vehicle_no,vehicle_color,vehicle_type",
        '''
        city_id,
        area_id,
        vehicle_no,
        vehicle_color,
        vehicle_type,
        any(area_manger_short_name) area_manger_short_name,
        max(online) is_online,
        sum(risk_oneLevel) + sum(risk_twoLevel) + sum(risk_threeLevel) riskNum
        ''',
        start_day=start_day, end_day=end_day)
    jg_sql = SQLHelper().table(wide_sql) \
        .groupBy('city_id,area_id,vehicle_type') \
        .select('''
                    city_id,
                    area_id,
                    vehicle_type,
                    any(area_manger_short_name) area_manger_short_name,
                    count(1) jg_count,
                    sum(is_online) online_count,
                    sum(riskNum) riskNum
            ''')
    join_sql = SQLHelper().table(jg_sql, 'A').leftJoin() \
        .table(xjg_sql, 'B').on(
        '''
        A.city_id=B.city_id 
        and A.area_id=B.area_id 
        and A.vehicle_type = B.vehicle_type
        '''
    ).select(
        f'''
            A.city_id city_id,
            A.area_id area_id,
            area_manger_short_name,
            A.vehicle_type vehicle_type,
            if(xjg_count<jg_count,jg_count,xjg_count) xjg_count,
            jg_count,
            online_count,
            FLOOR(riskNum / online_count / {days}, 4 ) risk_rate
        '''
    )
    final_sql = SQLHelper().table(join_sql).groupBy(
        '''
        city_id,area_id
        '''
    ).select(
        '''
        city_id,area_id,
        splitByChar(',', any(area_manger_short_name))[2] AS city_name,
        splitByChar(',', any(area_manger_short_name))[3] AS area_name,
        maxIf(xjg_count,vehicle_type =1) ke_xjg_count, 
        maxIf(jg_count,vehicle_type =1) ke_jg_count, 
        maxIf(online_count,vehicle_type =1) ke_online_count, 
        maxIf(risk_rate,vehicle_type =1) ke_risk_rate,
        maxIf(xjg_count,vehicle_type =3) w_xjg_count, 
        maxIf(jg_count,vehicle_type =3) w_jg_count, 
        maxIf(online_count,vehicle_type =3) w_online_count, 
        maxIf(risk_rate,vehicle_type =3) w_risk_rate,
        maxIf(xjg_count,vehicle_type =4) z_xjg_count, 
        maxIf(jg_count,vehicle_type =4) z_jg_count, 
        maxIf(online_count,vehicle_type =4) z_online_count,
        maxIf(risk_rate,vehicle_type =4) z_risk_rate
        '''
    )
    print(final_sql)
    return final_sql


def export_outlist_orderDict(inlist):
    outlist = []
    h = [
        'city_name', 'area_name',
        'ke_xjg_count','ke_jg_count','ke_online_count','ke_risk_rate',
        'w_xjg_count','w_jg_count','w_online_count','w_risk_rate',
        'z_xjg_count','z_jg_count','z_online_count','z_risk_rate',
    ]
    for e in inlist:
        v = [
            e['city_name'], e['area_name'],
            e['ke_xjg_count'], e['ke_jg_count'], e['ke_online_count'], e['ke_risk_rate'],
            e['w_xjg_count'], e['w_jg_count'], e['w_online_count'], e['w_risk_rate'],
            e['z_xjg_count'], e['z_jg_count'], e['z_online_count'], e['z_risk_rate'],
        ]
        ne = OrderedDict(zip(h, v))
        outlist.append(ne)
    return outlist


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    datalist = orderByCity(datalist)
    outlist = export_outlist_orderDict(datalist)
    return outlist


if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
