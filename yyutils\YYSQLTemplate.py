from SQLHelper import SQLHelper


class YYSQLTemplate:
    @staticmethod
    def riskStatisticsDayAll_vehicle_period_stats(where_conditions: list, select_columns, **kwargs):
        '''
        风险日结表的车辆风险细类统计
        :param where_conditions:
        :param select_columns:
        :param kwargs: 包含 start_day end_day
        :return:
        '''
        if 'start_day' not in kwargs or 'end_day' not in kwargs:
            raise RuntimeError("入参起始日期必填, eg. start_day='2022-05-01' , end_day='2022-05-31' ")
        _example_sql = '''
            select
                vehicle_no,
                vehicle_color,
                sum(alarmNum) riskNum,
                sum(type0110401) l1_call,
                sum(type0110413) l1_play,
                sum(type0110402) l1_smoking,
                sum(type0210401) l2_call,
                sum(type0210413) l2_play,
                sum(type0210402) l2_smoking,
                sum(type0310401) l3_call,
                sum(type0310413) l3_play,
                sum(type0310402) l3_smoking
            from gdispx_data.risk_statistics_day_all 
            where alarm_time between '{start_day}' and '{end_day}'
                and city_id != '440300'
                and vehicle_type = 4
            group by  vehicle_no,vehicle_color
        '''
        sql = (SQLHelper().table('gdispx_data.risk_statistics_day_all')
               .where(["alarm_time between '{start_day}' and '{end_day}'"] + where_conditions)
               .groupBy("vehicle_no,vehicle_color")
               .select(select_columns)
               )
        sql = sql.format(**kwargs)
        return sql

    @staticmethod
    def AlarmDayAll_vehicle_period_stats(where_conditions: list, select_columns, **kwargs):
        '''
        风险日结表的车辆风险细类统计
        :param where_conditions:
        :param select_columns:
        :param kwargs: 包含 start_day end_day
        :return:
        '''
        if 'start_day' not in kwargs or 'end_day' not in kwargs:
            raise RuntimeError("入参起始日期必填, eg. start_day='2022-05-01' , end_day='2022-05-31' ")
        _example_sql = '''
                select
                    vehicle_no,
                    vehicle_color,
                    sum(alarmNum) alarmNum,
                    sum(type10400) tiredDriving,
                    sum(type10408) timeLongDriving,
                    sum(type10300) crash,
                    sum(type10403) seeAnother,
                    sum(type10302) tooclose,
                    sum(type10301) goNotLine,
                    sum(type10312) crossLine,
                    sum(type10410) notWearBelt,
                    sum(type10412) handsFly
                from statistics_original_alarm_day_all
                where alarm_time between '{start_day}' and '{end_day}'
                    and city_id != '440300'
                    and vehicle_type = 4
                group by  vehicle_no,vehicle_color
            '''
        sql = (SQLHelper().table('gdispx_data.statistics_original_alarm_day_all')
               .where(["alarm_time between '{start_day}' and '{end_day}'"] + where_conditions)
               .groupBy("vehicle_no,vehicle_color")
               .select(select_columns)
               )
        sql = sql.format(**kwargs)
        return sql

    @staticmethod
    def vehicleWideDayAll_vehicle_period_stats(where_conditions: list, group_keys, select_columns, **kwargs):
        '''
        车辆宽表的周期内分组【自定义】统计
        :param where_conditions:
        :param group_keys:
        :param select_columns:
        :param kwargs: 包含 start_day end_day
        :return:
        '''
        if 'start_day' not in kwargs or 'end_day' not in kwargs:
            raise RuntimeError("入参起始日期必填, eg. start_day='2022-05-01' , end_day='2022-05-31' ")
        _sql = f'''
            select
                vehicle_no ,
                vehicle_color ,
                any(owner_name) owner_name,
                any(city_name) city_name,
                any(area_name) area_name,
                any(third_party_name)  third_party_name,
                sum(if(exception_type = 2 , 1,0)) ex_days,
                if( max(online) == 1, '当月上线','当月未上线' ) online,
                if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, '不合格','合格'  ) ok
            from gdispx_data.v_vehicle_wide_day_all 
            where inc_day between '2022-04-01' and '2022-04-30'
                and city_id != '440300'
                and vehicle_type = 4
            group by vehicle_no,vehicle_color
        '''
        sql = (SQLHelper().table('gdispx_data.v_vehicle_wide_day_all')
               .where(["inc_day between '{start_day}' and '{end_day}'"] + where_conditions)
               .groupBy(group_keys)
               .select(select_columns)
               )
        sql = sql.format(**kwargs)
        return sql

    @staticmethod
    def vehicleRelatedInfosWide(select_columns=None):
        '''
        车辆明细表，关联车架号、发动机号、交强险、商业险信息使用
        :param select_columns:
        :return:
        '''
        _sql = f'''
        select
            vehicle_no,
            vehicle_color,
            vin,
            engine_number,
            compulsory_insurance_name,
            commercial_insurance_name
        from gdispx_data.vehicle_related_infos_wide
    '''
        if not select_columns:
            select_columns = f'''
            vehicle_no,
            vehicle_color,
            vin,
            engine_number,
            compulsory_insurance_name,
            commercial_insurance_name
        '''
        sql = (SQLHelper().table('gdispx_data.vehicle_related_infos_wide')
               .select(select_columns)
               )
        return sql

    @staticmethod
    def calTrackAvgDriftPoints(start_day, end_day):
        sql = f'''
        select 
        toInt32(sum(drift_point_count_sum) / count())  average_drift_num,
        count() online_vehicle_count
        from (select
        vehicle_no,vehicle_color,sum(drift_point_count) drift_point_count_sum,
        max(online) online
        from gdispx_data.v_vehicle_wide_day_all 
        where inc_day between '{start_day}' and '{end_day}'
        group by vehicle_no,vehicle_color
        having online = 1
        )
    '''
        return sql

    @staticmethod
    def risk_dispose_stats(start_day, end_day):
        sql = f'''
        select
            vehicle_no ,
            toUInt8(vehicle_color) vehicle_color,
            count(distinct alarm_id) riskNum,
            sum(manual_dispose) manual_riskNum,
            sum(dispose_mistake) manual_mistake_riskNum,
            sum(check_reject) check_mistake_ng_riskNum,
            sum(check_real) check_mistake_ok_riskNum,
            manual_riskNum - check_mistake_ok_riskNum is_real_riskNum
        from 
            (SELECT 
                A.id alarm_id,
                A.vehicle_no vehicle_no,
                A.vehicle_color vehicle_color,
                A.city_name city_name,
                B.manual_dispose manual_dispose,
                B.dispose_mistake dispose_mistake,
                B.check_reject check_reject,
                B.check_real check_real
             from 
                    (SELECT 
                    vehicle_no ,
                    vehicle_color,
                    id,
                    splitByChar(',', area_name)[2] AS city_name
                    from gdispx_data.business_ai_alarm_info 
                    where toDate(alarm_time) BETWEEN '{start_day}'  AND '{end_day}') A
                LEFT JOIN 
                    (SELECT 
                    alarm_id,
                    max(if(dispose_type=0 and process_unit !='监管系统', 1, 0)) `manual_dispose`,
                    max(if(process_status=3 and dispose_type=0, 1, 0)) `dispose_mistake`,
                    max(if(process_status=1 and dispose_type=7, 1, 0)) `check_reject`,
                    max(if(process_status=2 and dispose_type=7, 1, 0)) `check_real`
                    from gdispx_data.business_alarm_audit_info final
                    where toDate(create_time) BETWEEN  '{start_day}' AND '{end_day}'
                    and alarm_id !=''
                    group by alarm_id) B on A.id = B.alarm_id
            ) C
        group by vehicle_no,vehicle_color
    '''
        return sql

    @staticmethod
    def xjg_stats(group_keys,select_columns,start_day,end_day):
        '''
        获取需监管车辆统计信息，根据需要聚合
        :param start_day:
        :param end_day:
        :return:
        '''
        _sql = f'''
        SELECT 
            area_id,
            vehicle_type,
            owner_id,
            count(DISTINCT vehicle_no) xjg_count
            from gdispx_data.vehicle_history_yunzheng vhy 
        where create_date BETWEEN '2022-05-01'  and '2022-05-31'
        group by area_id,vehicle_type,owner_id
        '''
        sql = (SQLHelper().table('gdispx_data.vehicle_history_yunzheng')
                .groupBy(group_keys)
                .where([f"create_date BETWEEN '{start_day}'  and '{end_day}'"])
               .select(select_columns)
               )
        return sql

def zh_stats():
    risk_select_columns = f'''
                    vehicle_no,
                    vehicle_color,
                    oneLevelNum+twoLevelNum+threeLevelNum riskNum,
                    sum(oneLevel) oneLevelNum,
                    sum(type0110401) l1_call,
                    sum(type0110413) l1_play,
                    sum(type0110402) l1_smoking,
                    sum(twoLevel) twoLevelNum,
                    sum(type0210401) l2_call,
                    sum(type0210413) l2_play,
                    sum(type0210402) l2_smoking,
                    sum(threeLevel) threeLevelNum,
                    sum(type0310401) l3_call,
                    sum(type0310413) l3_play,
                    sum(type0310402) l3_smoking
                    
        '''
    risk_sql = YYSQLTemplate. \
        riskStatisticsDayAll_vehicle_period_stats(["city_id != '440300'", "vehicle_type = 4"]
                                                  , risk_select_columns,
                                                  start_day='2022-05-01',
                                                  end_day='2022-05-31'
                                                  )
    alarm_select_columns = f'''
                        vehicle_no,
                        vehicle_color,
                        sum(type10400) tiredDriving,
                        sum(type10408) timeLongDriving,
                        sum(type10300) crash,
                        sum(type10403) seeAnother,
                        sum(type10302) tooclose,
                        sum(type10301) goNotLine,
                        sum(type10312) crossLine,
                        sum(type10410) notWearBelt,
                        sum(type10412) handsFly
            '''
    alarm_sql = YYSQLTemplate. \
        AlarmDayAll_vehicle_period_stats(["city_id != '440300'", "vehicle_type = 4"]
                                                  , alarm_select_columns,
                                                  start_day='2022-05-01',
                                                  end_day='2022-05-31'
                                                  )
    wide_select_columns = f'''
            vehicle_no ,
            vehicle_color ,
            any(owner_name) owner_name,
            any(city_name) city_name,
            any(area_name) area_name,
            any(third_party_name)  third_party_name,
            sum(if(exception_type = 2 , 1,0)) ex_days,
            if( max(online) = 1, '当月上线','当月未上线' ) online,
            if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, '不合格','合格'  ) ok
        '''
    wide_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats(["city_id != '440300'", "vehicle_type = 4"]
                                                                    , 'vehicle_no,vehicle_color', wide_select_columns,
                                                                    start_day='2022-05-01',
                                                                    end_day='2022-05-31'
                                                                    )
    vehicle_sql = YYSQLTemplate.vehicleRelatedInfosWide()

    final_select_columns = f'''
            A.vehicle_no `车牌号`,
            A.vehicle_color `车牌颜色`,
            owner_name `业户名称`,
            third_party_name `第三方监控机构名称`,
            vin `车架号`,
            engine_number `发动机号`,
            city_name `地市`,
            area_name `区县`,
            compulsory_insurance_name `交强险`,
            commercial_insurance_name `商业险`,
            if(riskNum>=0,riskNum,0) `风险总数`,
            if(oneLevelNum>=0,oneLevelNum,0) `一级风险总数`,
            if(l1_call>=0,l1_call,0) `一级风险拨打电话`,
            if(l1_play>=0,l1_play,0) `一级风险玩手机`,
            if(l1_smoking>=0,l1_smoking,0) `一级风险抽烟`,
            if(twoLevelNum>=0,twoLevelNum,0) `二级风险总数`,
            if(l2_call>=0,l2_call,0) `二级风险拨打电话`,
            if(l2_play>=0,l2_play,0) `二级风险玩手机`,
            if(l2_smoking>=0,l2_smoking,0) `二级风险抽烟`,
            if(threeLevelNum>=0,threeLevelNum,0) `三级风险总数`,
            if(l3_call>0,l3_call,0) `三级风险拨打电话`,
            if(l3_play>0,l3_play,0) `三级风险玩手机`,
            if(l3_smoking>0,l3_smoking,0) `三级风险抽烟`,
            tiredDriving `疲劳驾驶`,
            timeLongDriving `单次连续驾驶超时`,
            crash `前向碰撞`,
            seeAnother `长时间不目视前方`,
            tooclose `前向车距过近`,
            goNotLine `车道偏离`,
            crossLine `实线变道`,
            notWearBelt `未系安全带`,
            handsFly `双手脱离方向盘`,
            ex_days `疑似屏蔽信号运行天数`,
            online `当月是否上线`,
            ok `数据是否合格`
        '''
    final_sql = (
        SQLHelper().table(wide_sql, 'A')
            .leftJoin().table(risk_sql, 'B')
            .on('A.vehicle_no = B.vehicle_no and A.vehicle_color = B.vehicle_color')
            .leftJoin().table(alarm_sql, 'D')
            .on('A.vehicle_no = D.vehicle_no and A.vehicle_color = D.vehicle_color')
            .leftJoin().table(vehicle_sql, 'C')
            .on('A.vehicle_no = C.vehicle_no and A.vehicle_color = toUInt8(C.vehicle_color)')
            .select(final_select_columns)
    )
    print(final_sql)


def producterStats():
    AvgDriftPoints = YYSQLTemplate.calTrackAvgDriftPoints('2022-05-01', '2022-05-31')

    wide_select_columns = '''
                vehicle_no ,
                vehicle_color ,
                any(master_producter_id) master_producter_id,
                any(master_producter_name) master_producter_name,
                max(online) online,
                if( sum(drift_point_count) > {average_drift_num},1,0  ) is_drift,
                sum(total_mileage) total_mileage,
                sum(continuous_mileage) continuous_mileage,
                if( min(never_gps)=1 or min(never_alarm)=1 or min(never_acc) = 1, 0,1  ) is_ok,
                min(never_gps) never_gps1,
                min(never_alarm) never_alarm1,
                min(never_acc) never_acc1,
                sum(accessoriesNum) accessoriesNum,
                sum(accessoriesonTimeNum) accessoriesonTimeNum
            '''
    wide_sql = YYSQLTemplate.vehicleWideDayAll_vehicle_period_stats([], 'vehicle_no,vehicle_color',
                                                                    wide_select_columns,
                                                                    start_day='2022-05-01',
                                                                    end_day='2022-05-31',
                                                                    average_drift_num=5
                                                                    )
    risk_disposed_sql = YYSQLTemplate.risk_dispose_stats('2022-05-01', '2022-05-31')
    wide_risk_select_columns = f'''
        t1.vehicle_no vehicle_no,
        t1.vehicle_color vehicle_color,
        master_producter_id,
        master_producter_name,
        online,
        is_drift,
        total_mileage,
        continuous_mileage,
        is_ok,
        never_gps1 never_gps,
        never_alarm1 never_alarm,
        never_acc1 never_acc,
        accessoriesNum,
        accessoriesonTimeNum,
        manual_riskNum,
        is_real_riskNum
    '''
    wide_risk_sql = (
        SQLHelper().table(wide_sql,'t1').leftJoin().table(risk_disposed_sql,'t2')
        .on(" t1.vehicle_no = t2.vehicle_no and t1.vehicle_color = t2.vehicle_color ")
        .select(wide_risk_select_columns)
    )
    producter_select_columns = f'''
        master_producter_id,
        any(master_producter_name) `厂商`,
        count(1) `入网车辆`,
        sum(online) `上线车辆`,
        FLOOR(sum(online) / count(1),4 ) `上线率`,
        sum(is_drift) `高频漂移车辆`,
        FLOOR(sum(is_drift) / sum(online),4 ) `高频漂移车辆占比`,
        sum(total_mileage) `轨迹总里程`,
        sum(continuous_mileage) `轨迹完整里程`,
        FLOOR(sum(continuous_mileage) / sum(total_mileage),4 ) `轨迹完整率`,
        sum(is_ok) `合格车辆数`,
        sum(never_gps) `定位异常车辆`,
        sum(never_alarm) `长期未报警车辆`,
        sum(never_acc) `附件异常车辆`,
        FLOOR(sum(is_ok) / count(1),4 ) `数据合格率`,
        sum(accessoriesNum) `需上传附件报警数`,
        sum(accessoriesNum) - sum(accessoriesonTimeNum) `附件丢失报警数`,
        FLOOR((sum(accessoriesNum) - sum(accessoriesonTimeNum)) / sum(accessoriesNum),4 ) `附件丢失率`,
        sum(manual_riskNum) `人工处置报警量`,
        sum(is_real_riskNum) `人工处置属实的报警量`,
        FLOOR(sum(is_real_riskNum) / sum(manual_riskNum),4 ) `报警属实率`
    '''
    producter_group_sql = (
        SQLHelper().table(wide_risk_sql).groupBy('master_producter_id')
        .select(producter_select_columns)
    )
    print(producter_group_sql)


if __name__ == '__main__':
    zh_stats()
