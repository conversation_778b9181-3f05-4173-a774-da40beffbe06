<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广东省重点监管车辆离线位移情况 - 可视化分析报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f4f8;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 320px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 384px;
            }
        }
        .stat-card {
            background: linear-gradient(135deg, #374c80, #003f5c);
            color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
        .stat-number {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1;
        }
        .stat-label {
            font-size: 1.1rem;
            margin-top: 0.5rem;
            opacity: 0.9;
        }
        .section-title-bar {
            border-left: 5px solid #ff764a;
            padding-left: 1rem;
        }
        .recommendation-card h4::before {
            content: '➔';
            margin-right: 0.75rem;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

<div class="container mx-auto p-4 md:p-8">

    <header class="text-center mb-12">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">广东省重点监管车辆离线位移情况</h1>
        <h2 class="text-xl md:text-2xl font-semibold text-[#003f5c]">可视化分析报告 (数据已终审)</h2>
        <p class="mt-4 text-gray-600 max-w-3xl mx-auto">本报告旨在通过数据可视化手段，深度剖析重点监管车辆在终端离线期间的位移事件，揭示潜在风险，为提升行业监管效能提供决策支持。本次共计分析了 <strong class="text-[#bc5090] font-bold">2115</strong> 条有效离线位移事件记录。</p>
    </header>

    <main class="space-y-12">

        <section id="overview">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold">总体态势分析</h3>
                <p class="text-gray-600 mt-1">从宏观层面洞察离线位移事件的分布特征，把握整体风险轮廓。多数事件表现为短时、短距，但隐藏其中的极端案例是本次分析的焦点。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold text-center mb-4">离线时长分布</h4>
                    <p class="text-sm text-gray-500 text-center mb-4">分析显示，约13%的事件离线时长在6小时以内，但仍有超过87%的事件离线时间较长，构成了主要的监管风险。</p>
                    <div class="chart-container h-80 md:h-96">
                        <canvas id="durationChart"></canvas>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold text-center mb-4">离线位移距离分布</h4>
                    <p class="text-sm text-gray-500 text-center mb-4">大多数事件的位移距离较短，超过95%的事件位移在50公里内。但数据中存在的超长距离位移事件是本次分析的重中之重，它们揭示了最严重的监管漏洞。</p>
                    <div class="chart-container h-80 md:h-96">
                        <canvas id="distanceChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <section id="long-distance">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#bc5090]">核心关注点 (一): 超长距离离线位移</h3>
                <p class="text-gray-600 mt-1">此类“短时-长距”事件强烈暗示车辆可能在完全脱离监管的状态下进行跨区域长途运输，风险等级最高，极可能涉及蓄意规避行为。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="md:col-span-1 flex items-center justify-center">
                    <div class="stat-card w-full">
                        <div class="stat-number">1051.16<span class="text-2xl">公里</span></div>
                        <div class="stat-label">单次离线最大位移记录</div>
                    </div>
                </div>
                <div class="md:col-span-2 bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold mb-4">部分超长距离位移事件清单</h4>
                    <p class="text-sm text-gray-500 mb-4">以下车辆在数小时内离线位移数百乃至上千公里，其行为严重偏离正常运营，需立即启动专项核查程序。</p>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="bg-gray-100 text-gray-600 uppercase">
                            <tr>
                                <th class="p-3">车牌号</th>
                                <th class="p-3">离线时长</th>
                                <th class="p-3 text-right">位移距离 (公里)</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤J53123</td>
                                <td class="p-3">1小时51分4秒</td>
                                <td class="p-3 text-right font-bold text-red-600">1051.16</td>
                            </tr>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤J63410</td>
                                <td class="p-3">9小时46分6秒</td>
                                <td class="p-3 text-right font-bold text-red-600">1039.38</td>
                            </tr>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤J63580</td>
                                <td class="p-3">19小时19分22秒</td>
                                <td class="p-3 text-right font-bold text-red-600">1039.36</td>
                            </tr>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤J68851</td>
                                <td class="p-3">0小时51分9秒</td>
                                <td class="p-3 text-right font-bold text-red-600">1034.59</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤A38988D</td>
                                <td class="p-3">7小时54分21秒</td>
                                <td class="p-3 text-right font-bold text-red-600">995.32</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <section id="long-duration">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#7a5195]">核心关注点 (二): 长时间离线伴随位移</h3>
                <p class="text-gray-600 mt-1">车辆持续数天脱离监控网络，同时仍有位移记录，反映出设备健康状况或合规管理存在严重问题，是对监管体系的重大挑战。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="md:col-span-1 flex items-center justify-center">
                    <div class="stat-card w-full">
                        <div class="stat-number">13.96<span class="text-2xl">天</span></div>
                        <div class="stat-label">单次离线最长持续时间</div>
                    </div>
                </div>
                <div class="md:col-span-2 bg-white p-6 rounded-lg shadow-md">
                    <h4 class="text-lg font-semibold mb-4">部分长时间离线伴随位移事件 (数据已更正)</h4>
                    <p class="text-sm text-gray-500 mb-4">这些车辆长时间“失联”，需对车辆终端设备进行强制健康检查，并评估是否存在人为规避或对离线告警漠视的行为。</p>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="bg-gray-100 text-gray-600 uppercase">
                            <tr>
                                <th class="p-3">车牌号</th>
                                <th class="p-3">离线时长</th>
                                <th class="p-3 text-right">位移距离 (公里)</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤D40655</td>
                                <td class="p-3 font-bold text-purple-600">13天23小时5分33秒</td>
                                <td class="p-3 text-right">8.73</td>
                            </tr>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤K21436</td>
                                <td class="p-3 font-bold text-purple-600">10天15小时8分4秒</td>
                                <td class="p-3 text-right">4.11</td>
                            </tr>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤N06540</td>
                                <td class="p-3 font-bold text-purple-600">9天20小时27分34秒</td>
                                <td class="p-3 text-right">10.78</td>
                            </tr>
                            <tr class="border-b hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤SB0880</td>
                                <td class="p-3 font-bold text-purple-600">6天21小时31分15秒</td>
                                <td class="p-3 text-right">378.00</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="p-3 font-medium text-gray-900">粤D33003</td>
                                <td class="p-3 font-bold text-purple-600">5天23小时19分27秒</td>
                                <td class="p-3 text-right">58.75</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <section id="high-frequency">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold">核心关注点 (三): 高频离线位移车辆识别</h3>
                <p class="text-gray-600 mt-1">识别“惯犯”车辆，有助于发现持续性的设备问题或管理漏洞。这些“病灶车”应被列为最高优先级的监管与整改对象。</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h4 class="text-lg font-semibold text-center mb-4">离线位移事件高频车辆 TOP 5</h4>
                <p class="text-sm text-gray-500 text-center mb-4">以下车辆在分析周期内频繁发生离线位移事件。这强烈指向了设备持续故障或管理长期缺位的问题。</p>
                <div class="chart-container h-80 md:h-96">
                    <canvas id="frequencyChart"></canvas>
                </div>
            </div>
        </section>

        <section id="owner-analysis">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#ef5675]">核心关注点 (四): 高发离线位移业户</h3>
                <p class="text-gray-600 mt-1">将风险下钻至企业主体，识别管理中的薄弱环节。事件高发的业户需接受重点监管与专项检查。</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h4 class="text-lg font-semibold text-center mb-4">离线位移事件高发业户 TOP 5 (数据已更正)</h4>
                <p class="text-sm text-gray-500 text-center mb-4">这些企业名下的车辆离线位移事件总数最多，反映出其在车辆动态监控、设备维护或驾驶员管理上可能存在系统性问题。</p>
                <div class="chart-container h-80 md:h-96">
                    <canvas id="ownerChart"></canvas>
                </div>
            </div>
        </section>

        <section id="platform-analysis">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold text-[#ff764a]">核心关注点 (五): 事件集中的第三方平台</h3>
                <p class="text-gray-600 mt-1">从技术服务商维度进行分析，评估不同平台的设备稳定性与服务质量，为技术选型和供应商管理提供参考。</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h4 class="text-lg font-semibold text-center mb-4">离线位移事件集中的第三方平台 TOP 5 (数据已更正)</h4>
                <p class="text-sm text-gray-500 text-center mb-4">关联事件数较多的平台，可能因其市场份额大，或其终端设备、网络服务的稳定性有待提高。建议对其进行服务质量评估。</p>
                <div class="chart-container h-80 md:h-96">
                    <canvas id="platformChart"></canvas>
                </div>
            </div>
        </section>

        <section id="recommendations">
            <div class="mb-8 section-title-bar">
                <h3 class="text-2xl font-bold">结论与系统性管理建议</h3>
                <p class="text-gray-600 mt-1">基于数据洞察，建议构建包含技术升级、管理规程、监管执法和长效机制的“四位一体”综合治理体系，从根本上解决问题。</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#ff764a] recommendation-card">
                    <h4 class="text-xl font-bold text-[#ff764a] mb-3">技术体系优化</h4>
                    <p class="text-gray-600">提升终端准入门槛，推广双模通讯，并升级平台智能分析告警能力，建立“终端健康度”评分体系。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#ffa600] recommendation-card">
                    <h4 class="text-xl font-bold text-[#ffa600] mb-3">企业责任压实</h4>
                    <p class="text-gray-600">要求企业建立车辆技术档案，将在线率与绩效挂钩，并强化监控人员培训，确保告警有效处置。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#7a5195] recommendation-card">
                    <h4 class="text-xl font-bold text-[#7a5195] mb-3">创新执法协同</h4>
                    <p class="text-gray-600">实施基于“企业画像”的差异化监管，通过数据驱动现场稽查，并与公安、高速管理部门联动，核实轨迹。</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md border-t-4 border-[#003f5c] recommendation-card">
                    <h4 class="text-xl font-bold text-[#003f5c] mb-3">构建长效机制</h4>
                    <p class="text-gray-600">建立常态化分析通报与“红黑榜”制度，完善法规，细化对违规行为的处罚标准，并可引入第三方评估。</p>
                </div>
            </div>
        </section>

    </main>

    <footer class="text-center mt-16 pt-8 border-t border-gray-200">
        <p class="text-sm text-gray-500">广东省道路运输事务中心 | 数据可视化分析报告</p>
        <p class="text-xs text-gray-400 mt-2">数据来源: `5月客运车辆离线漂移明细(已过滤).csv` | 分析周期: 2025年5月 | 生成时间: 2025年6月10日</p>
    </footer>

</div>

<script>
    const brilliantBluesPalette = {
        primary: '#003f5c',
        secondary: '#374c80',
        tertiary: '#7a5195',
        accent1: '#bc5090',
        accent2: '#ef5675',
        accent3: '#ff764a',
        accent4: '#ffa600',
    };

    const tooltipTitleCallback = (tooltipItems) => {
        const item = tooltipItems[0];
        if (!item || !item.chart.data.labels) return '';
        let label = item.chart.data.labels[item.dataIndex];
        if (Array.isArray(label)) {
            return label.join(' ');
        }
        return label;
    };

    const processLabel = (label) => {
        const maxLength = 16;
        if (typeof label === 'string' && label.length > maxLength) {
            const words = label.split('');
            let lines = [];
            let currentLine = '';
            words.forEach(word => {
                if ((currentLine + word).length > maxLength) {
                    lines.push(currentLine);
                    currentLine = '';
                }
                currentLine += word;
            });
            lines.push(currentLine);
            return lines;
        }
        return label;
    };

    const commonChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    font: {
                        family: "'Noto Sans SC', sans-serif",
                    }
                }
            },
            tooltip: {
                callbacks: {
                    title: tooltipTitleCallback
                },
                titleFont: {
                    family: "'Noto Sans SC', sans-serif",
                },
                bodyFont: {
                    family: "'Noto Sans SC', sans-serif",
                }
            }
        },
        animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
        }
    };

    const durationData = {
        labels: ["<1小时", "1-6小时", "6-12小时", "12-24小时", "1-3天", "3-7天", ">7天"],
        datasets: [{
            label: '事件数量',
            data: [43, 146, 252, 452, 693, 401, 128],
            backgroundColor: [
                brilliantBluesPalette.primary,
                brilliantBluesPalette.secondary,
                brilliantBluesPalette.tertiary,
                brilliantBluesPalette.accent1,
                brilliantBluesPalette.accent2,
                brilliantBluesPalette.accent3,
                brilliantBluesPalette.accent4,
            ],
            borderColor: '#f0f4f8',
            borderWidth: 3,
            hoverOffset: 10
        }]
    };
    new Chart(document.getElementById('durationChart'), {
        type: 'doughnut',
        data: durationData,
        options: { ...commonChartOptions }
    });

    const distanceDataRaw = {
        labels: ['0-10公里', '10-50公里', '50-200公里', '超过200公里(高风险)'],
        datasets: [{
            label: '事件数量',
            data: [1784, 219, 78, 34],
            backgroundColor: [
                brilliantBluesPalette.primary,
                brilliantBluesPalette.secondary,
                brilliantBluesPalette.tertiary,
                brilliantBluesPalette.accent2
            ],
            borderRadius: 5,
            barPercentage: 0.6
        }]
    };
    distanceDataRaw.labels = distanceDataRaw.labels.map(processLabel);
    new Chart(document.getElementById('distanceChart'), {
        type: 'bar',
        data: distanceDataRaw,
        options: {
            ...commonChartOptions,
            scales: {
                y: {
                    beginAtZero: true,
                    title: { display: true, text: '事件数量 (条)', font: { size: 14 } }
                }
            },
            plugins: {
                ...commonChartOptions.plugins,
                legend: { display: false }
            }
        }
    });

    const frequencyData = {
        labels: ["粤K05833", "粤NYW793", "粤GM9393", "粤NSW357", "粤V26777"],
        datasets: [{
            label: '离线位移事件次数',
            data: [29, 22, 21, 20, 19],
            backgroundColor: [
                brilliantBluesPalette.accent2,
                brilliantBluesPalette.accent1,
                brilliantBluesPalette.tertiary,
                brilliantBluesPalette.secondary,
                brilliantBluesPalette.primary,
            ],
            borderRadius: 5,
            barPercentage: 0.5
        }]
    };
    new Chart(document.getElementById('frequencyChart'), {
        type: 'bar',
        data: frequencyData,
        options: {
            ...commonChartOptions,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    title: { display: true, text: '事件次数', font: { size: 14 } }
                }
            },
            plugins: {
                ...commonChartOptions.plugins,
                legend: { display: false }
            }
        }
    });

    const ownerData = {
        labels: ["兴宁市客运公司", "阳江市第二汽车运输有限公司", "阳春市汽车客运总站有限公司", "广州市花都区汽车客运站", "广东粤运交通股份有限公司肇庆分公司"],
        datasets: [{
            label: '离线位移事件次数',
            data: [67, 50, 49, 48, 43],
            backgroundColor: [
                brilliantBluesPalette.accent2,
                brilliantBluesPalette.accent1,
                brilliantBluesPalette.tertiary,
                brilliantBluesPalette.secondary,
                brilliantBluesPalette.primary,
            ],
            borderRadius: 5,
            barPercentage: 0.5
        }]
    };
    new Chart(document.getElementById('ownerChart'), {
        type: 'bar',
        data: ownerData,
        options: {
            ...commonChartOptions,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    title: { display: true, text: '事件次数', font: { size: 14 } }
                }
            },
            plugins: {
                ...commonChartOptions.plugins,
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        title: (tooltipItems) => {
                            const item = tooltipItems[0];
                            let label = item.label || '';
                            // Manual wrapping for long labels in tooltip
                            const max = 20;
                            let lines = [];
                            for (let i = 0; i < label.length; i += max) {
                                lines.push(label.substring(i, i + max));
                            }
                            return lines;
                        }
                    }
                }
            }
        }
    });

    const platformData = {
        labels: ["深圳市国脉畅行科技股份有限公司江门分公司", "广州赫赫智能科技有限公司茂名分公司", "广东华盈光达科技有限公司", "广东壹安行科技有限公司", "广东新时空科技股份有限公司"],
        datasets: [{
            label: '离线位移事件次数',
            data: [282, 198, 141, 137, 110],
            backgroundColor: [
                brilliantBluesPalette.accent3,
                brilliantBluesPalette.accent4,
                brilliantBluesPalette.tertiary,
                brilliantBluesPalette.secondary,
                brilliantBluesPalette.primary,
            ],
            borderRadius: 5,
            barPercentage: 0.5
        }]
    };
    new Chart(document.getElementById('platformChart'), {
        type: 'bar',
        data: platformData,
        options: {
            ...commonChartOptions,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true,
                    title: { display: true, text: '事件次数', font: { size: 14 } }
                }
            },
            plugins: {
                ...commonChartOptions.plugins,
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        title: (tooltipItems) => {
                            const item = tooltipItems[0];
                            let label = item.label || '';
                            // Manual wrapping for long labels in tooltip
                            const max = 20;
                            let lines = [];
                            for (let i = 0; i < label.length; i += max) {
                                lines.push(label.substring(i, i + max));
                            }
                            return lines;
                        }
                    }
                }
            }
        }
    });
</script>
</body>
</html>
