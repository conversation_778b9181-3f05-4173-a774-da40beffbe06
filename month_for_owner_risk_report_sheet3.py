from yyutils import *
from utils.YYSQLTemplate import YYSQLTemplate
from utils.config import orderByCity, FLOORtoPercent, bpath
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=end_day)
    xjg_sql = f'''
                SELECT 
                    count(distinct vehicle_no,vehicle_color) xjg_count,
                    toUInt32(owner_id) owner_id
                from gdispx_data.vehicle_history_yunzheng vhy 
                where create_date BETWEEN '{start_day}'  and '{end_day}'
                and toUInt8(vehicle_type) = 3
                group by owner_id
    '''
    jg_vt_sql1 = f'''
                SELECT 
                    owner_id ,
                    any(owner_name) owner_name,
                    vehicle_no ,vehicle_color,
                    any(area_manger_short_name) area_manger_short_name,
                    sum(risk_oneLevel)+sum(risk_twoLevel)+sum(risk_threeLevel) riskNum,
                    sum(risk_oneLevel) riskOneLevel,
                    sum(risk_twoLevel) riskTwoLevel ,
                    sum(risk_threeLevel) riskThreeLevel,
                    if(max(exception_type)=2 ,1,0) has_exception,
                    if(max(third_party_id)>0, 1,0) has_third,
                    max(online) is_online
                from gdispx_data.v_vehicle_wide_day_all vvwda 
                where inc_day BETWEEN '{start_day}'  and '{end_day}'
                and vehicle_type = 3
                group by owner_id  ,vehicle_no ,vehicle_color 
            '''
    jg_vt_sql2 = SQLHelper().table(jg_vt_sql1, 'D') \
        .groupBy('owner_id') \
        .select(
        '''
            owner_id ,
            any(owner_name) owner_name,
            any(area_manger_short_name) area_manger_short_name,
            count(1) jg_count,
            sum(riskNum) riskNum,
            sum(riskOneLevel) riskOneLevel,
            sum(riskTwoLevel) riskTwoLevel ,
            sum(riskThreeLevel) riskThreeLevel,
            sum(has_exception) has_exception_count,
            sum(has_third) has_third_count,
            sum(is_online) online_count
        '''
    )
    # risk_sql1 = f'''
    #             select
    #                 owner_id,
    #                 sum(type10401) call,
    #                 sum(type10413) play,
    #                 sum(type10402) smoking
    #             from gdispx_data.risk_statistics_day_all
    #             where alarm_time between '{start_day}' and '{end_day}'
    #             and vehicle_type = 3
    #             group by  owner_id
    #         '''
    risk_sql2 = f'''
                SELECT
                    owner_id ,
                    sum(if(alarm_code = 10401 , 1, 0)) call,
                    sum(if(alarm_code = 10413 , 1, 0)) play,
                    sum(if(alarm_code = 10402 , 1, 0)) smoking,
                    sum(if(alarm_code = 90001 , 1, 0)) over_speed,
                    sum(if(alarm_code = 90002 , 1, 0)) over_time,
                    sum(if(alarm_code = 90007 , 1, 0)) _2_5_runnint,
                    sum(if(alarm_code = 10410, 1,0)) not_wear_safe_belt,
                    sum(if(alarm_code = 10405, 1,0)) device_covered,
                    sum(if(alarm_code = 10412, 1,0)) hands_up
                from
                    gdispx_data.business_ai_alarm_info
                where
                    toDate(alarm_time) between '{start_day}' and '{end_day}'
                    and alarm_code in (90001, 90002,90007,10401,10413,10402,10410,10405,10412)
                    and vehicle_type = 3
                    and id != ''
                    and id not in (
                        select alarm_id  from gdispx_data.business_alarm_audit_info baai2 
                        where toDate(create_time) BETWEEN '{start_day}' and '{risk_dispose_end_day}'
                        and process_status=2 and dispose_type=7
                    )
                group by owner_id
        '''

    final_sql = SQLHelper().table(jg_vt_sql2, 'A') \
        .leftJoin().table(xjg_sql, 'B') \
        .on(' A.owner_id = B.owner_id ') \
        .leftJoin().table(risk_sql2, 'D').on(' A.owner_id = D.owner_id') \
        .where([' A.online_count != 0 and A.riskNum !=0 ']) \
        .select(f'''
                    splitByChar(',',area_manger_short_name)[2] city_name,
                    splitByChar(',',area_manger_short_name)[3] area_name,
                    owner_name,
                    '危运' AS vehicle_type,
                    if(xjg_count<jg_count,jg_count,xjg_count) xjg_count,
                    jg_count,
                    has_third_count,
                    over_speed,
                    smoking,
                    over_time,
                    call,
                    _2_5_runnint,
                    play,
                    not_wear_safe_belt,
                    device_covered,
                    hands_up,
                    over_speed+smoking+over_time+call+_2_5_runnint+play+not_wear_safe_belt+device_covered+hands_up total,
                    has_exception_count,
                    riskNum,
                    riskOneLevel,
                    riskTwoLevel,
                    riskThreeLevel,
                    A.owner_id owner_id,
                    FLOOR(riskOneLevel / online_count / {days}, 4 ) l1_risk_rate,
                    FLOOR(riskTwoLevel / online_count / {days}, 4 ) l2_risk_rate,
                    FLOOR(riskThreeLevel / online_count / {days}, 4 ) l3_risk_rate,
                    FLOOR(riskNum / online_count / {days}, 4 ) risk_rate
                '''
                )
    login_sql1 = f'''
                select
                    u.user_id,
                    d.owner_id
                from gdispx.sys_user u
                left join gdispx.sys_user_role ur on u.user_id = ur.user_id
                left join gdispx.sys_role r on ur.role_id = r.role_id
                left join gdispx_basics.basics_owner d on d.owner_id = u.dept_id 
                where u.tenant_id = 1
        				and d.is_delete = 0 
        				and r.role_type = 1
            '''
    login_sql = SQLHelper().table(login_sql1, 'A').groupBy('owner_id') \
        .select(
        f'''
                    owner_id,
                    count(1) open_account
                '''
    )
    return final_sql, login_sql


def getTop10(datalist, loginlist):
    id_login = list_2_dict(loginlist, 'owner_id')
    outlist = []
    h = [
        'pid', 'city_name', 'area_name', 'owner_name', 'vehicle_type', 'xjg_count', 'jg_count',
        'has_third_count','account',  'over_speed', 'smoking', 'over_time', 'call', '_2_5_runnint',
        'play', 'hands_up','not_wear_safe_belt','device_covered',
        'total', 'has_exception_count',  'l1_risk_rate', 'l2_risk_rate', 'l3_risk_rate',
        'risk_rate', 'remark'
    ]
    n_g = list_2_dictlist(datalist,'city_name')
    for g in n_g.values():
        g.sort(key=lambda o: - o['risk_rate'])
        if len(g) > 10:
            tmplist = list(filter(lambda o: o['risk_rate'] >= g[9]['risk_rate'], g))
        else:
            tmplist = g[:10]
        pid = 0
        for e in tmplist:
            pid += 1
            e['pid'] = pid
            e['account'] = id_login.get(e['owner_id'])['open_account'] if id_login.get(e['owner_id']) else 0
        for e in tmplist:
            v = (
                e['pid'], e['city_name'], e['area_name'], e['owner_name'], e['vehicle_type'], e['xjg_count'],
                e['jg_count'], e['has_third_count'],e['account'],  e['over_speed'], e['smoking'],
                e['over_time'], e['call'], e['_2_5_runnint'], e['play'],
                e['hands_up'], e['not_wear_safe_belt'], e['device_covered'],
                e['total'], e['has_exception_count'],
                 e['l1_risk_rate'], e['l2_risk_rate'], e['l3_risk_rate'], e['risk_rate'], ''
            )
            ne = OrderedDict(zip(h, v))
            outlist.append(ne)
    return outlist


def load_data(start_day, end_day, days):
    final_sql, login_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    db = DBUtil(DBType.MYSQL.value)
    loginlist = db.query_to_dictList(login_sql)
    db.close()
    outlist = getTop10(datalist, loginlist)
    outlist = orderByCity(outlist,pid=False)
    # otfile = join_path(bpath,'wei_top10.csv')
    # h = list(outlist[0].keys())
    # write_csv(otfile,h,outlist)
    return outlist

if __name__ == '__main__':
    '''

    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
