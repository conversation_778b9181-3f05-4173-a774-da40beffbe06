from yyutils import *
from utils.YYSQLTemplate import YYS<PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT,orderByCityVT
from collections import OrderedDict


def loadSQL(start_day, end_day, days):

    jg_vt_sql1 = f'''
                    select 
                        third_party_id,
                        any(third_party_name) third_party_name,
                        city_id,
                        any(city_short_name) city_name,
                        vehicle_no,
                        vehicle_color,
                        if(max(exception_type)=2 ,1,0) has_exception
                    from gdispx_data.v_vehicle_wide_day_all vvwda 
                    where inc_day BETWEEN '{start_day}'  and '{end_day}'
                    and third_party_id is not null
                    group by third_party_id,city_id,vehicle_no,vehicle_color
                '''
    final_sql = SQLHelper().table(jg_vt_sql1,'A')\
        .groupBy('third_party_id,city_id')\
        .select(
        '''
        third_party_id,
        city_id,
        any(city_name) city_name,
        count(1) jg_count,
        sum(has_exception) expt_count,
        ROUND(expt_count/jg_count,4) expt_rate
        '''
    )
    final_sql1 = SQLHelper().table(final_sql,'B')\
        .leftJoin()\
        .table('gdispx_data.basics_third_party_info','C')\
        .on('B.third_party_id = toUInt32(C.id)')\
        .select(
        '''
        third_party_id,
        C.name third_party_name,
        city_id,
        city_name,
        jg_count,
        expt_count,
        expt_rate
        '''
    )
    return final_sql1


def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        ne = OrderedDict(zip(
            ('city_name',
             'third_party_name',
              'jg_count',
             'expt_count','expt_rate'
             ),
            (e['city_name'],
             e['third_party_name'],
             e['jg_count'],
             e['expt_count'],
             FLOORtoPercent(e['expt_rate'])
             )
        ))
        outlist.append(ne)
    return outlist





def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = orderByCity(datalist)
    outlist = export_outlist_orderDict(outlist)
    return outlist

if __name__ == '__main__':
    '''

    '''
    start, end = TimeUtils.getLastWeekRange()
    load_data(start, end, 7)
