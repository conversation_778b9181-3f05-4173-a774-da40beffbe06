import datetime
from itertools import groupby
from itertools import tee
from tqdm import tqdm
import httpmethod
import logging
import time
import gc
import pandas as pd

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s: %(message)s')


def GetQueryKeyun(startTime, endTime):
    startTime2 = startTime[:10]  # 取前10个字符
    endTime2 = endTime[:10]  # 取前10个字符

    query_ke = f"""
        SELECT vehicle_no ,master_sim_code 
        from vehicle_wide_day_all vwda final
        where inc_day BETWEEN '{startTime2}' and '{endTime2}'
        and vehicle_type = 1
--         and master_sim_code = '013306348787'
--         and vehicle_no = '粤TA6500'
        group by vehicle_no ,master_sim_code 
--         limit 1000
    """
    title_ke = ['vehicle_no', 'master_sim_code']
    return query_ke, title_ke


def GetQueryTemplate(simCode, startTime, endTime):
    dakaDate = datetime.datetime.strptime(startTime, "%Y-%m-%d %H:%M:%S")
    dakaStartTime = (dakaDate - datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
    dakaEndTime = (dakaDate + datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
    query = f"""
        select a.sim_code,a.speed,a.gps_time,b.id_card
        from (select sim_code, latitude, longitude, speed, gps_time, mileage
            from business_raw_gps_info_all
            where gps_time between '{startTime}' and '{endTime}'
            order by sim_code, gps_time
        ) a
        asof
        LEFT join (select sim_code,
                ic_time,
                if(length(id_card) == 18 or length(id_card) == 15, id_card, '无法解析')     id_card
                from
                remote('172.16.123.221', 'gdispx_data', driver_alarm_detail, 'default', '<EMAIL>')
                where create_time between '{dakaStartTime}' and '{dakaEndTime}'
                and vehicle_type = '1'
                order by sim_code, ic_time
                settings force_primary_key = 0
        ) b on a.sim_code = b.sim_code and a.gps_time >= b.ic_time
        WHERE a.sim_code in {simCode};
    """
    title = ['sim_code', 'speed', 'gps_time', 'id_card']
    return query, title


def ExecuteQuery(db, sql_query, columns_title):
    df = db.execute_sql(sql_query, columns_title)
    return df


def CalculateDrivingTime(df, all_results):
    df['id_card'] = df['id_card'].astype(str).replace('nan', '无法解析').fillna('无法解析')

    if df.empty:
        return all_results

    # 按 sim_code 分组
    grouped = df.groupby('sim_code')

    for simCode, group_df in grouped:
        # 按照 gps_time 排序
        group_df = group_df.sort_values(by='gps_time').reset_index(drop=True)

        # 如果排序后为空，跳过
        if group_df.empty:
            continue

        # 初始化本车驾驶时间和司机信息
        vehicle_driving_time = 0
        driver_vehicle = {}

        # 标记连续行驶段（speed != 0）
        group_df['block'] = (group_df['speed'] == 0).cumsum()

        # 只保留 speed != 0 的记录（即行驶中）
        moving_df = group_df[group_df['speed'] != 0]

        # 对每一段行驶段进行处理
        for block_id, segment in moving_df.groupby('block'):
            start_row = segment.iloc[0]
            end_row = segment.iloc[-1]

            driverId = start_row['id_card']
            # simCode = start_row['sim_code']

            maxTime = datetime.datetime.strptime(end_row['gps_time'], "%Y-%m-%d %H:%M:%S")
            minTime = datetime.datetime.strptime(start_row['gps_time'], "%Y-%m-%d %H:%M:%S")

            duration = (maxTime - minTime).seconds
            vehicle_driving_time += duration

            if driverId not in driver_vehicle:
                driver_vehicle[driverId] = 0
            driver_vehicle[driverId] += duration

        # 汇总结果
        total_hours = round(vehicle_driving_time / 3600, 2)
        drivers_hours = {k: round(v / 3600, 2) for k, v in driver_vehicle.items()}

        results = {
            simCode: {
                'total_driving_time': total_hours,
                'drivers': drivers_hours
            }
        }
        # print(results)
        all_results.append(results)

    return all_results


def CalculateDrivingTime2(df, all_results):
    df['id_card'] = df['id_card'].astype(str).replace(['nan', 'None', 'null', ''], '无法解析')

    if df.empty:
        return all_results

    # 标记连续行驶段
    df['block'] = (df['speed'] == 0).cumsum()
    moving_df = df[df['speed'] != 0]

    # 提取每段起止时间 & 驾驶员信息
    segments = moving_df.groupby(['sim_code', 'block']).agg(
        start_time=('gps_time', 'first'),
        end_time=('gps_time', 'last'),
        driver_id=('id_card', 'first')
    ).reset_index()

    # 计算持续时间（秒）
    segments['start_time'] = pd.to_datetime(segments['start_time'])
    segments['end_time'] = pd.to_datetime(segments['end_time'])
    segments['duration'] = (segments['end_time'] - segments['start_time']).dt.total_seconds()

    # 汇总结果
    total_driving = segments.groupby('sim_code')['duration'].sum().div(3600).round(2)
    driver_driving = segments.groupby(['sim_code', 'driver_id'])['duration'].sum().div(3600).round(2)

    results_dict = {}
    for sim_code, group in driver_driving.groupby(level=0):
        drivers = group.xs(sim_code, level=0).to_dict()
        total = total_driving.loc[sim_code]
        results_dict[sim_code] = {
            'total_driving_time': float(total),
            'drivers': {k: float(v) for k, v in drivers.items()}
        }

    all_results.append(results_dict)
    # print(results_dict)
    return all_results


def SummaryDateFrame(results):
    driver_data = []
    for vehicle_dict in results:
        for sim_code, info in vehicle_dict.items():
            for driver_id, driving_time in info['drivers'].items():
                driver_data.append({
                    'sim_code': sim_code,
                    'total_driving_time': info['total_driving_time'],
                    'driver_id': driver_id,
                    'driving_time': driving_time
                })
    return pd.DataFrame(driver_data)


def GenerateSimcodeQueries(db237, df_keyun, startTime, endTime, group_size, output_file, flush_size=10000):
    sim_code_list = df_keyun['master_sim_code'].dropna().astype(str).str.zfill(12).tolist()
    total_groups = (len(sim_code_list) + group_size - 1) // group_size
    processed_count = 0

    # 初始化 CSV 文件并写入 header
    fieldnames = ['sim_code', 'total_driving_time', 'driver_id', 'driving_time']
    pd.DataFrame(columns=fieldnames).to_csv(output_file, index=False)

    buffer_df = pd.DataFrame()  # 缓存 DataFrame

    with tqdm(total=total_groups, desc="处理 SIM 卡批次", unit="batch") as pbar:
        for i in range(0, len(sim_code_list), group_size):
            start_time_batch = time.time()

            group = sim_code_list[i:i + group_size]
            group_str = "(" + ", ".join(f"'{item}'" for item in group) + ")"
            # logging.info("查询SIM卡：%s", group_str)
            try:
                query, title = GetQueryTemplate(group_str, startTime, endTime)
                df = db237.execute_sql(query, title)

                if not df.empty:
                    batch_size = len(df['sim_code'].unique())
                    # logging.info("本次查询结果包含 %d 个 SIM 卡，共 %d 条记录", batch_size, len(df))

                    batch_results = []
                    CalculateDrivingTime2(df, batch_results)
                    batch_df = SummaryDateFrame(batch_results)

                    buffer_df = pd.concat([buffer_df, batch_df], ignore_index=True)

                    if len(buffer_df) >= flush_size:
                        buffer_df.to_csv(output_file, mode='a', header=False, index=False)
                        buffer_df = pd.DataFrame()

                else:
                    logging.info("本次查询无数据返回")

            except Exception as e:
                logging.error("处理批次时发生错误: %s", str(e), exc_info=True)
                del df
                gc.collect()

            finally:
                if 'df' in locals():
                    del df
                gc.collect()
                end_time_batch = time.time()
                duration = end_time_batch - start_time_batch
                # logging.info("完成一批次，耗时 %.2f 秒", duration)
                pbar.update(1)
                # pbar.set_postfix({"当前批次": f"{processed_count + 1}/{total_groups}"})
                processed_count += 1

        # 写入剩余缓存数据
        if not buffer_df.empty:
            buffer_df.to_csv(output_file, mode='a', header=False, index=False)

    logging.info("所有批次处理完成，共处理 %d 个批次", total_groups)


if __name__ == '__main__':
    start_time = time.time()
    #
    # df = pd.read_csv(r'C:\Users\<USER>\Desktop\driving_time\driving_time\013306348787.csv')
    # df = pd.read_csv(r'D:\2025\5月\driving_range\041365642509.csv')
    # # df = pd.read_csv(r'C:\Users\<USER>\Desktop\driving_time\driving_time\粤H23339的sim卡013601101152.csv')
    # # print(df)
    # # endTime = datetime.datetime.now()
    # # startTime = 减24小时
    #
    #
    # startTime = '2025-05-12 00:00:00'
    # endTime = '2025-05-12 23:59:59'

    # query_ke, title_ke = GetQueryKeyun(startTime, endTime)
    # df_keyun = ExecuteQuery(db253, query_ke, title_ke)
    # df_keyun['master_sim_code'] = df_keyun['master_sim_code'].astype(str).str.zfill(12)
    #
    # all_results = []
    # GenerateSimcodeQueries(db237, df_keyun, startTime, endTime, group_size=2000, output_file='./driver_2000_df.csv')
    #
    # CalculateDrivingTime(df, all_results)
    # CalculateDrivingTime2(df, all_results)
    # # print("==========================")
    # # print(all_results)
    # driver_df = SummaryDateFrame(all_results)
    # driver_df.to_csv('./driver_local_df.csv', index=False)
    #

    db253 = httpmethod.httpDBCK253()
    db237 = httpmethod.httpDBCK237()

    # 定义开始和结束日期
    start_date = datetime.datetime.strptime('2025-05-18 00:00:00', "%Y-%m-%d %H:%M:%S")
    end_date = datetime.datetime.strptime('2025-05-22 23:59:59', "%Y-%m-%d %H:%M:%S")

    # 计算总共有多少个小时（从 start_date 到 end_date）
    total_hours = int((end_date - start_date).total_seconds() // 3600) + 1

    for i in range(total_hours):
        # 当前循环的起始时间
        current_start = (start_date + datetime.timedelta(hours=i)).strftime("%Y-%m-%d %H:%M:%S")
        # 当前循环的结束时间（当前起始时间 + 24 小时 - 1 秒）
        current_end = (datetime.datetime.strptime(current_start, "%Y-%m-%d %H:%M:%S") + datetime.timedelta(
            days=1) - datetime.timedelta(seconds=1)).strftime("%Y-%m-%d %H:%M:%S")

        logging.info(f"处理时间段: {current_start} - {current_end}")

        # 文件名格式化
        file_time = datetime.datetime.strptime(current_start, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d_%H")
        output_file = f'./8/driver_df_{file_time}.csv'

        try:
            query_ke, title_ke = GetQueryKeyun(current_start, current_end)
            df_keyun = ExecuteQuery(db253, query_ke, title_ke)
            df_keyun['master_sim_code'] = df_keyun['master_sim_code'].astype(str).str.zfill(12)

            all_results = []
            GenerateSimcodeQueries(db237, df_keyun, current_start, current_end, group_size=2000,
                                   output_file=output_file)

        except Exception as e:
            logging.error(f"处理时间段 {current_start} - {current_end} 时发生错误: {str(e)}", exc_info=True)

    end_time = time.time()  # 记录程序结束时间
    elapsed_time = end_time - start_time  # 计算耗时
    print(f"程序运行耗时：{elapsed_time:.2f} 秒")
