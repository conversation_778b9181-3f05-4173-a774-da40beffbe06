2024/10/01 19:19:38-root-INFO------------ begin 增量更新 begin -----------
2024/10/01 19:19:38-root-INFO------------ begin 生产当日宽表 begin -----------
2024/10/01 19:19:38-root-INFO-2024-09-23 开始生产宽表...
2024/10/01 19:19:38-root-INFO-	通过历史监管表获取当日监管车辆..
2024/10/01 19:19:48-clickhouse_driver.connection-WARNING-Failed to connect to **************:9000
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 325, in connect
    return self._init_connection(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 289, in _init_connection
    self.socket = self._create_socket(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 261, in _create_socket
    raise err
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 252, in _create_socket
    sock.connect(sa)
socket.timeout: timed out
2024/10/01 19:19:48-root-INFO------------ begin 生产当日宽表 begin -----------
2024/10/01 19:19:48-root-INFO-2024-09-24 开始生产宽表...
2024/10/01 19:19:48-root-INFO-	通过历史监管表获取当日监管车辆..
2024/10/01 19:19:58-clickhouse_driver.connection-WARNING-Failed to connect to **************:9000
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 325, in connect
    return self._init_connection(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 289, in _init_connection
    self.socket = self._create_socket(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 261, in _create_socket
    raise err
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 252, in _create_socket
    sock.connect(sa)
socket.timeout: timed out
2024/10/01 19:19:58-root-INFO------------ begin 生产当日宽表 begin -----------
2024/10/01 19:19:58-root-INFO-2024-09-25 开始生产宽表...
2024/10/01 19:19:58-root-INFO-	通过历史监管表获取当日监管车辆..
2024/10/01 19:20:08-clickhouse_driver.connection-WARNING-Failed to connect to **************:9000
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 325, in connect
    return self._init_connection(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 289, in _init_connection
    self.socket = self._create_socket(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 261, in _create_socket
    raise err
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 252, in _create_socket
    sock.connect(sa)
socket.timeout: timed out
2024/10/01 19:20:08-root-INFO------------ begin 生产当日宽表 begin -----------
2024/10/01 19:20:08-root-INFO-2024-09-26 开始生产宽表...
2024/10/01 19:20:08-root-INFO-	通过历史监管表获取当日监管车辆..
2024/10/01 19:20:18-clickhouse_driver.connection-WARNING-Failed to connect to **************:9000
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 325, in connect
    return self._init_connection(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 289, in _init_connection
    self.socket = self._create_socket(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 261, in _create_socket
    raise err
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 252, in _create_socket
    sock.connect(sa)
socket.timeout: timed out
2024/10/01 19:20:18-root-INFO------------ begin 生产当日宽表 begin -----------
2024/10/01 19:20:18-root-INFO-2024-09-27 开始生产宽表...
2024/10/01 19:20:18-root-INFO-	通过历史监管表获取当日监管车辆..
2024/10/01 19:20:28-clickhouse_driver.connection-WARNING-Failed to connect to **************:9000
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 325, in connect
    return self._init_connection(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 289, in _init_connection
    self.socket = self._create_socket(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 261, in _create_socket
    raise err
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 252, in _create_socket
    sock.connect(sa)
socket.timeout: timed out
2024/10/01 19:20:28-root-INFO------------ begin 生产当日宽表 begin -----------
2024/10/01 19:20:28-root-INFO-2024-09-28 开始生产宽表...
2024/10/01 19:20:28-root-INFO-	通过历史监管表获取当日监管车辆..
2024/10/01 19:20:38-clickhouse_driver.connection-WARNING-Failed to connect to **************:9000
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 325, in connect
    return self._init_connection(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 289, in _init_connection
    self.socket = self._create_socket(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 261, in _create_socket
    raise err
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 252, in _create_socket
    sock.connect(sa)
socket.timeout: timed out
2024/10/01 19:20:38-root-INFO------------ begin 生产当日宽表 begin -----------
2024/10/01 19:20:38-root-INFO-2024-09-29 开始生产宽表...
2024/10/01 19:20:38-root-INFO-	通过历史监管表获取当日监管车辆..
2024/10/01 19:20:48-clickhouse_driver.connection-WARNING-Failed to connect to **************:9000
Traceback (most recent call last):
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 325, in connect
    return self._init_connection(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 289, in _init_connection
    self.socket = self._create_socket(host, port)
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 261, in _create_socket
    raise err
  File "C:\Users\<USER>\.conda\envs\paddle39\lib\site-packages\clickhouse_driver\connection.py", line 252, in _create_socket
    sock.connect(sa)
socket.timeout: timed out
2024/10/01 19:20:48-root-INFO------------ end 增量更新 end -----------
2024/10/01 19:20:48-root-INFO-执行函数 create_recent_report_wide 花费了 70.17 秒
