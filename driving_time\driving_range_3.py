import datetime

import numpy as np
import pandas as pd

import httpmethod
from utils import ClickHouseHTTPClient as cl
# 1、获取所有的客运车辆
# 2、根据客运车辆循环获取GPS数据
# 3、保存成CSV文件


# 计算距离
def geo_distance_vectorized(lat1, lng1, lat2, lng2):
    R = 6371  # 地球半径(公里)
    lat1, lon1 = np.radians(lat1), np.radians(lng1)
    lat2, lon2 = np.radians(lat2), np.radians(lng2)
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2) ** 2
    c = 2 * np.arcsin(np.sqrt(a))
    return R * c

def getOfflineVehicle(df):

    # df = pd.read_csv('X:\\track_report\\driving_time\\013303187759_2025-05.csv.gz', compression='gzip')
    # 转换时间列
    df['gps_time'] = pd.to_datetime(df['gps_time'])
    df['create_time'] = pd.to_datetime(df['create_time'])
    # 按gps_time升序排序
    df = df.sort_values(by='gps_time')
    # 计算相邻记录的距离
    df['prev_source_protocol'] = df['source_protocol'].shift()
    df['prev_gps_time'] = df['gps_time'].shift()
    df['prev_create_time'] = df['create_time'].shift()
    df['prev_latitude'] = df['latitude'].shift()
    df['prev_longitude'] = df['longitude'].shift()

    df['distance'] = geo_distance_vectorized(
        df['prev_latitude'], df['prev_longitude'],
        df['latitude'], df['longitude']
    )

    df['time_diff'] = df['gps_time'].diff()
    diff2 = df['time_diff'] > datetime.timedelta(minutes=30)
    diff3 = df['distance'] >2
    df_filtered = df[diff2 & diff3]
    return df_filtered


def getVehilceList():
    df = pd.read_csv('车辆信息列表-202506051409.csv',dtype={'主设备SIM卡号': str})
    return df


def getVehicleTrack(simCode):
    db237 = ClickHouseHTTPClient.get237Client()
    query = f"""
SELECT vri.vehicle_no ,vri.vehicle_color ,
       bin(brgia.alarm_status) as alarm_status ,
       bin(brgia.gnss_status) as gnss_status,
       brgia.latitude ,brgia.longitude ,brgia.height ,brgia.speed ,brgia.direction ,brgia.gps_time ,brgia.create_time ,brgia.source_protocol
from business_raw_gps_info_all brgia
         join vehicle_related_infos vri on brgia.sim_code = vri.master_sim_code
  where sim_code = '{simCode}'
  and toYYYYMM(gps_time) = '202505'
  and brgia.latitude > 0 and brgia.longitude > 0
  and brgia.latitude < 90 and brgia.longitude < 180
  format CSVWithNames
"""
    return db237.execute_sql(query)


def main():
    df_keyun = getVehilceList()
    isFirst = True
    for index, row in df_keyun.iterrows():
        simCode = row['主设备SIM卡号']
        vehicleNo = row['车牌号']
        print(f"正在处理车辆：{vehicleNo} {simCode} ")
        df_track = getVehicleTrack(simCode)
        outputdf = getOfflineVehicle(df_track)
        outputdf = outputdf.reindex(columns=['vehicle_no','vehicle_color','alarm_status','gnss_status','prev_latitude','prev_longitude','latitude','longitude','distance','height','speed','direction','prev_gps_time','gps_time','time_diff','prev_create_time','create_time','prev_source_protocol','source_protocol'])
        outputdf = outputdf.rename(columns={'vehicle_no':'车牌号',
                                            'vehicle_color':'车辆颜色',
                                            'alarm_status':'报警状态',
                                            'gnss_status':'轨迹状态',
                                            'prev_latitude':'上一条轨迹纬度',
                                            'prev_longitude':'上条轨迹经度',
                                            'latitude':'轨迹纬度',
                                            'longitude':'轨迹经度',
                                            'distance':'距离',
                                            'height':'高度',
                                            'speed':'速度',
                                            'direction':'方向',
                                            'prev_gps_time':'上一条轨迹时间',
                                            'gps_time':'轨迹时间',
                                            'time_diff':'时间间隔',
                                            'prev_create_time':'上条创建时间',
                                            'create_time':'创建时间',
                                            'prev_source_protocol':'上条数据来源',
                                            'source_protocol':'数据来源'
                                            })
        if outputdf.size >0:
            if isFirst == True:
                isFirst = False
                outputdf.to_csv(f"offline_detail.csv", mode='w', index=False,header=True)
            else:
                outputdf.to_csv(f"offline_detail.csv", mode='a', index=False,header=False)
if __name__ == '__main__':
    main()
