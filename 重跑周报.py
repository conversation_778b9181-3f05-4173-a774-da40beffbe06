#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os

from yyutils import *
import week_main
import vt4_online_week_report
import exception_week_report


def do_week_report():
    start, end = TimeUtils.getLastWeekRange()
    week_main.main(start, end)
    vt4_online_week_report.main(start, end)
    exception_week_report.main(start, end)
    os.system("cd /data/track_report && zip report/20230522.zip *.xlsx")

if __name__ == '__main__':
    do_week_report()
    # python3 重跑周报.py
