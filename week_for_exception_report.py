# 此时的test_import_2.py文件中有：
import sys
from yyutils import *
from utils.config import bpath
import week_for_exception_report_sheet1 as s1
import week_for_exception_report_sheet2 as s2
import week_for_exception_report_sheet3 as s3

template_file = './templates/template_week_疑似屏蔽信号需求({start_day}-{end_day}).xlsx'


def fill_sheet1(elhander, s1_list, s1_se, year, month, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("各地市疑似屏蔽信号运行车辆总体情况")
    row_styles = elhander.copyRowStyle(sheet, 5)
    row_styles1 = elhander.copyRowStyle(sheet, 26)
    elhander.clearData(sheet, 5)
    sheet[2][0].value = sheet[2][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, s1_list, row_styles)
    elhander.fillData(sheet, [s1_se], row_styles1)


def fill_sheet2(elhander, s2_list, year, month, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("各地疑似屏蔽信号运行车辆占比最高的企业情况")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[2][0].value = sheet[2][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, s2_list, row_styles)


def fill_sheet3(elhander, s3_list, year, month, start_day_format, end_day_format):
    sheet = elhander.activeWorksheet("各地市第三方监控机构疑似屏蔽信号运行车辆占比情况")
    row_styles = elhander.copyRowStyle(sheet, 4)
    elhander.clearData(sheet, 4)
    sheet[2][0].value = sheet[2][0].value.format(start_day=start_day_format, end_day=end_day_format)
    elhander.fillData(sheet, s3_list, row_styles)


def writeByTemplate(s1_list, s1_se, s2_list, s3_list, year, month, start_day, end_day):
    _, fn = get_filePath_and_fileName(template_file)
    start_day_format = start_day.replace('-', '.')
    end_day_format = end_day.replace('-', '.')[5:] if start_day[:4] == end_day[:4] else end_day.replace('-', '.')
    fn = fn.format(start_day=start_day_format, end_day=end_day_format).strip('template_week_')
    otfile = join_path(bpath, fn)
    elhander = ExcelHandler(template_file, otfile)
    fill_sheet1(elhander, s1_list, s1_se, year, month, start_day_format, end_day_format)
    fill_sheet2(elhander, s2_list, year, month, start_day_format, end_day_format)
    fill_sheet3(elhander, s3_list, year, month, start_day_format, end_day_format)
    elhander.save()


def main(start_day, end_day, days=7):
    s1_list, s1_se = s1.load_data(start_day, end_day, days)
    s2_list = s2.load_data(start_day, end_day, days)
    s3_list = s3.load_data(start_day, end_day, days)
    year = start_day.split('-')[0]
    month = start_day.split('-')[1]
    writeByTemplate(s1_list, s1_se, s2_list, s3_list, year, month, start_day, end_day)


if __name__ == '__main__':
    start, end = TimeUtils.getLastWeekRange()
    days = 7
    main(start, end, days)
