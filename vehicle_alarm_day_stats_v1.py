#!/usr/bin/env python
# -*- coding: utf-8 -*-
from db import CkDB


def insert_select(client, stats_date):
    insert_select_sql = """
        insert into gdispx_data.vehicle_alarm_stats_day(`date`,device_id, alarm_num, need_acc_num, acc_miss_num) 
        select toDate('{stats_date}')
            , a.device_id
            , count() alarm_num 
            , countIf(a.alarm_code in (10400,10401,10402,10410,10413,10412)) need_acc_num
            , countIf(a.alarm_code in (10400,10401,10402,10410,10413,10412) 
                        and (b.alarm_tag = '' or a.alarm_time < addDays(b.create_time,-1))) acc_miss_num
        from (select device_id, alarm_time, alarm_tag, alarm_code from gdispx_data.business_alarm_info 
                where alarm_time >= toDate('{stats_date}') and alarm_time < toDate('{stats_date}')+1) a
        left join (select alarm_tag,create_time from gdispx_data.business_accessories_info
            where create_time >= toDate('{stats_date}') and create_time < toDate('{stats_date}')+2) b  on a.alarm_tag = b.alarm_tag 
        group by a.device_id
    """.format(stats_date=stats_date)
    print(insert_select_sql)
    return client.execute(insert_select_sql, with_column_types=True)


def stats(stats_date):
    ck = CkDB()
    try:
        client = ck.get_client()
        insert_select(client, stats_date)
    finally:
        ck.disconnect()


if __name__ == '__main__':
    stats_date = '2021-10-24'
    stats(stats_date)

