from yyutils import *
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=end_day)
    sql = f'''
    select
        city_id,any(city_name) city_name, 
        count(1) jg_count, sumIf(is_except , vehicle_type= 1) vt1,
        sumIf(is_except , vehicle_type= 3) vt3,
        sumIf(is_except , vehicle_type= 4) vt4,
        sum(is_except) except_count,
        Floor(except_count/jg_count,4) p
    from 
    (
        SELECT 
        A.vehicle_no vehcile_no , vehicle_color ,A.city_id city_id,ba.name city_name,
        vehicle_type ,if(B.vehicle_no ='' , 0,1) is_except,
        dictGetString('gdispx_data.sys_dict_item','label',(toString(A.vehicle_type),'vehicle_type')) vehicle_type_name
        from 
        (SELECT 
        vehicle_no ,vehicle_color ,city_id ,vehicle_type 
        from gdispx_data.v_vehicle_wide_day_all vvwda 
        where inc_day BETWEEN '{start_day}' and '{end_day}'
        group by vehicle_no ,vehicle_color ,city_id ,vehicle_type 
        ) A
        LEFT join 
        
        (SELECT 
        DISTINCT 
        vehicle_no 
        from gdispx_data.business_ai_alarm_info baai 
        where toDate(alarm_time) BETWEEN '{start_day}' and '{end_day}'
        and alarm_code = 10405
        and id not in 
        (
        select 
        alarm_id
        from gdispx_data.business_alarm_audit_info baai2 
        where toDate(create_time) BETWEEN '{start_day}' and '{risk_dispose_end_day}'
        and process_status=2 and dispose_type=7
        )
        ) B on A.vehicle_no = B.vehicle_no
        left join 
        gdispx_data.basics_area ba on ba.id = A.city_id 
    ) C
    group by city_id
    '''
    return sql


def export_outlist_orderDict(inlist):
    outlist = []
    for e in inlist:
        outlist.append(OrderedDict(zip(
            ('pid','city_name','vt1','vt3','vt4','except_count','p'),
            (e['pid'],e['city_name'],e['vt1'],e['vt3'],e['vt4'],e['except_count'],
             FLOORtoPercent(e['p'])
             )
        )))
    return outlist


def doSum(datalist):
    ne = OrderedDict()
    ne['pid'] = '合计'
    ne['city_name'] = ''
    ne['vt1'] = sum([e['vt1'] for e in datalist])
    ne['vt3'] = sum([e['vt3'] for e in datalist])
    ne['vt4'] = sum([e['vt4'] for e in datalist])
    ne['except_count'] = sum([e['except_count'] for e in datalist])
    ne['p'] = FLOORtoPercent(sum([e['jg_count'] for e in datalist])/ ne['except_count'])
    return ne


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = orderByCity(datalist)
    outlist = export_outlist_orderDict(outlist)
    se = doSum(datalist)
    return outlist,se

if __name__ == '__main__':
    '''

        '''
    start, end = TimeUtils.getLastWeekRange()
    load_data(start, end, 7)