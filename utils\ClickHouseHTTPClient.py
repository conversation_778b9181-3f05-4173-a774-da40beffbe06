import requests
import io

# Pandas 是此客户端的硬性依赖
try:
    import pandas as pd
except ImportError:
    # 如果 pandas 未安装，在模块加载时立即抛出错误
    raise ImportError(
        "Pandas library is required for this ClickHouseHTTPClient but is not installed. "
        "Please install it with 'pip install pandas'."
    )

class ClickHouseHTTPClient:
    """
    一个 ClickHouse HTTP 客户端，执行查询并总是返回 Pandas DataFrame。
    默认启用压缩。
    """
    def __init__(self, host='localhost', port=8123, database='default', user=None, password=None, protocol='http'):
        """
        初始化客户端。

        Args:
            host (str): ClickHouse 服务器主机名或 IP 地址。
            port (int): ClickHouse HTTP 接口端口。
            database (str): 默认数据库名称。
            user (str, optional): ClickHouse 用户名。
            password (str, optional): ClickHouse 密码。
            protocol (str): 使用的协议 ('http' 或 'https')。
        """
        if not isinstance(host, str) or not host:
            raise ValueError("Host must be a non-empty string.")
        if not isinstance(port, int) or not (0 < port < 65536):
            raise ValueError("Port must be a valid integer (1-65535).")
        if protocol not in ('http', 'https'):
            raise ValueError("Protocol must be 'http' or 'https'.")

        self.base_url = f"{protocol}://{host}:{port}/"
        self.database = database
        self.auth = None
        self.headers = {
            'Accept-Encoding': 'gzip, deflate', # 客户端接受压缩
        }
        if user:
            if password:
                self.auth = (user, password) # HTTP Basic Auth
            else:
                self.headers['X-ClickHouse-User'] = user
        if password and not user:
            self.headers['X-ClickHouse-Key'] = password

    def execute_query(self, query, query_params=None, clickhouse_settings=None):
        """
        执行给定的 SQL 查询并返回 Pandas DataFrame。

        Args:
            query (str): 要执行的 SQL 查询语句 (不应包含 FORMAT 子句)。
            query_params (dict, optional): 要传递给 ClickHouse 的额外 URL 参数。
                                          'query', 'database', 'default_format', 'enable_http_compression'
                                          会被此方法自动管理或覆盖。
            clickhouse_settings (dict, optional): 要作为 ClickHouse settings 传递的键值对。
                                                例如: {'max_block_size': '10000'}

        Returns:
            pandas.DataFrame: 查询结果。

        Raises:
            requests.exceptions.RequestException: 如果发生网络或 HTTP 错误。
            RuntimeError: 如果 ClickHouse 返回错误信息或无法解析 CSV 为 DataFrame。
            ValueError: 如果输入参数无效。
        """
        # 准备 ClickHouse 参数
        ch_params = {
            'database': self.database,
            'default_format': 'CSVWithNames', # 总是请求带列名的 CSV 以便 DataFrame 正确解析
            'enable_http_compression': 1      # 启用服务端压缩
        }

        if query_params:
            ch_params.update(query_params)

        if clickhouse_settings:
            for key, value in clickhouse_settings.items():
                ch_params[f'param_{key}'] = value

        final_headers = self.headers.copy()

        try:
            response = requests.post(
                self.base_url,
                params=ch_params,
                data=query.encode('utf-8'),
                headers=final_headers,
                auth=self.auth,
                timeout=300000 # 5分钟超时
            )
            response.raise_for_status()

            csv_data = response.text # requests 会自动解压

            if not csv_data.strip(): # 如果ClickHouse返回空结果 (例如，只有表头或完全为空)
                return pd.DataFrame()

            try:
                # 使用 io.StringIO 将 CSV 字符串转换为类文件对象供 pandas 读取
                df = pd.read_csv(io.StringIO(csv_data))
                return df
            except pd.errors.EmptyDataError: # CSV 为空或只有表头，但 pandas 无法解析为数据
                return pd.DataFrame() # 返回空 DataFrame
            except Exception as parse_err: # 其他 pandas 解析错误
                error_detail = (
                    f"Failed to parse CSV data from ClickHouse into DataFrame.\n"
                    f"CSV data (first 500 chars): '{csv_data[:500]}'\n"
                    f"Pandas Error: {parse_err}"
                )
                raise RuntimeError(error_detail) from parse_err

        except requests.exceptions.HTTPError as http_err:
            error_message = http_err.response.text if http_err.response else str(http_err)
            detailed_error = (
                f"ClickHouse HTTP Error: {http_err.response.status_code if http_err.response else 'N/A'} "
                f"Reason: {http_err.response.reason if http_err.response else 'N/A'}\n"
                f"Server Message: {error_message}"
            )
            raise RuntimeError(detailed_error) from http_err
        except requests.exceptions.RequestException as req_err:
            raise RuntimeError(f"Request failed: {req_err}") from req_err

    @staticmethod
    def get237Client():
        host = '**************'
        port = 8123
        user = 'default'
        password = '<EMAIL>'
        database = 'gdispx_data'
        """
        静态方法，用于直接获取 ClickHouseHTTPClient 实例
        
        参数:
            host (str): ClickHouse 服务器地址，默认为 'localhost'
            user (str): 用户名，默认为 'default'
            password (str): 密码，默认为空字符串
            
        返回:
            ClickHouseHTTPClient: 配置好的 ClickHouseHTTPClient 实例
        """
        return ClickHouseHTTPClient(host=host, user=user, password=password,database=database,port=port)

# --- 示例用法 ---
if __name__ == '__main__':
    # 请替换为你的 ClickHouse 服务器信息
    CLICKHOUSE_HOST = 'localhost' # 你的 ClickHouse 服务器 IP/域名
    CLICKHOUSE_USER = 'default'   # 用户名 (如果需要)
    CLICKHOUSE_PASSWORD = ''      # 密码 (如果需要)

    try:
        # 1. 初始化客户端
        client = ClickHouseHTTPClient(
            host=CLICKHOUSE_HOST,
            user=CLICKHOUSE_USER,
            password=CLICKHOUSE_PASSWORD
        )
        print(f"Client initialized for {client.base_url}, database: {client.database}")

        # 2. 执行查询并获取 Pandas DataFrame
        print("\n--- Test 1: Get DataFrame from system.numbers ---")
        test_query_1 = "SELECT number, toString(number) as s, now() as t FROM system.numbers LIMIT 3"
        try:
            df_result_1 = client.execute_query(test_query_1)
            print("DataFrame Result 1:")
            if not df_result_1.empty:
                print(df_result_1)
                df_result_1.info()
            else:
                print("DataFrame is empty.")
        except Exception as e:
            print(f"Error executing query 1: {e}")

        # 3. 示例：带 ClickHouse settings 的查询
        print("\n--- Test 2: Query with ClickHouse settings ---")
        test_query_2 = "SELECT name, value FROM system.settings WHERE name = 'max_query_size'"
        try:
            settings = {'log_queries': '0'} # 示例 setting
            df_result_2 = client.execute_query(test_query_2, clickhouse_settings=settings)
            print("DataFrame Result 2 (with settings):")
            if not df_result_2.empty:
                print(df_result_2)
            else:
                print("DataFrame is empty (or setting not found).")
        except Exception as e:
            print(f"Error executing query 2: {e}")

        # 4. 示例：查询一个返回空结果集的查询
        print("\n--- Test 3: Query with empty result set ---")
        test_query_3 = "SELECT * FROM system.numbers WHERE number < 0 LIMIT 5" # 这通常返回空
        try:
            df_result_3 = client.execute_query(test_query_3)
            print("DataFrame Result 3 (empty query):")
            if df_result_3.empty:
                print("DataFrame is correctly empty.")
                df_result_3.info() # 应该显示0行
            else:
                print("DataFrame was not empty, unexpected:")
                print(df_result_3)
        except Exception as e:
            print(f"Error executing query 3: {e}")


        # 5. 示例：查询不存在的表以测试错误处理
        print("\n--- Test 4: Query non-existent table ---")
        test_query_4 = "SELECT * FROM this_table_does_not_exist_hopefully"
        try:
            client.execute_query(test_query_4)
        except RuntimeError as e: # 我们期望从 execute_query 中捕获 RuntimeError
            print(f"Caught expected error for non-existent table:\n{e}")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

    except ValueError as ve: # 来自 __init__ 的配置错误
        print(f"Configuration Error: {ve}")
    except RuntimeError as re: # 来自 execute_query 的运行时错误
        print(f"Runtime Error during client operation: {re}")
    except ImportError as ie: # Pandas 未安装
        print(f"Import Error: {ie}")
    except Exception as e: # 其他意外错误
        print(f"An unexpected error occurred in the main block: {e}")
