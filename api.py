#!/usr/bin/env python
# -*- coding: utf-8 -*-
import datetime
from fastapi import FastAPI
from fastapi.responses import FileResponse
import os

app = FastAPI()


@app.get("/")
def hello():
    now = datetime.datetime.now()
    return {"Hello World": now}


@app.get("/hello")
def test():
    return "hello World!"


@app.get("/operatReport/{date}")
def report(date: str):
    file_path = 'report/{}.zip'.format(date)
    if os.path.exists(file_path):
        return FileResponse(path=file_path, filename='{}.zip'.format(date))
    return '该日期没有报表生成'
