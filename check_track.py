#!/usr/bin/env python
# -*- coding: utf-8 -*-
from multiprocessing import Process
from optparse import OptionParser
import sys
import math
import datetime
from cal_old import cal
from db import *


def read_imei(imei_file):
    imeis = []
    with open(imei_file, 'r') as f:
        for imei in f.readlines():
            imeis.append(imei.rstrip("\n"))
    return imeis


def read_imei_from_db(ck_client):
    imeis = []
    ret = ck_client.execute('SELECT master_sim_code from gdispx_data.v_regulator_vehicle_related_infos')
    for items in ret:
        imeis.append(items[0])
    return imeis


def now_time():
    return datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d %H:%M:%S')


GPS_TIME_FMT = '%y%m%d%H%M%S'
CDATE_TIME_FMT = '%Y-%m-%d %H:%M:%S'


def query_track_infos(session, imei, day):
    rows = session.execute(query="select cdate, gps_time, latitude, longitude, speed "
                                 "from dhiot.gps_data_{} where imei = '{}'".format(day, imei))
    return list(rows)


def check(imei, track_points):
    invalid_track_num = 0
    for track in track_points:
        cdate, gps_time, latitude, longitude, speed = track.cdate, track.gps_time, track.latitude, track.longitude, track.speed
        cdate = cdate + datetime.timedelta(hours=8)
        try:
            gps_time = datetime.datetime.strptime(gps_time, GPS_TIME_FMT)
        except:
            print('gps_time:{}'.format(gps_time))
            invalid_track_num += 1
            continue
        if cdate < gps_time:
            print('cdate:{}, gps_time:{}'.format(cdate, gps_time))
            invalid_track_num += 1
            continue
        latitude = latitude / 1000000
        if latitude < 3.51 or latitude > 53.33:
            print('latitude:{}'.format(latitude))
            invalid_track_num += 1
            continue
        longitude = longitude / 1000000
        if longitude < 73.33 or longitude > 135.05:
            print('longitude:{}'.format(longitude))
            invalid_track_num += 1
            continue
        if speed < 0 or speed > 1600:
            print('speed:{}'.format(speed))
            invalid_track_num += 1
            continue
    return invalid_track_num


# 轨迹总条数，合格轨迹数，涉及到的车辆总数，上线车辆数
def run(imeis, day):
    cassandra_db = CassandraDB()
    try:
        cassandra_session = cassandra_db.get_session()
        track_num_total = 0
        invalid_track_num_total = 0
        invalid_imei_num = 0
        no_track_imei_num = 0
        error_imei_num = 0
        for imei in imeis:
            imei = imei.strip()
            try:
                # 获取轨迹信息列表
                track_points = query_track_infos(cassandra_session, imei, day)
                if len(track_points) == 0:
                    print('设备号%s无轨迹' % imei)
                    no_track_imei_num += 1
                    continue
                # 执行计算
                invalid_track_num = check(imei, track_points)
                track_num_total += len(track_points)
                invalid_track_num_total += invalid_track_num
                if invalid_track_num == len(track_points):
                    print('设备号%s轨迹全部无效' % imei)
                    invalid_imei_num += 1
                elif invalid_track_num > 0:
                    print('设备号%s存在无效轨迹' % imei)
            except Exception as e:
                error_imei_num += 1
                print('设备号%s计算异常' % imei)
                print(e)
        print('轨迹总条数:{},合格轨迹数:{},涉及到的车辆总数:{},计算异常车辆数:{},无设备号车辆数:{},轨迹全部无效车辆数:{},上线车辆数:{}'.format(
            track_num_total,
            track_num_total - invalid_track_num_total,
            len(imeis),
            error_imei_num,
            no_track_imei_num,
            invalid_imei_num,
            len(imeis) - invalid_imei_num - no_track_imei_num))
    finally:
        cassandra_db.shutdown()


def partition(big_list, size):
    """将大的list均分"""
    big_list_size = len(big_list)
    sub_list_list = []
    start = 0
    for i in range(size):
        sub_size = math.ceil((big_list_size - start) / (size - i))
        sub_list_list.append(big_list[start:start + sub_size])
        start += sub_size
        if start >= big_list_size:
            break
    return sub_list_list


def get_user_paras():
    try:
        opt = OptionParser()
        opt.add_option('--imei_file',
                       dest="imei_file",
                       type=str,
                       help="the file of imei list")
        opt.add_option('--day',
                       dest='day',
                       type=str,
                       help='the statistic day, eg: 210801')
        opt.add_option('--thread_num',
                       dest="thread_num",
                       type=int,
                       default=8,
                       help="parallel thread num , default 8")
        (options, args) = opt.parse_args()
        is_valid_paras = True
        error_messages = []
        imei_file = options.imei_file
        day = options.day
        thread_num = options.thread_num
        # if not imei_file:
        #     error_messages.append("imei_file must be set;")
        #     is_valid_paras = False
        if not day:
            error_messages.append("day must be set;")
            is_valid_paras = False

        if is_valid_paras:
            user_paras = {"imei_file": imei_file, "day": day, "thread_num": thread_num}
            return user_paras
        else:
            for error_message in error_messages:
                print(error_message)
            opt.print_help()
            return None
    except Exception as ex:
        print("exception :{0}".format(str(ex)))
        return None


def main(user_paras):
    imei_file = user_paras['imei_file']
    day = user_paras['day']
    thread_num = user_paras['thread_num']
    print('{}: begin execute imei_file={}, day={}, thread_num={}'.format(now_time(), imei_file, day, thread_num))
    # 1.加载设备号
    if imei_file:
        imei_list = read_imei(imei_file)
    else:
        ck_db = CkDB()
        try:
            ck_client = ck_db.get_client()
            imei_list = read_imei_from_db(ck_client)
        finally:
            ck_db.disconnect()
    # 2.根据进程数平均分配
    process_list = []
    for sub_imei_list in partition(imei_list, thread_num):
        process_list.append(Process(target=run, args=(sub_imei_list, day,)))
    # 3.启动进程
    for p in process_list:
        p.start()
    for p in process_list:
        p.join()
    print('{}: finish execute imei_file={}, day={}, thread_num={}'.format(now_time(), imei_file, day, thread_num))


if __name__ == '__main__':
    # 设备号文件, 日期, 线程数
    user_paras = get_user_paras()
    if user_paras is None:
        sys.exit(0)
    main(user_paras)

