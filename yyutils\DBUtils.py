from cassandra.cluster import Cluster
from cassandra.auth import PlainTextAuthProvider
from clickhouse_driver import Client
import pymysql
from enum import Enum
import yaml
import os
from yyutils.FileUtil import write_csv


def loadYaml():
    cur_path = os.path.split(os.path.realpath(__file__))[0]
    with open(os.path.join(cur_path, 'db.yaml'), 'r', encoding='utf-8') as f:
        file_content = f.read()
    content = yaml.load(file_content, yaml.FullLoader)
    return content


class DBInfo(Enum):
    CK_PROD = 'ck_db_prod'
    CK_PROD_ALARM = 'ck_db_alarm'
    CK_PROD_204 = 'ck_db_prod_204'
    CK_BETA = 'ck_db_beta'
    CK_TEST = 'ck_db_test'
    MySQL_PROD = 'mysql_db_prod'
    SCYLLA_PROD = 'scylla_db_prod'
    HWY_MySQL_TEST = 'hwy_mysql_test'

class DBType(Enum):
    MYSQL = 'mysql'
    SCYLLA = 'scylla'
    CLICKHOUSE = 'click-house'


class DBUtil(object):
    def __init__(self, dbtype, db=None):
        self.dbtype = dbtype
        self.db = None

        self.getDBInfo(db)
        self.conn = None
        self.getConnector()
        self.client = self.getClient()

    def getDBInfo(self, db_info):
        if self.dbtype not in (DBType.MYSQL.value, DBType.CLICKHOUSE.value, DBType.SCYLLA.value):
            raise RuntimeError(f'当前DB工具未支持 {self.dbtype} 数据库')
        if isinstance(db_info, dict):
            self.db = db_info
            self.checkDBInfo()
            return
        dbs = loadYaml()
        if self.dbtype == DBType.MYSQL.value:
            if not db_info:
                self.db = dbs.get(DBInfo.MySQL_PROD.value)
            else:
                self.db = dbs.get(db_info)
        elif self.dbtype == DBType.CLICKHOUSE.value:
            if not db_info:
                self.db = dbs.get(DBInfo.CK_PROD.value)
            else:
                self.db = dbs.get(db_info)
        elif self.dbtype == DBType.SCYLLA.value:
            if not db_info:
                self.db = dbs.get(DBInfo.SCYLLA_PROD.value)
            else:
                self.db = dbs.get(db_info)
        else:
            raise RuntimeError('未指定数据库类型')
        if not self.db:
            raise RuntimeError(f'无法从 db.yaml 配置中读取{db_info}数据库信息')
        self.checkDBInfo()

    def checkDBInfo(self):
        if self.dbtype == DBType.MYSQL.value or self.dbtype == DBType.CLICKHOUSE.value:
            keys = set(self.db.keys())
            db_key_set = {'host', 'port', 'user', 'password', 'database'}
            if db_key_set - keys:
                raise RuntimeError(f'{self.dbtype}数据库连接信息缺少必要配置{" ".join(db_key_set - keys)}')
        else:
            keys = set(self.db.keys())
            db_key_set = {'hosts', 'user', 'password'}
            if db_key_set - keys:
                raise RuntimeError(f'{self.dbtype}数据库连接信息缺少必要配置{" ".join(db_key_set - keys)}')

    def getConnector(self):
        if self.dbtype == DBType.MYSQL.value:
            self.conn = pymysql.connect(db=self.db['database'],
                                        user=self.db['user'],
                                        password=self.db['password'],
                                        host=self.db['host'],
                                        port=int(self.db['port']))
        elif self.dbtype == DBType.CLICKHOUSE.value:
            self.conn = Client(database=self.db['database'],
                               user=self.db['user'],
                               password=self.db['password'],
                               host=self.db['host'],
                               port=int(self.db['port']),
                               send_receive_timeout=100)
        else:
            self.conn = Cluster(contact_points=self.db['hosts'].split(','),
                                auth_provider=PlainTextAuthProvider(username=self.db['user'],
                                                                    password=self.db['password']))

    def getClient(self):
        if self.dbtype == DBType.MYSQL.value:
            return self.conn.cursor(pymysql.cursors.DictCursor)
        elif self.dbtype == DBType.CLICKHOUSE.value:
            return self.conn
        else:
            return self.conn.connect()

    def excute(self, sql='', params=None):
        if not self.client:
            self.client = self.getClient()
        if self.dbtype == DBType.MYSQL.value:
            self.client.execute(sql)
        elif self.dbtype == DBType.CLICKHOUSE.value:
            self.client.execute(sql, params=params)
        else:
            self.client.execute(query=sql)

    def excutemany(self,sql='',params=None):
        if not self.client:
            self.client = self.getClient()
        if self.dbtype == DBType.MYSQL.value:
            self.client.executemany(sql,params)
            self.conn.commit()


    def query(self, sql=''):
        if not self.client:
            self.client = self.getClient()
        if self.dbtype == DBType.MYSQL.value:
            self.client.execute(sql)
            return self.client.fetchall()
        elif self.dbtype == DBType.CLICKHOUSE.value:
            return self.client.execute(sql, with_column_types=True)
        else:
            return self.client.execute(query=sql)

    def query_to_dictList(self, sql, h=[]):
        outlist = []
        results = self.query(sql)
        if self.dbtype == DBType.MYSQL.value:
            return self.query(sql)
        elif self.dbtype == DBType.CLICKHOUSE.value:
            if not h:
                h = [col[0] for col in results[1]]
            for row in results[0]:
                e = dict(zip(h, list(row)))
                outlist.append(e)
        else:
            pass
        return outlist

    def query_to_csv(self, sql, otfile, h=[], col_mapping={}):
        outlist = self.query_to_dictList(sql)
        if not outlist:
            return
        if not h:
            h = list(outlist[0].keys())
        if not col_mapping:
            write_csv(otfile, h, outlist)
            return
        outlist1 = []
        for e in outlist:
            ne = {}
            for k, v in e.items():
                if col_mapping.get(k):
                    ne[col_mapping.get(k)] = v
                else:
                    if k in h:
                        ne[k] = v
            outlist1.append(ne)
        write_csv(otfile, h, outlist1)

    def close(self):
        if self.dbtype == DBType.MYSQL.value:
            self.conn.close()
        elif self.dbtype == DBType.CLICKHOUSE.value:
            self.conn.disconnect()
        else:
            self.conn.shutdown()


if __name__ == '__main__':
    print(loadYaml())
