from yyutils import *
from utils.config import orderByCity, FLOORtoPercent, bpath, do_rank_by_col, FLOORCUT,orderByCityVT
from collections import OrderedDict


def loadSQL(start_day, end_day):
    risk_dispose_end_day = TimeUtils.DeltaDay(12, cur_day=end_day)
    xjg_sql = f'''
                SELECT 
                    count(distinct vehicle_no,vehicle_color) xjg_count,
                    toUInt32(vehicle_type) vehicle_type,
                    toUInt32(owner_id) owner_id
                from gdispx_data.vehicle_history_yunzheng vhy 
                where create_date BETWEEN '{start_day}'  and '{end_day}'
                group by owner_id,vehicle_type
    '''
    jg_sql = f'''
        select 
        toUInt32(owner_id) owner_id  ,toUInt32(vehicle_type) vehicle_type,
        count(distinct vehicle_no) jg_count,
        any(city_id) city_id,
        any(area_id) area_id,
        any(owner_name) owner_name
        from gdispx_data.v_vehicle_wide_day_all vvwda 
        where inc_day BETWEEN '{start_day}'  and '{end_day}'
        group by owner_id  ,vehicle_type
    '''

    covered_sql = f'''
        select 
            toUInt32(owner_id) owner_id  ,toUInt32(vehicle_type) vehicle_type,
            count(1) except_count
        from 
        (
        SELECT 
            vehicle_no ,
            any(vehicle_type) vehicle_type,
            any(owner_id) owner_id ,
            count(distinct toDate(alarm_time) ) except_days
            from gdispx_data.business_ai_alarm_info baai 
            where toDate(alarm_time) BETWEEN '{start_day}' and '{end_day}'
            and alarm_code = 10405
            and id not in 
            (
            select 
            alarm_id
            from gdispx_data.business_alarm_audit_info baai2 
            where toDate(create_time) BETWEEN '{start_day}' and '{risk_dispose_end_day}'
            and process_status=2 and dispose_type=7
            )
        group by vehicle_no
        ) 
        group by owner_id , vehicle_type
    '''
    sql = SQLHelper().table(covered_sql,'C') \
        .leftJoin().table(xjg_sql,'B').on('C.owner_id = B.owner_id and C.vehicle_type=B.vehicle_type') \
        .leftJoin().table(jg_sql,'A').on('A.owner_id=C.owner_id and A.vehicle_type=C.vehicle_type') \
        .leftJoin().table('gdispx_data.basics_area','D').on('D.id = A.area_id') \
        .select(
        f'''
            A.owner_id owner_id,
            A.vehicle_type vehicle_type,
            dictGetString('gdispx_data.sys_dict_item','label',(toString(A.vehicle_type),'vehicle_type')) vehicle_type_name,
            A.owner_name owner_name,
            D.city_id city_id,
            D.id area_id,            
            D.city_short city_name,
            D.name area_name,
            B.xjg_count xjg_count,
            A.jg_count jg_count,
            C.except_count except_count,
            Floor(except_count/jg_count,4) p
        '''
    )
    return sql

def getTop10(datalist):
    city_g = list_2_dictlist(datalist,'city_id')
    outlist = []
    for _,g in city_g.items():
        vt_g = list_2_dictlist(g,'vehicle_type')
        for vt,sg in vt_g.items():
            sg.sort(key=lambda o: - o['p'])
            if len(sg) > 10:
                tmplist = list(filter(lambda o:o['p']>= sg[9]['p'],sg))
            else:
                tmplist = sg[:10]
            outlist.extend(tmplist)
    outlist1 = []
    for e in outlist:
        ne = OrderedDict(zip([
            'city_name','area_name','owner_name',
            'vehicle_type_name','xjg_count','jg_count','except_count','p','vehicle_type'
        ],[
            e['city_name'],e['area_name'],e['owner_name'],
            e['vehicle_type_name'],
            e['xjg_count'],e['jg_count'],
            e['except_count'] , FLOORtoPercent(e['p']),e['vehicle_type']
        ]))
        outlist1.append(ne)
    outlist = orderByCityVT(outlist1,pid=False)
    for e in outlist:
        e.pop('vehicle_type')
    return outlist


def load_data(start_day, end_day, days):
    final_sql = loadSQL(start_day, end_day)
    db = DBUtil(DBType.CLICKHOUSE.value)
    datalist = db.query_to_dictList(final_sql)
    db.close()
    outlist = getTop10(datalist)
    return outlist



if __name__ == '__main__':
    '''
            '''
    start, end = TimeUtils.getLastWeekRange()
    load_data(start, end, 7)