from yyutils import *
from utils.YYSQLTemplate import YY<PERSON><PERSON>Template
from utils.config import orderByCity, FLOORtoPercent, bpath
from collections import OrderedDict


def loadSQL(start_day, end_day, days):
    avg_drift_sql = YYSQLTemplate.calTrackAvgDriftPoints(start_day, end_day)

    xjg_sql = YYSQLTemplate.xjg_stats([],
                                      "city_id",
                                      '''
                                      toInt32(city_id) city_id,
                                      count(distinct vehicle_no,vehicle_color) xjg_count
                                      ''',
                                      start_day, end_day)

    wide_sql = YYSQLTemplate.wide_stats(
        [],
        'city_id ,vehicle_no ,vehicle_color',
        '''
        city_id,
        any(city_short_name) city_name,
        vehicle_no,
        vehicle_color,
        if(max(online) =1,1,0 ) is_online,
        if(min(never_gps) +  min(never_alarm) + min(never_acc) > 0 , 0 , 1) is_ok,
        if(sum(drift_point_count) > avg_drift_point_count, 1, 0) is_high_drift,
        sum(total_mileage) total_mileage,
        sum(continuous_mileage) continuous_mileage,
        sum(accessoriesNum) accessoriesNum,
        sum(accessoriesonTimeNum) accessoriesonTimeNum,
        sum(risk_oneLevel) + sum(risk_twoLevel) + sum(risk_threeLevel) riskNum
        ''',
        start_day=start_day, end_day=end_day
    )
    jg_sql = SQLHelper().table(wide_sql) \
        .groupBy('city_id') \
        .select('''
            city_id,
            any(city_name) city_name,
            count(1) jg_count,
            sum(is_online) online_count,
            sum(is_ok) ok_count,
            sum(is_high_drift) drift_count,
            sum(total_mileage) total_mileage,
            sum(continuous_mileage) continuous_mileage,
            sum(accessoriesNum) accessoriesNum,
            sum(accessoriesonTimeNum) accessoriesonTimeNum,
            sum(riskNum) riskNum
        ''')
    final_sql = SQLHelper().table(jg_sql, 'B') \
        .leftJoin().table(xjg_sql, 'A').on('A.city_id=B.city_id').select(
        f'''
            A.city_id city_id,
            city_name,
            if(xjg_count>jg_count,xjg_count,jg_count) xjg_count,
            jg_count,
            online_count,
            ok_count,
            jg_count - ok_count ng_count,
            riskNum,
            drift_count,
            total_mileage,
            continuous_mileage,
            accessoriesNum,
            accessoriesonTimeNum,
            FLOOR(jg_count/xjg_count,4) jg_rate,
            FLOOR(online_count/jg_count,4) online_rate,
            FLOOR(drift_count/online_count,4) drift_rate,
            FLOOR(continuous_mileage/total_mileage,4) track_rate,
            FLOOR(ok_count/jg_count,4) ok_rate,
            FLOOR((accessoriesNum - accessoriesonTimeNum)/accessoriesNum,4) acc_rate,
            FLOOR(riskNum / online_count / {days}, 4 ) risk_rate
        '''
    )
    return final_sql, avg_drift_sql


def do_rank(inlist):
    inlist.sort(key=lambda o: -o['jg_rate'])
    for i in range(len(inlist)):
        inlist[i]['jg_rank'] = i + 1
    inlist.sort(key=lambda o: -o['online_rate'])
    for i in range(len(inlist)):
        inlist[i]['online_rank'] = i + 1
    inlist.sort(key=lambda o: o['drift_rate'])
    for i in range(len(inlist)):
        inlist[i]['drift_rank'] = i + 1
    inlist.sort(key=lambda o: -o['track_rate'])
    for i in range(len(inlist)):
        inlist[i]['track_rank'] = i + 1
    inlist.sort(key=lambda o: -o['ok_rate'])
    for i in range(len(inlist)):
        inlist[i]['ok_rank'] = i + 1
    inlist.sort(key=lambda o: o['acc_rate'])
    for i in range(len(inlist)):
        inlist[i]['acc_rank'] = i + 1
    inlist.sort(key=lambda o: o['risk_rate'])
    for i in range(len(inlist)):
        inlist[i]['risk_rank'] = i + 1
    inlist.sort(key=lambda o: o['pid'])


def export_outlist_orderDict(inlist):
    do_rank(inlist)
    outlist = []
    for e in inlist:
        e['jg_rate'] = f"{round(e['jg_rate'] * 100, 2)}%"
        e['online_rate'] = f"{round(e['online_rate'] * 100, 2)}%"
        e['drift_rate'] = f"{round(e['drift_rate'] * 100, 2)}%"
        e['track_rate'] = f"{round(e['track_rate'] * 100, 2)}%"
        e['ok_rate'] = f"{round(e['ok_rate'] * 100, 2)}%"
        e['acc_rate'] = f"{round(e['acc_rate'] * 100, 2)}%"
        ne = OrderedDict(zip(
            ('pid', 'city_name', 'xjg_count', 'jg_count', 'online_count',
             'ng_count', 'jg_rate', 'jg_rank', 'online_rate', 'online_rank',
             'drift_rate', 'drift_rank', 'track_rate', 'track_rank',
             'ok_rate', 'ok_rank', 'acc_rate', 'acc_rank',
             'risk_rate', 'risk_rank'),
            (e['pid'], e['city_name'], e['xjg_count'], e['jg_count'], e['online_count'],
             e['ng_count'], e['jg_rate'], e['jg_rank'], e['online_rate'], e['online_rank'],
             e['drift_rate'], e['drift_rank'], e['track_rate'], e['track_rank'], e['ok_rate'],
             e['ok_rank'], e['acc_rate'], e['acc_rank'], e['risk_rate'], e['risk_rank'])
        ))
        outlist.append(ne)

    return outlist


def doSum(inlist, days):
    total_xjg = sum([e['xjg_count'] for e in inlist])
    total_jg = sum([e['jg_count'] for e in inlist])
    total_online = sum([e['online_count'] for e in inlist])
    total_ok = sum([e['ok_count'] for e in inlist])
    total_drift = sum([e['drift_count'] for e in inlist])
    total_mileage = sum([e['total_mileage'] for e in inlist])
    continuous_mileage = sum([e['continuous_mileage'] for e in inlist])
    accessoriesNum = sum([e['accessoriesNum'] for e in inlist])
    accessoriesonTimeNum = sum([e['accessoriesonTimeNum'] for e in inlist])
    risk_count = sum([e['riskNum'] for e in inlist])
    ne = OrderedDict(zip(
        ('pid', 'city_name', 'xjg_count', 'jg_count', 'online_count',
         'ng_count', 'jg_rate', 'jg_rank', 'online_rate', 'online_rank',
         'drift_rate', 'drift_rank', 'track_rate', 'track_rank',
         'ok_rate', 'ok_rank', 'acc_rate', 'acc_rank',
         'risk_rate', 'risk_rank'),
        ('全省平均', '-', total_xjg, total_jg, total_online,
         total_jg - total_ok,
         FLOORtoPercent(total_jg / total_xjg), '/',
         FLOORtoPercent(total_online / total_jg), '/',
         FLOORtoPercent(total_drift / total_online), '/',
         FLOORtoPercent(continuous_mileage / total_mileage), '/',
         FLOORtoPercent(total_ok / total_jg), '/',
         FLOORtoPercent((accessoriesNum - accessoriesonTimeNum) / accessoriesNum), '/',
         round(risk_count / days / total_online,4), '/'
         )
    ))
    return ne


def load_data(start_day, end_day, days):
    final_sql, avg_drift_sql = loadSQL(start_day, end_day, days)
    db = DBUtil(DBType.CLICKHOUSE.value)
    avg_drift_list = db.query_to_dictList(avg_drift_sql)
    avg_drift_point_count = avg_drift_list[0]['average_drift_num']
    xjg_jg_sql = final_sql.replace('avg_drift_point_count', str(avg_drift_point_count))
    xjg_jg_list = db.query_to_dictList(xjg_jg_sql)
    db.close()
    outlist = orderByCity(xjg_jg_list)
    se = doSum(xjg_jg_list,days)
    outlist = export_outlist_orderDict(outlist)
    # h = list(outlist[0].keys())
    # otfile = join_path(bpath, 'test.csv')
    # outlist.append(se)
    # write_csv(otfile, h, outlist)
    return outlist,se


if __name__ == '__main__':
    '''
    
    '''
    start, end, days = TimeUtils.getLastMonthRange()
    load_data(start, end, days)
