import os
from concurrent.futures import <PERSON>PoolExecutor
import multiprocessing
from yyutils.FileUtil import readDir, read_by_trunk
import math


class ProcessRunner(object):
    def __init__(self, core_fun, reduce_args=None, reduce_fun=None, params=None, has_lock=False, pro_count=10):
        self.core_fun = core_fun
        self.reduce_fun = reduce_fun
        self.reduce_args = reduce_args
        self.pro_count = pro_count
        self.lock = None
        self.task_count = 0
        if params:
            self.params = params
        else:
            self.params = []
        self.lock = None
        if has_lock:
            self.lock = multiprocessing.Manager().Lock()

    @staticmethod
    def acquire(lock):
        lock.acquire()

    @staticmethod
    def release(lock):
        lock.release()

    def create_params_by_inpath(self, inpath, *args):
        process_id = 0
        for _ in readDir(inpath):
            self.task_count += 1

        for infile, fn in readDir(inpath, return_mode=2):
            process_id += 1
            log_info = f'进程{process_id} / {self.task_count} '
            if not self.lock:
                self.params.append((infile, fn, *args, process_id, log_info))
            else:
                self.params.append((infile, fn, *args, process_id, log_info, self.lock))

    def create_params_by_inlist(self, inlist, trunk_size=1000, *args):
        process_id = 0
        self.task_count = math.ceil(len(inlist) / trunk_size)
        for g in read_by_trunk(inlist, trunk_size):
            process_id += 1
            log_info = f'进程{process_id} / {self.task_count} '
            if not self.lock:
                self.params.append((g, *args, process_id, log_info))
            else:
                self.params.append((g, *args, process_id, log_info, self.lock))

    def run(self):
        if not self.params:
            raise RuntimeError(f'多进程任务参数为空!')
        with ProcessPoolExecutor(max_workers=min(os.cpu_count(), len(self.params), self.pro_count)) as executor:
            for r in executor.map(self.core_fun, self.params):
                if self.reduce_fun:
                    self.reduce_fun(r, self.reduce_args)
                else:
                    pass


def core_fun(args):
    for i in args:
        print('------', i)
    return args


def reduce_fun(r, outlist):
    outlist.extend(r)


def test():
    params = [[1, 2], [3, 4]]
    outlist = []
    runner = ProcessRunner(params, core_fun, outlist, reduce_fun)
    runner.run()
    print(outlist)


if __name__ == '__main__':
    test()
